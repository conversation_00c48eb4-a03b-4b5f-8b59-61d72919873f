# Coupon Management System Documentation

## Overview

This document describes the complete CRUD (Create, Read, Update, Delete) functionality for the coupon management system in the multi-vendor ecommerce platform. The system follows established patterns from the blog module and includes multi-vendor support with Middle East market considerations.

## Architecture

The coupon system follows the established MVC pattern with the following components:

### Core Components

1. **Model**: `App\Models\Coupon` - Eloquent model with relationships and business logic
2. **Controller**: `App\Http\Controllers\Admin\CouponController` - Handles HTTP requests and responses
3. **Service**: `App\Services\CouponService` - Contains business logic and data processing
4. **Form Requests**: 
   - `App\Http\Requests\StoreCouponRequest` - Validation for creating coupons
   - `App\Http\Requests\UpdateCouponRequest` - Validation for updating coupons
5. **Resources**: 
   - `App\Http\Resources\CouponResource` - API response formatting
   - `App\Http\Resources\CouponCollection` - Collection response formatting
6. **Policy**: `App\Policies\CouponPolicy` - Authorization logic

### Database Schema

The coupons table includes the following key fields:

```sql
- id (primary key)
- code (unique coupon code)
- title (English title)
- title_ar (Arabic title)
- description (English description)
- description_ar (Arabic description)
- type (percentage/fixed)
- value (discount amount)
- min_order_value (minimum order requirement)
- usage_limit (total usage limit)
- per_user_limit (per user usage limit)
- vendor_id (nullable - for vendor-specific coupons)
- user_id (creator)
- start_date (activation date)
- end_date (expiry date)
- is_active (status)
- timestamps
```

## Features

### Multi-Vendor Support

1. **Platform-wide Coupons**: Created by admins, applicable to all vendors
2. **Vendor-specific Coupons**: Created by vendors or admins, applicable only to specific vendor's products
3. **Authorization**: Role-based access control using Spatie Laravel Permission

### Middle East Market Features

1. **Multi-language Support**: 
   - English and Arabic titles/descriptions
   - Localized content retrieval methods
   - RTL-friendly field structure

2. **Currency Handling**:
   - AED currency formatting
   - Proper decimal precision for monetary values
   - Regional number formatting

3. **Regional Compliance**:
   - Validation rules suitable for Middle East market
   - Business logic for discount limitations

### Coupon Types

1. **Percentage Discounts**: e.g., 10% off
2. **Fixed Amount Discounts**: e.g., AED 50 off

### Validation Features

1. **Business Logic Validation**:
   - Percentage discounts cannot exceed 100%
   - Fixed discounts cannot exceed reasonable limits
   - Date range validation (max 365 days validity)
   - Usage limits validation

2. **Security Features**:
   - Unique coupon codes
   - Uppercase code normalization
   - Authorization checks for all operations

## API Endpoints

### Admin Routes (Protected)

```
GET    /api/admin/coupons                    - List all coupons
POST   /api/admin/coupons                    - Create new coupon
GET    /api/admin/coupons/{id}               - Show specific coupon
PUT    /api/admin/coupons/{id}               - Update coupon
DELETE /api/admin/coupons/{id}               - Delete coupon

GET    /api/admin/coupons/vendor/{vendorId}  - Get vendor-specific coupons
GET    /api/admin/coupons/platform/all       - Get platform-wide coupons
POST   /api/admin/coupons/validate           - Validate coupon code
GET    /api/admin/coupons/statistics         - Get coupon statistics
PATCH  /api/admin/coupons/{id}/toggle-status - Toggle coupon status
```

### Public Routes

```
POST   /api/public/coupons/validate          - Validate coupon code (rate limited)
```

## Usage Examples

### Creating a Coupon

```json
POST /api/admin/coupons
{
    "code": "SAVE20",
    "title": "20% Off Everything",
    "title_ar": "خصم 20% على كل شيء",
    "description": "Get 20% off on all products",
    "description_ar": "احصل على خصم 20% على جميع المنتجات",
    "type": "percentage",
    "value": 20,
    "min_order_value": 100,
    "usage_limit": 1000,
    "per_user_limit": 1,
    "vendor_id": null,
    "start_date": "2024-01-01 00:00:00",
    "end_date": "2024-12-31 23:59:59",
    "is_active": true
}
```

### Validating a Coupon

```json
POST /api/admin/coupons/validate
{
    "code": "SAVE20",
    "vendor_id": 1
}
```

## Authorization Matrix

| Role | View All | Create | Update Own | Update All | Delete Own | Delete All | Platform Coupons |
|------|----------|--------|------------|------------|------------|------------|------------------|
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Vendor | Own + Platform | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |

## Testing Recommendations

### Unit Tests

1. **Model Tests**:
   - Test coupon validation methods
   - Test discount calculation logic
   - Test scope methods
   - Test localization methods

2. **Service Tests**:
   - Test CRUD operations
   - Test authorization scoping
   - Test filtering and searching
   - Test statistics calculation

3. **Policy Tests**:
   - Test authorization for different roles
   - Test vendor-specific access
   - Test platform-wide coupon permissions

### Integration Tests

1. **API Tests**:
   - Test all endpoints with different user roles
   - Test validation error responses
   - Test successful operations
   - Test rate limiting on public endpoints

2. **Feature Tests**:
   - Test complete coupon lifecycle
   - Test multi-vendor scenarios
   - Test Arabic content handling
   - Test currency formatting

### Manual Testing Scenarios

1. **Admin User**:
   - Create platform-wide coupons
   - Manage all vendor coupons
   - View comprehensive statistics

2. **Vendor User**:
   - Create vendor-specific coupons
   - Cannot access other vendors' coupons
   - Cannot create platform-wide coupons

3. **Multi-language**:
   - Test Arabic content display
   - Test localization methods
   - Test RTL compatibility

## Implementation Notes

### TODO Items

1. **User-Vendor Relationship**: The `getUserVendorId()` method in both `CouponService` and `CouponPolicy` needs to be implemented based on your specific user-vendor relationship structure.

2. **Usage Tracking**: The `usage_count` attribute currently returns 0. You'll need to implement actual usage tracking by creating a coupon_usages table or tracking through orders.

3. **Permissions Seeding**: Add coupon-related permissions to your `RolePermissionSeeder`:
   ```php
   'coupons' => [
       'browse_coupons', 'view_coupon', 'add_coupon', 
       'edit_coupon', 'delete_coupon', 'manage_platform_coupons'
   ]
   ```

### Migration Command

Run the migration to create the coupons table:
```bash
php artisan migrate
```

### Policy Registration

The `CouponPolicy` is automatically discovered by Laravel based on naming conventions. If you need to manually register it, you can do so in the `AppServiceProvider` or create an `AuthServiceProvider`:

```php
// In AppServiceProvider::boot() method
Gate::policy(Coupon::class, CouponPolicy::class);
```

## Conclusion

The coupon system provides a comprehensive, secure, and scalable solution for managing discounts in a multi-vendor environment. It follows established patterns, includes proper authorization, and supports Middle East market requirements with multi-language support and regional currency formatting.
