# Dropdown Module Documentation

## Overview

The Dropdown module follows the exact same pattern as ProductAttribute and ProductAttributeValue, providing a clean, structured way to manage dropdown data with parent-child relationships.

## Database Structure

### Dropdowns Table
- `id` - Primary key
- `name_en` - English name (e.g., "Color")
- `name_ar` - Arabic name (e.g., "اللون")
- `slug` - Unique slug for the dropdown
- `timestamps` - Created/updated timestamps

### DropdownOptions Table
- `id` - Primary key
- `dropdown_id` - Foreign key to dropdowns table (required)
- `value_en` - English option value (required)
- `value_ar` - Arabic option value
- `timestamps` - Created/updated timestamps

## API Endpoints

### Dropdown Management (NEW)

#### List Dropdowns
```
GET /api/admin/dropdowns
```

#### Create Dropdown
```
POST /api/admin/dropdowns
Content-Type: application/json

{
    "name_en": "Color",
    "name_ar": "اللون",
    "slug": "color",
    "options": [
        {"value_en": "Red", "value_ar": "أحمر"},
        {"value_en": "Blue", "value_ar": "أزرق"},
        {"value_en": "Green", "value_ar": "أخضر"}
    ]
}
```

#### Get Specific Dropdown
```
GET /api/admin/dropdowns/{id}
```

#### Update Dropdown
```
PUT /api/admin/dropdowns/{id}
Content-Type: application/json

{
    "name_en": "Updated Color",
    "name_ar": "اللون المحدث",
    "slug": "updated-color",
    "options": [
        {"value_en": "Red", "value_ar": "أحمر"},
        {"value_en": "Blue", "value_ar": "أزرق"}
    ]
}
```

#### Delete Dropdown
```
DELETE /api/admin/dropdowns/{id}
```

## Note

DropdownOptions are managed exclusively through the Dropdown API. There are no separate DropdownOption endpoints - all operations are handled through the parent Dropdown, following the ProductAttribute/ProductAttributeValue pattern exactly.

## Model Relationships

### Dropdown Model
```php
class Dropdown extends Model
{
    protected $fillable = ['name_en', 'name_ar', 'slug'];

    public function options()
    {
        return $this->hasMany(DropdownOption::class);
    }
}
```

### DropdownOption Model
```php
class DropdownOption extends Model
{
    protected $fillable = [
        'dropdown_id', 'value_en', 'value_ar'
    ];

    public function dropdown()
    {
        return $this->belongsTo(Dropdown::class, 'dropdown_id');
    }
}
```

## Usage Examples

### Creating a Dropdown with Options
```php
$dropdown = Dropdown::create([
    'name_en' => 'Size',
    'name_ar' => 'الحجم',
    'slug' => 'size'
]);

$dropdown->options()->createMany([
    ['value_en' => 'Small', 'value_ar' => 'صغير'],
    ['value_en' => 'Medium', 'value_ar' => 'متوسط'],
    ['value_en' => 'Large', 'value_ar' => 'كبير']
]);
```

### Retrieving Dropdown with Options
```php
$dropdown = Dropdown::with('options')->find(1);
```

## Migration Files Created

1. `2025_06_16_082652_create_dropdowns_table.php` - Creates dropdowns table with final structure (name_en, name_ar, slug)
2. `2025_06_16_082721_update_dropdown_options_table_for_dropdown_module.php` - Updates dropdown_options table: adds dropdown_id foreign key, removes legacy columns, ensures clean structure

## Clean Architecture

The module follows the ProductAttribute/ProductAttributeValue pattern exactly:
- No separate DropdownOption API endpoints
- All operations handled through parent Dropdown
- Clean, minimal database structure
- Consistent with existing codebase patterns

## Files Created/Modified

### New Files
- `app/Models/Dropdown.php`
- `app/Http/Controllers/DropdownController.php`
- `app/Services/DropdownService.php`
- `app/Http/Requests/StoreDropdownRequest.php`
- `app/Http/Requests/UpdateDropdownRequest.php`
- `database/seeders/DropdownSeeder.php`

### Modified Files
- `app/Models/DropdownOption.php` - Cleaned up to follow ProductAttributeValue pattern exactly
- `routes/api.php` - Added dropdown routes, removed legacy dropdown-option routes

### Removed Files
- `app/Http/Controllers/DropdownOptionController.php` - No longer needed
- `app/Services/DropdownOptionService.php` - No longer needed
- `app/Http/Requests/StoreDropdownOptionRequest.php` - No longer needed
- `app/Http/Requests/UpdateDropdownOptionRequest.php` - No longer needed

This implementation provides a clean, structured approach to managing dropdown data following the ProductAttribute/ProductAttributeValue pattern exactly.
