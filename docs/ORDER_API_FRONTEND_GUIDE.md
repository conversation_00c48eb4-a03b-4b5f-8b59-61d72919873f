# Order Management System - Frontend Integration Guide

## Overview
This guide provides comprehensive documentation for frontend developers to integrate with the new Order Management System APIs.

## Quick Start

### Authentication
All order endpoints require authentication. Include the bearer token in headers:
```javascript
headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}
```

### Base URLs
- **Client APIs:** `/api/client/orders`
- **Admin APIs:** `/api/admin/orders` 
- **Vendor APIs:** `/api/vendor/orders`

## Customer Order Management

### 1. Order Listing
Display customer's order history with filtering and pagination.

```javascript
// GET /api/client/orders
const getOrders = async (filters = {}) => {
    const params = new URLSearchParams({
        status: filters.status || '',
        payment_status: filters.payment_status || '',
        date_from: filters.date_from || '',
        date_to: filters.date_to || '',
        search: filters.search || '',
        per_page: filters.per_page || 15,
        page: filters.page || 1
    });
    
    const response = await fetch(`/api/client/orders?${params}`, {
        headers: authHeaders
    });
    
    return response.json();
};
```

**Response Structure:**
```json
{
    "data": [
        {
            "id": 1,
            "uuid": "550e8400-e29b-41d4-a716-************",
            "order_number": "ORD-202507-0001",
            "customer": {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>"
            },
            "pricing": {
                "total": 299.99,
                "currency": "AED",
                "total_items": 3
            },
            "status": {
                "fulfillment": "processing",
                "fulfillment_display": "Processing",
                "payment": "paid",
                "is_paid": true
            },
            "actions": {
                "can_cancel": false,
                "can_refund": false,
                "view_url": "/api/client/orders/550e8400-e29b-41d4-a716-************"
            },
            "created_at": "2025-07-27T10:30:00Z",
            "created_at_human": "2 hours ago"
        }
    ],
    "meta": {
        "current_page": 1,
        "total": 25,
        "per_page": 15,
        "statistics": {
            "total_orders": 25,
            "total_revenue": 7499.75,
            "average_order_value": 299.99
        }
    }
}
```

### 2. Order Creation from Cart
Convert a shopping cart into an order.

```javascript
// POST /api/client/orders/create-from-cart
const createOrderFromCart = async (orderData) => {
    const response = await fetch('/api/client/orders/create-from-cart', {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
            cart_uuid: orderData.cartUuid,
            payment_method: orderData.paymentMethod,
            shipping_address: {
                first_name: orderData.shipping.firstName,
                last_name: orderData.shipping.lastName,
                address_line_1: orderData.shipping.address1,
                address_line_2: orderData.shipping.address2,
                city: orderData.shipping.city,
                state: orderData.shipping.state,
                postal_code: orderData.shipping.postalCode,
                country: orderData.shipping.country,
                phone: orderData.shipping.phone,
                email: orderData.shipping.email
            },
            billing_address: orderData.billingAddress, // Optional
            use_shipping_for_billing: orderData.useShippingForBilling,
            customer_note: orderData.customerNote,
            terms_accepted: true
        })
    });
    
    return response.json();
};
```

### 3. Advanced Cart Conversion
For multi-vendor carts or advanced options.

```javascript
// POST /api/client/orders/convert-cart
const convertCart = async (conversionData) => {
    const response = await fetch('/api/client/orders/convert-cart', {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
            cart_uuid: conversionData.cartUuid,
            split_by_vendor: conversionData.splitByVendor, // Creates separate orders per vendor
            apply_member_pricing: conversionData.applyMemberPricing,
            recalculate_pricing: conversionData.recalculatePricing,
            validate_inventory: conversionData.validateInventory,
            shipping_address: conversionData.shippingAddress,
            billing_address: conversionData.billingAddress,
            payment_method: conversionData.paymentMethod,
            terms_accepted: true
        })
    });
    
    return response.json();
};
```

### 4. Order Details
Get comprehensive order information.

```javascript
// GET /api/client/orders/{uuid}
const getOrderDetails = async (orderUuid) => {
    const response = await fetch(`/api/client/orders/${orderUuid}`, {
        headers: authHeaders
    });
    
    return response.json();
};
```

**Detailed Response Structure:**
```json
{
    "data": {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "order_number": "ORD-202507-0001",
        "customer": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "pricing_tier": "VIP"
        },
        "pricing": {
            "subtotal": 279.99,
            "discount_total": 30.00,
            "tax_total": 14.00,
            "shipping_fee": 25.00,
            "total": 288.99,
            "currency": "AED",
            "savings_amount": 30.00
        },
        "status": {
            "fulfillment": "shipped",
            "fulfillment_display": "Shipped",
            "payment": "paid",
            "payment_display": "Paid",
            "is_paid": true
        },
        "details": {
            "payment_method": "card",
            "tracking_number": "TRK123456789",
            "customer_note": "Please deliver after 6 PM",
            "total_items": 3,
            "total_quantity": 5
        },
        "items": [
            {
                "id": 1,
                "product": {
                    "id": 101,
                    "title": "Premium Vitamin D3",
                    "sku": "VIT-D3-001",
                    "image": "/images/vitamin-d3.jpg"
                },
                "quantity": 2,
                "pricing": {
                    "base_price": 49.99,
                    "member_price": 39.99,
                    "unit_price": 39.99,
                    "subtotal": 79.98,
                    "discount": 20.00,
                    "tax": 4.00,
                    "total_with_tax": 83.98,
                    "savings_amount": 20.00,
                    "applied_pricing_type": "member"
                },
                "status": {
                    "fulfillment": "shipped",
                    "fulfillment_display": "Shipped"
                }
            }
        ],
        "addresses": {
            "shipping": {
                "contact": {
                    "full_name": "John Doe",
                    "phone": "+971501234567",
                    "email": "<EMAIL>"
                },
                "address": {
                    "address_line_1": "123 Main Street",
                    "city": "Dubai",
                    "country": "AE",
                    "formatted": "123 Main Street, Dubai, AE"
                }
            }
        },
        "status_history": [
            {
                "status_change": {
                    "from": "confirmed",
                    "to": "shipped",
                    "display": "Confirmed → Shipped",
                    "icon": "🚚",
                    "color": "info"
                },
                "timing": {
                    "changed_at": "2025-07-27T14:30:00Z",
                    "changed_at_human": "30 minutes ago"
                },
                "changed_by": {
                    "user_name": "Vendor Team",
                    "user_type": "vendor"
                }
            }
        ],
        "actions": [
            {
                "action": "view_details",
                "label": "View Details",
                "method": "GET",
                "url": "/api/client/orders/550e8400-e29b-41d4-a716-************"
            }
        ]
    },
    "meta": {
        "can_cancel": false,
        "can_refund": false,
        "order_age_days": 2,
        "estimated_delivery": "2025-07-29"
    }
}
```

### 5. Order Cancellation
Cancel an order (if eligible).

```javascript
// PATCH /api/client/orders/{uuid}/cancel
const cancelOrder = async (orderUuid, cancellationData) => {
    const response = await fetch(`/api/client/orders/${orderUuid}/cancel`, {
        method: 'PATCH',
        headers: authHeaders,
        body: JSON.stringify({
            reason: cancellationData.reason,
            refund_requested: cancellationData.refundRequested,
            notes: cancellationData.notes
        })
    });
    
    return response.json();
};
```

## UI Components Examples

### Order Status Badge
```jsx
const OrderStatusBadge = ({ status, paymentStatus }) => {
    const getStatusColor = (status) => {
        const colors = {
            pending: 'warning',
            confirmed: 'info',
            processing: 'primary',
            shipped: 'info',
            delivered: 'success',
            cancelled: 'danger'
        };
        return colors[status] || 'secondary';
    };

    return (
        <div className="flex gap-2">
            <Badge color={getStatusColor(status)}>
                {status.replace('_', ' ').toUpperCase()}
            </Badge>
            {paymentStatus === 'paid' && (
                <Badge color="success">PAID</Badge>
            )}
        </div>
    );
};
```

### Order Timeline
```jsx
const OrderTimeline = ({ statusHistory }) => {
    return (
        <div className="timeline">
            {statusHistory.map((entry, index) => (
                <div key={index} className="timeline-item">
                    <div className={`timeline-marker bg-${entry.status_change.color}`}>
                        {entry.status_change.icon}
                    </div>
                    <div className="timeline-content">
                        <h4>{entry.status_change.display}</h4>
                        <p className="text-sm text-gray-600">
                            {entry.timing.changed_at_human} by {entry.changed_by.user_name}
                        </p>
                        {entry.details.reason && (
                            <p className="text-sm">{entry.details.reason}</p>
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
};
```

### Order Summary Card
```jsx
const OrderSummaryCard = ({ order }) => {
    return (
        <div className="order-card border rounded-lg p-4">
            <div className="flex justify-between items-start mb-3">
                <div>
                    <h3 className="font-semibold">{order.order_number}</h3>
                    <p className="text-sm text-gray-600">
                        {order.created_at_human}
                    </p>
                </div>
                <OrderStatusBadge 
                    status={order.status.fulfillment}
                    paymentStatus={order.status.payment}
                />
            </div>
            
            <div className="flex justify-between items-center">
                <div>
                    <p className="text-sm text-gray-600">
                        {order.pricing.total_items} items
                    </p>
                    <p className="font-semibold">
                        {order.pricing.currency} {order.pricing.total}
                    </p>
                </div>
                
                <div className="flex gap-2">
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => viewOrder(order.uuid)}
                    >
                        View Details
                    </Button>
                    {order.actions.can_cancel && (
                        <Button 
                            variant="outline" 
                            size="sm" 
                            color="danger"
                            onClick={() => cancelOrder(order.uuid)}
                        >
                            Cancel
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};
```

## Error Handling

### Standard Error Response
```javascript
const handleApiError = (error) => {
    if (error.status === 422) {
        // Validation errors
        const validationErrors = error.data.errors;
        Object.keys(validationErrors).forEach(field => {
            showFieldError(field, validationErrors[field][0]);
        });
    } else if (error.status === 403) {
        // Access denied
        showNotification('You do not have permission to perform this action', 'error');
    } else if (error.status === 404) {
        // Not found
        showNotification('Order not found', 'error');
    } else {
        // General error
        showNotification(error.data.message || 'An error occurred', 'error');
    }
};
```

## Best Practices

### 1. Caching
- Cache order lists for better performance
- Invalidate cache when orders are updated
- Use order UUID for cache keys

### 2. Real-time Updates
- Consider WebSocket integration for order status updates
- Poll order details periodically for active orders
- Show loading states during API calls

### 3. Offline Support
- Store order data locally for offline viewing
- Queue order actions when offline
- Sync when connection is restored

### 4. Performance
- Use pagination for order lists
- Implement infinite scrolling for better UX
- Lazy load order details

### 5. Security
- Never expose order IDs in URLs (use UUIDs)
- Validate user permissions on frontend
- Sanitize user inputs

## Testing

### Unit Tests
```javascript
describe('Order API Integration', () => {
    test('should fetch orders with filters', async () => {
        const filters = { status: 'shipped', per_page: 10 };
        const orders = await getOrders(filters);
        
        expect(orders.data).toBeInstanceOf(Array);
        expect(orders.meta.current_page).toBe(1);
    });
    
    test('should create order from cart', async () => {
        const orderData = {
            cartUuid: 'test-cart-uuid',
            paymentMethod: 'card',
            shipping: mockShippingAddress
        };
        
        const order = await createOrderFromCart(orderData);
        expect(order.data.uuid).toBeDefined();
        expect(order.data.status.fulfillment).toBe('pending');
    });
});
```

## Conclusion

This guide provides all necessary information for frontend integration with the Order Management System. The APIs are designed to be intuitive and follow RESTful principles while providing comprehensive data for rich user interfaces.

For additional support or questions, refer to the API validation document or contact the backend development team.
