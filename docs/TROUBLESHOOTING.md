# Development Database Manager - Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 1. "Network error: Failed to fetch"

This error typically occurs when the frontend cannot communicate with the backend. Here are the solutions:

#### **Solution A: Clear Configuration Cache**
```bash
php artisan config:clear
php artisan route:clear
php artisan cache:clear
```

#### **Solution B: Check Environment Variables**
Ensure your `.env` file contains:
```env
APP_ENV=local
DEV_DB_USERNAME=admin
DEV_DB_PASSWORD=dev123!@#
```

#### **Solution C: Verify Server is Running**
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

#### **Solution D: Test Configuration**
Run this command to verify config is loaded:
```bash
php artisan tinker --execute="echo config('app.dev_username');"
```

### 2. "404 Not Found"

#### **Cause**: Application is in production mode
**Solution**: Check your `.env` file:
```env
APP_ENV=local  # or development
```

#### **Cause**: Routes not loaded
**Solution**: Clear route cache:
```bash
php artisan route:clear
```

### 3. "Invalid credentials"

#### **Cause**: Wrong username/password
**Solution**: Check your `.env` file for correct credentials:
```env
DEV_DB_USERNAME=your_username
DEV_DB_PASSWORD=your_password
```

#### **Cause**: Configuration not loaded
**Solution**: Clear config cache:
```bash
php artisan config:clear
```

### 4. "419 Page Expired" (CSRF Error)

#### **Cause**: CSRF token mismatch
**Solution**: The interface has been updated to handle CSRF properly with FormData. If you still see this:

1. Refresh the page to get a new CSRF token
2. Clear browser cache
3. Ensure the server is running properly

### 5. "Command not allowed"

#### **Cause**: Command not in whitelist
**Solution**: Only these commands are allowed:
- `migrate:fresh --seed`
- `migrate:fresh`
- `migrate`
- `migrate:rollback`
- `migrate:reset`
- `db:seed`
- `cache:clear`
- `config:clear`
- `route:clear`
- `view:clear`
- `optimize:clear`
- `storage:link`

### 6. "Destructive command requires confirmation"

#### **Cause**: Destructive commands need explicit confirmation
**Solution**: Check the "I understand this command may destroy data" checkbox for:
- `migrate:fresh --seed`
- `migrate:fresh`
- `migrate:reset`

### 7. Command Timeout

#### **Cause**: Command takes longer than 5 minutes
**Solution**: 
1. Check if your database is too large
2. Consider running commands in smaller batches
3. Increase timeout in `routes/web.php`:
```php
$process->setTimeout(600); // 10 minutes
```

### 8. Permission Denied

#### **Cause**: File permissions or database access issues
**Solution**:
```bash
# Fix storage permissions
chmod -R 775 storage bootstrap/cache

# Check database connection
php artisan tinker --execute="DB::connection()->getPdo();"
```

## 🔧 Quick Diagnostic Commands

### Test System Status
```bash
# Test basic connectivity
curl http://localhost:8000/dev/test

# Expected response:
# {"status":"working","env":"local","username_config":"admin","timestamp":"..."}
```

### Verify Configuration
```bash
# Check environment
php artisan env

# Check config values
php artisan tinker --execute="
echo 'Environment: ' . config('app.env') . PHP_EOL;
echo 'Username: ' . config('app.dev_username') . PHP_EOL;
echo 'Password set: ' . (config('app.dev_password') ? 'Yes' : 'No') . PHP_EOL;
"
```

### Test Database Connection
```bash
php artisan migrate:status
```

### Check Laravel Logs
```bash
tail -f storage/logs/laravel.log
```

## 🛠️ Advanced Troubleshooting

### Enable Debug Mode
Add to `.env`:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

### Check Route Registration
```bash
php artisan route:list | grep dev
```

### Verify Middleware
```bash
php artisan route:list --name=dev
```

### Test CSRF Token Generation
Open browser developer tools and check if the CSRF token meta tag is present:
```html
<meta name="csrf-token" content="...">
```

## 📞 Getting Help

If none of these solutions work:

1. **Check Laravel Version**: Ensure you're using Laravel 10+
2. **Review Error Logs**: Check `storage/logs/laravel.log`
3. **Browser Console**: Check for JavaScript errors in browser dev tools
4. **Network Tab**: Check actual HTTP requests/responses in browser dev tools
5. **Server Logs**: Check your web server error logs

## 🔒 Security Reminders

- ✅ Only use in development environments
- ✅ Never commit credentials to version control
- ✅ Use strong passwords for production-like development environments
- ✅ Remove or disable these routes before deploying to production

## 📝 Reporting Issues

When reporting issues, please include:

1. **Environment**: Laravel version, PHP version, OS
2. **Error Message**: Exact error message from browser/logs
3. **Steps to Reproduce**: What you did before the error occurred
4. **Configuration**: Relevant `.env` settings (without passwords)
5. **Browser**: Browser type and version
6. **Network**: Any proxy/firewall that might interfere
