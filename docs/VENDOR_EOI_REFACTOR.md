# Vendor EOI CRUD System Refactor

## 📋 Overview

The Vendor EOI (Expression of Interest) system has been successfully refactored based on client feedback to simplify the data structure and reduce unnecessary fields while maintaining all core functionality.

## 🔄 Changes Made

### **Database Schema Changes**

#### **Removed Fields:**
- **Director-related fields**: `director_name`, `director_designation`, `director_passport_number`, etc.
- **Document upload fields**: `director_passport_copy`, `spoc_emirates_id_copy`, etc.
- **Business type specific fields**: `manufacturer_brands`, `importer_brands`, `distributor_stores`, `retailer_total_outlets`
- **Tax/TRN related fields**: `tax_registration_number`, `trn_issue_date`, `trn_name_in_english`, `trn_name_in_arabic`
- **SPOC document fields**: `spoc_passport_number`, `spoc_emirates_id_number`, etc.
- **Business type field**: Replaced with flexible `business_data` array

#### **New Fields Added:**
- `other_social_media` (string, nullable) - Additional social media links
- `business_data` (JSON) - Array of business type objects with "type" and "details"
- `inventory_management` (enum) - Options: 'store_inventory', 'manage_orders', 'both'
- `order_collection_location` (JSON) - Array of location strings
- `order_collection_location_details` (text, nullable) - Details for "other" locations

#### **Modified Fields:**
- `categories_to_sell` - Now stores comma-separated category slugs, returns as array of objects
- `tl_entity_type` - Updated enum: 'LLC', 'PJSC', 'Sole_Proprietorship', 'Partnership', 'Branch'

### **Preserved Functionality:**
✅ Approval workflow (approval_status, approved_by, approveEOI method)  
✅ Email notification system (VendorEOISubmission mail)  
✅ Unique VEOI ID generation  
✅ Soft deletes and timestamps  
✅ Existing API endpoints structure  
✅ Related entities (contact persons, bank info, locations)

## 🏗️ Updated Components

### **1. Migration File**
```php
// File: database/migrations/2025_06_18_143000_refactor_vendor_eoi_table.php
// Removes old fields and adds new JSON fields with proper enum updates
```

### **2. VendorEoi Model**
```php
// Updated fillable fields, added JSON casting, and model relationships
protected $casts = [
    'business_data' => 'array',
    'order_collection_location' => 'array',
    'tl_license_first_issue_date' => 'date',
    'tl_license_valid_till' => 'date',
    'is_active' => 'boolean',
];

// Added relationships
public function contactPersons()
public function bankInformation()
public function locations()
public function approver()
```

### **3. Request Validation**
```php
// VendorEoiRequest & UpdateVendorEoiRequest updated with:
'business_data' => 'nullable|array',
'business_data.*.type' => 'required_with:business_data|in:Manufacturer,Importer,Distributor,Retailer,Others',
'business_data.*.details' => 'nullable|string|max:500',
'categories_to_sell' => 'nullable|array',
'categories_to_sell.*' => 'string|max:255',
'inventory_management' => 'nullable|in:store_inventory,manage_orders,both',
'order_collection_location' => 'nullable|array',
'tl_entity_type' => 'nullable|in:LLC,PJSC,Sole_Proprietorship,Partnership,Branch',
```

### **4. VendorEoiService Enhancements**
```php
// Enhanced search functionality
$searchKeys = ['name_tl_en', 'name_tl_ar', 'spoc_email', 'veoi_id'];

// Added approval status filtering
if ($request->filled('approval_status')) {
    $query->where('approval_status', $request->approval_status);
}

// Category processing - converts array to comma-separated string for storage
// Returns formatted response with category objects
```

## 📊 API Request/Response Format

### **Request Format (JSON):**
```json
{
    "name_tl_en": "mr mukaram",
    "name_tl_ar": "mr mukaram", 
    "vendor_display_name_en": "mr mukaram",
    "vendor_display_name_ar": "mr mukaram",
    "website": "https://www.reddit.com/",
    "facebook_page": "https://www.reddit.com/",
    "instagram_page": "https://www.reddit.com/",
    "other_social_media": "https://www.reddit.com/",
    "business_data": [
        {
            "type": "Manufacturer",
            "details": "nokia china"
        },
        {
            "type": "Retailer", 
            "details": "45"
        }
    ],
    "brands_to_sell": "nokia, sony",
    "categories_to_sell": ["electronics", "fashion", "home-garden"],
    "sku_on_amazon": 43,
    "sku_on_noon": 2,
    "inventory_management": "store_inventory",
    "order_collection_location": ["dubai", "sharjah", "abu_dhabi", "other"],
    "order_collection_location_details": "some details",
    "tl_entity_type": "LLC",
    "spoc_name": "test name",
    "spoc_email": "<EMAIL>"
}
```

### **Response Format:**
```json
{
    "status": true,
    "data": {
        "id": 1,
        "veoi_id": "VEOI#20250618MRMUK001",
        "name_tl_en": "mr mukaram",
        "business_data": [
            {
                "type": "Manufacturer",
                "details": "nokia china"
            }
        ],
        "categories_to_sell": [
            {
                "id": 1,
                "name": "Electronics", 
                "slug": "electronics"
            }
        ],
        "order_collection_location": ["dubai", "sharjah"],
        "inventory_management": "store_inventory",
        "approval_status": "Pending",
        // ... other fields
    },
    "message": "Vendor EOI created successfully!"
}
```

## 🔗 API Endpoints (Unchanged)

### **Admin Routes** (Authenticated + Admin Role)
```
GET    /api/admin/vendor-eoi           # List all EOIs
POST   /api/admin/vendor-eoi           # Create new EOI  
GET    /api/admin/vendor-eoi/{id}      # Get specific EOI
PUT    /api/admin/vendor-eoi/{id}      # Update EOI
DELETE /api/admin/vendor-eoi/{id}      # Delete EOI
POST   /api/admin/vendor-eoi/approve   # Approve/Reject EOI
```

### **Public Routes** (Rate Limited)
```
POST   /api/vendor-eoi/submit                           # Public EOI submission
GET    /api/vendor-eoi/bank/{vendor_eoi_id}            # Get bank info
POST   /api/vendor-eoi/bank/store                      # Add bank info
POST   /api/add-vendor-eoi-contact-persons             # Add contact person
GET    /api/get-vendor-eoi-contact-persons/{id}        # Get contact persons
POST   /api/vendor-eoi/add-location                    # Add location
GET    /api/vendor-eoi/location/{vendor_eoi_id}        # Get locations
```

## ✅ Testing Results

The refactored system has been successfully tested:

1. **✅ Migration Applied**: Database schema updated successfully
2. **✅ API Submission**: Public EOI submission working with new JSON structure
3. **✅ Data Processing**: Business data and location arrays properly stored
4. **✅ Category Handling**: Categories converted from array to comma-separated storage
5. **✅ Response Formatting**: Categories returned as objects with id, name, slug
6. **✅ VEOI ID Generation**: Unique ID generation still working
7. **✅ Email Notifications**: Email system preserved and functional

## 🎯 Key Improvements

1. **📉 Simplified Data Model**: Reduced from 40+ fields to ~20 essential fields
2. **🔄 Flexible Business Data**: JSON array supports multiple business types
3. **📍 Dynamic Locations**: JSON array for collection locations
4. **🏷️ Enhanced Categories**: Proper category object responses
5. **🔍 Better Search**: Enhanced search across multiple fields
6. **📊 Status Filtering**: Added approval status filtering
7. **🔗 Model Relationships**: Added proper Eloquent relationships
8. **📝 Improved Validation**: Comprehensive validation for new structure

## 🚀 Next Steps

1. **Frontend Integration**: Update frontend forms to use new JSON structure
2. **Documentation**: Update API documentation with new request/response formats
3. **Testing**: Add comprehensive unit tests for new functionality
4. **Category Management**: Ensure category slugs match frontend expectations
5. **File Uploads**: Implement file upload system if needed in future

The refactored Vendor EOI system is now more streamlined, flexible, and maintainable while preserving all essential functionality.
