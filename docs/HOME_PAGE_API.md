# Home Page API Documentation

## Overview

The Home Page APIs provide all the necessary data for rendering an e-commerce home page. These APIs are public (no authentication required) and optimized for frontend consumption with proper caching strategies.

## Base URL
```
/api/home
```

## Available Endpoints

### 1. Complete Home Page Data
**GET** `/api/home/<USER>

Returns all home page data in a single request for optimal performance.

**Response Structure:**
```json
{
  "status": true,
  "data": {
    "banners": [...],
    "categories": [...],
    "featured_products": [...],
    "latest_products": [...],
    "best_sellers": [...],
    "popular_brands": [...]
  },
  "message": "Home page data retrieved successfully!"
}
```

### 2. Featured/Popular Products
**GET** `/api/home/<USER>

**Query Parameters:**
- `limit` (optional): Number of products to return (default: 10)

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "Product Name",
      "name_ar": "اسم المنتج",
      "short_name": "Short Name",
      "regular_price": "480.00",
      "offer_price": "380.00",
      "has_discount": true,
      "discount_percentage": 21,
      "image_url": "https://s3.amazonaws.com/bucket/image.jpg",
      "category": {
        "id": 1,
        "name": "Electronics"
      },
      "product_class": {
        "id": 1,
        "name": "Smartphones"
      },
      "brand": {
        "id": 1,
        "name": "Brand Name"
      },
      "dietary_info": {
        "is_vegan": false,
        "is_vegetarian": false,
        "is_halal": true
      }
    }
  ]
}
```

### 3. Latest Products
**GET** `/api/home/<USER>

**Query Parameters:**
- `limit` (optional): Number of products to return (default: 10)

**Response:** Same structure as featured products

### 4. Categories with Product Counts
**GET** `/api/home/<USER>

**Query Parameters:**
- `limit` (optional): Number of categories to return (default: 8)

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "Electronics",
      "name_ar": "الإلكترونيات",
      "slug": "electronics",
      "icon_url": "https://s3.amazonaws.com/bucket/icon.jpg",
      "cover_image_url": "https://s3.amazonaws.com/bucket/cover.jpg",
      "products_count": 150
    }
  ]
}
```

### 5. Promotional Banners
**GET** `/api/home/<USER>

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "title": "Summer Sale",
      "description": "Up to 50% off on selected items",
      "items": [
        {
          "id": 1,
          "media_url": "https://s3.amazonaws.com/bucket/banner.jpg",
          "link_url": "/category/electronics",
          "alt_text": "Summer Sale Banner",
          "position": 1
        }
      ]
    }
  ]
}
```

### 6. Best Sellers by Category
**GET** `/api/home/<USER>

Returns best-selling products grouped by categories (limited to 2 categories as per design).

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "category": {
        "id": 1,
        "name": "Electronics",
        "name_ar": "الإلكترونيات"
      },
      "products": [
        // Array of products (same structure as featured products)
      ]
    }
  ]
}
```

### 7. Product Recommendations
**GET** `/api/home/<USER>

**Query Parameters:**
- `limit` (optional): Number of products to return (default: 8)

**Response:** Same structure as featured products

### 8. Popular Brands
**GET** `/api/home/<USER>

**Query Parameters:**
- `limit` (optional): Number of brands to return (default: 6)

**Response:**
```json
{
  "status": true,
  "data": [
    {
      "id": 1,
      "name": "Brand Name",
      "name_ar": "اسم العلامة التجارية",
      "slug": "brand-name",
      "logo_url": "https://s3.amazonaws.com/bucket/logo.jpg",
      "products_count": 25
    }
  ]
}
```

## Caching Strategy

- **Home Page Data**: Cached for 5 minutes
- **Categories**: Cached for 10 minutes
- **Banners**: Cached for 30 minutes
- **Popular Brands**: Cached for 30 minutes

## Performance Optimizations

1. **Eager Loading**: All relationships are loaded efficiently to avoid N+1 queries
2. **Selective Fields**: Only necessary fields are selected from the database
3. **Caching**: Frequently accessed data is cached with appropriate TTL
4. **Image URLs**: Pre-computed image URLs for frontend consumption
5. **Filtering**: Only active, approved products are included

## Error Handling

All endpoints return consistent error responses:

```json
{
  "status": false,
  "error": "Error message",
  "errors": "Detailed error information"
}
```

## Usage Examples

### Frontend Integration
```javascript
// Get complete home page data
const homeData = await fetch('/api/home/').then(res => res.json());

// Get specific sections
const categories = await fetch('/api/home/<USER>').then(res => res.json());
const featuredProducts = await fetch('/api/home/<USER>').then(res => res.json());
```

### Mobile App Integration
```dart
// Flutter example
Future<Map<String, dynamic>> getHomePageData() async {
  final response = await http.get(Uri.parse('$baseUrl/api/home/'));
  return json.decode(response.body);
}
```

## Architecture

- **Controller**: `HomeController` - Handles HTTP requests and responses
- **Service**: `HomeService` - Contains business logic and data processing
- **Models**: Utilizes existing `Product`, `Category`, `Brand`, `Banner` models
- **Caching**: Laravel Cache with configurable TTL
- **Database**: Optimized queries with proper indexing

## Future Enhancements

1. **Personalization**: User-based recommendations
2. **A/B Testing**: Dynamic content based on user segments
3. **Real-time Updates**: WebSocket integration for live data
4. **Analytics**: Track user interactions with home page elements
5. **Internationalization**: Multi-language support for all content
