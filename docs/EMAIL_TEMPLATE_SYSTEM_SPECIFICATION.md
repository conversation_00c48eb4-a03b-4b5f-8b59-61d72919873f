# Email Template System - Technical Specification

## 1. Executive Summary

This document outlines the technical specification for implementing a comprehensive admin-managed customizable email template system for the Vitamins.ae multi-vendor eCommerce platform. The system will provide administrators with the ability to create, manage, and customize email templates for various business communications while maintaining consistency with the existing Laravel-based architecture.

## 2. Current System Analysis

### 2.1 Existing Email Infrastructure
- **Framework**: Laravel 12.0 with Blade templating engine
- **Mail Configuration**: SMTP, SES, Postmark, and other Laravel-supported drivers
- **Authentication**: Laravel Passport OAuth2 with role-based access control (<PERSON><PERSON> Permission)
- **Current Mail Classes**: 
  - `OtpVerificationMail`
  - `ResetPasswordMail`
  - `VendorEOIApproved`
  - `VendorEOISubmission`
  - `VendorPasswordMail`

### 2.2 Existing User Roles and Permissions
- **Admin Role**: Full system access with `role:admin` middleware
- **Vendor Roles**: Multiple vendor-specific roles (vendor, vendor_assistant, etc.)
- **Customer Role**: Standard customer access
- **Permission System**: Spatie <PERSON>vel Permission package with granular permissions

### 2.3 Current Database Structure
- **Users Table**: Central user management with role assignments
- **Orders Table**: Comprehensive order management with status tracking
- **Vendors Table**: Detailed vendor information and approval workflow
- **Customers Table**: Customer profiles with preferences and KYC data
- **Notifications Table**: Basic notification system for user communications

## 3. System Architecture Overview

### 3.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │  Template API   │    │  Email Service  │
│   (Frontend)    │◄──►│   Controller    │◄──►│   Integration   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Template       │    │  Mail Classes   │
                       │  Engine         │    │  & Notifications│
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Database      │
                       │   (Templates,   │
                       │   Categories,   │
                       │   History)      │
                       └─────────────────┘
```

### 3.2 Core Components
1. **Template Management System**: CRUD operations for email templates
2. **Variable Engine**: Dynamic content replacement system
3. **Category Management**: Template organization and classification
4. **Preview System**: Real-time template preview with sample data
5. **Version Control**: Template history and rollback functionality
6. **Integration Layer**: Seamless integration with existing Mail classes

## 4. Database Schema Design

### 4.1 Email Templates Table (`email_templates`)
```sql
CREATE TABLE email_templates (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body_html LONGTEXT NOT NULL,
    body_text LONGTEXT NULL,
    category_id BIGINT UNSIGNED NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    language VARCHAR(5) DEFAULT 'en',
    variables JSON NULL,
    metadata JSON NULL,
    created_by BIGINT UNSIGNED NULL,
    updated_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (category_id) REFERENCES email_template_categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_slug (slug),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_language (language)
);
```

### 4.2 Email Template Categories Table (`email_template_categories`)
```sql
CREATE TABLE email_template_categories (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    icon VARCHAR(100) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);
```

### 4.3 Email Template Variables Table (`email_template_variables`)
```sql
CREATE TABLE email_template_variables (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    key VARCHAR(255) NOT NULL,
    description TEXT NULL,
    data_type ENUM('string', 'number', 'date', 'boolean', 'object', 'array') DEFAULT 'string',
    default_value TEXT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    category VARCHAR(100) NULL,
    example_value TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_key (key),
    INDEX idx_category (category),
    INDEX idx_required (is_required)
);
```

### 4.4 Email Template History Table (`email_template_histories`)
```sql
CREATE TABLE email_template_histories (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    template_id BIGINT UNSIGNED NOT NULL,
    version_number INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body_html LONGTEXT NOT NULL,
    body_text LONGTEXT NULL,
    variables JSON NULL,
    metadata JSON NULL,
    changed_by BIGINT UNSIGNED NULL,
    change_reason VARCHAR(500) NULL,
    created_at TIMESTAMP NULL,
    
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_template_version (template_id, version_number),
    INDEX idx_created_at (created_at)
);
```

## 5. API Endpoint Specifications

### 5.1 Template Management Endpoints

#### GET /api/admin/email-templates
- **Purpose**: List all email templates with filtering and pagination
- **Authentication**: Required (Admin role)
- **Parameters**: 
  - `page` (int): Page number
  - `per_page` (int): Items per page (max 100)
  - `category_id` (int): Filter by category
  - `language` (string): Filter by language
  - `search` (string): Search in name/subject
  - `is_active` (boolean): Filter by status
- **Response**: Paginated list of templates with metadata

#### POST /api/admin/email-templates
- **Purpose**: Create new email template
- **Authentication**: Required (Admin role)
- **Validation**: 
  - `name`: required|string|max:255
  - `subject`: required|string|max:500
  - `body_html`: required|string
  - `body_text`: nullable|string
  - `category_id`: nullable|exists:email_template_categories,id
  - `language`: required|string|size:2
  - `variables`: nullable|array
- **Response**: Created template with UUID

#### GET /api/admin/email-templates/{uuid}
- **Purpose**: Get specific template details
- **Authentication**: Required (Admin role)
- **Response**: Complete template data with variables and metadata

#### PUT /api/admin/email-templates/{uuid}
- **Purpose**: Update existing template
- **Authentication**: Required (Admin role)
- **Validation**: Same as POST with optional fields
- **Response**: Updated template data

#### DELETE /api/admin/email-templates/{uuid}
- **Purpose**: Soft delete template
- **Authentication**: Required (Admin role)
- **Response**: Success confirmation

### 5.2 Category Management Endpoints

#### GET /api/admin/email-template-categories
- **Purpose**: List all template categories
- **Authentication**: Required (Admin role)
- **Response**: List of categories with template counts

#### POST /api/admin/email-template-categories
- **Purpose**: Create new category
- **Authentication**: Required (Admin role)
- **Response**: Created category data

### 5.3 Preview and Testing Endpoints

#### POST /api/admin/email-templates/{uuid}/preview
- **Purpose**: Generate template preview with sample data
- **Authentication**: Required (Admin role)
- **Parameters**: 
  - `sample_data` (object): Variable values for preview
- **Response**: Rendered HTML and text versions

#### POST /api/admin/email-templates/{uuid}/test-send
- **Purpose**: Send test email to specified address
- **Authentication**: Required (Admin role)
- **Parameters**: 
  - `email` (string): Test recipient email
  - `sample_data` (object): Variable values for test
- **Response**: Send status confirmation

## 6. Template Variable System Design

### 6.1 Variable Categories
- **User Variables**: `{{user.name}}`, `{{user.email}}`, `{{user.phone}}`
- **Order Variables**: `{{order.number}}`, `{{order.total}}`, `{{order.status}}`
- **Vendor Variables**: `{{vendor.name}}`, `{{vendor.contact}}`
- **System Variables**: `{{site.name}}`, `{{site.url}}`, `{{current.date}}`
- **Custom Variables**: Admin-defined variables for specific use cases

### 6.2 Variable Processing Engine
- **Syntax**: Handlebars-style `{{variable.property}}` notation
- **Nested Objects**: Support for deep object property access
- **Conditional Logic**: Basic if/else statements for dynamic content
- **Loops**: Support for iterating over arrays (order items, etc.)
- **Filters**: Text formatting (uppercase, date formatting, currency)

## 7. Security Considerations

### 7.1 Admin Access Control
- **Role-Based Access**: Only users with `admin` role can access template management
- **Permission Granularity**: Specific permissions for create, read, update, delete operations
- **Audit Logging**: Track all template changes with user attribution
- **Input Validation**: Comprehensive validation for all template data

### 7.2 Template Security
- **HTML Sanitization**: Prevent XSS attacks in template content
- **Variable Validation**: Ensure only allowed variables are processed
- **Template Isolation**: Prevent access to system variables or functions
- **Version Control**: Maintain history for rollback capabilities

## 8. Integration Points

### 8.1 Existing Email System Integration
- **Mail Class Enhancement**: Extend existing Mail classes to use templates
- **Backward Compatibility**: Maintain existing email functionality
- **Template Resolution**: Automatic template selection based on email type
- **Fallback Mechanism**: Use default templates if custom ones fail

### 8.2 Notification System Integration
- **Template-Based Notifications**: Use templates for system notifications
- **Multi-Channel Support**: Email, SMS, push notification templates
- **Event-Driven Templates**: Automatic template selection based on events

## 9. Implementation Plan

### 9.1 Phase 1: Foundation (Week 1)
- Database schema creation and migrations
- Core model development with relationships
- Basic CRUD API endpoints

### 9.2 Phase 2: Template Engine (Week 2)
- Variable processing engine implementation
- Template rendering system
- Preview functionality

### 9.3 Phase 3: Advanced Features (Week 3)
- Category management system
- Version control and history tracking
- Integration with existing Mail classes

### 9.4 Phase 4: Testing and Documentation (Week 4)
- Comprehensive testing suite
- API documentation updates
- Performance optimization

## 10. Performance Considerations

### 10.1 Caching Strategy
- **Template Caching**: Cache compiled templates for faster rendering
- **Variable Caching**: Cache frequently used variable data
- **Database Optimization**: Proper indexing for template queries

### 10.2 Scalability
- **Template Compilation**: Pre-compile templates for production use
- **Queue Integration**: Use Laravel queues for bulk email operations
- **CDN Integration**: Store template assets in CDN for faster loading

## 11. Monitoring and Maintenance

### 11.1 Logging
- **Template Usage**: Track template usage statistics
- **Error Logging**: Comprehensive error tracking for template rendering
- **Performance Metrics**: Monitor template rendering performance

### 11.2 Maintenance
- **Template Validation**: Regular validation of template syntax
- **Variable Updates**: Automatic updates when system variables change
- **Cleanup Procedures**: Regular cleanup of old template versions

## 12. Template Categories and Use Cases

### 12.1 Predefined Template Categories
1. **Authentication & Security**
   - OTP Verification
   - Password Reset
   - Account Activation
   - Security Alerts

2. **Order Management**
   - Order Confirmation
   - Payment Confirmation
   - Shipping Notifications
   - Delivery Confirmation
   - Order Cancellation
   - Refund Notifications

3. **Vendor Communications**
   - Vendor EOI Submission
   - Vendor Approval/Rejection
   - Vendor Account Creation
   - Vendor Performance Reports
   - Commission Statements

4. **Customer Engagement**
   - Welcome Emails
   - Newsletter Subscriptions
   - Promotional Offers
   - Abandoned Cart Reminders
   - Loyalty Program Updates

5. **Support & Service**
   - Support Ticket Creation
   - Support Ticket Updates
   - Service Announcements
   - Maintenance Notifications

### 12.2 Template Variable Mapping

#### User Context Variables
```json
{
  "user": {
    "id": "User ID",
    "name": "Full name",
    "email": "Email address",
    "phone": "Phone number",
    "avatar_url": "Profile picture URL",
    "created_at": "Registration date",
    "is_verified": "Verification status"
  }
}
```

#### Order Context Variables
```json
{
  "order": {
    "id": "Order ID",
    "order_number": "Order number (INV-2025-0001)",
    "total": "Order total amount",
    "subtotal": "Subtotal amount",
    "tax_total": "Tax amount",
    "shipping_fee": "Shipping cost",
    "currency": "Currency code (AED)",
    "payment_status": "Payment status",
    "fulfillment_status": "Fulfillment status",
    "created_at": "Order date",
    "items": "Array of order items",
    "shipping_address": "Delivery address",
    "tracking_number": "Shipment tracking"
  }
}
```

#### Vendor Context Variables
```json
{
  "vendor": {
    "id": "Vendor ID",
    "name": "Vendor name",
    "code": "Vendor code",
    "email": "Contact email",
    "phone": "Contact phone",
    "website": "Website URL",
    "approval_status": "Approval status",
    "spoc_name": "Primary contact name",
    "spoc_email": "Primary contact email"
  }
}
```

#### System Context Variables
```json
{
  "site": {
    "name": "Vitamins.ae",
    "url": "https://vitamins.ae",
    "support_email": "<EMAIL>",
    "logo_url": "Logo image URL",
    "address": "Company address",
    "phone": "Company phone"
  },
  "current": {
    "date": "Current date",
    "time": "Current time",
    "year": "Current year"
  }
}
```

## 13. Error Handling and Validation

### 13.1 Template Validation Rules
- **Subject Line**: Maximum 500 characters, required
- **HTML Body**: Required, valid HTML structure
- **Text Body**: Optional, plain text only
- **Variables**: Must use valid syntax `{{variable.property}}`
- **Language Code**: Must be valid ISO 639-1 code
- **Category**: Must exist in categories table

### 13.2 Runtime Error Handling
- **Missing Variables**: Use default values or empty strings
- **Invalid HTML**: Sanitize and log warnings
- **Template Not Found**: Fall back to system default
- **Rendering Errors**: Log error and use plain text version

### 13.3 Validation Response Format
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "subject": ["Subject is required"],
    "body_html": ["Invalid HTML structure"],
    "variables": ["Unknown variable: {{invalid.var}}"]
  }
}
```

## 14. Testing Strategy

### 14.1 Unit Tests
- Model validation and relationships
- Template rendering engine
- Variable processing logic
- API endpoint responses

### 14.2 Integration Tests
- Email sending functionality
- Template-Mail class integration
- Database operations
- Authentication and authorization

### 14.3 Feature Tests
- Complete template CRUD workflows
- Preview generation
- Version control operations
- Multi-language support

### 14.4 Performance Tests
- Template rendering speed
- Database query optimization
- Concurrent template usage
- Memory usage during bulk operations

## 15. Deployment and Configuration

### 15.1 Environment Configuration
```env
# Email Template System Configuration
EMAIL_TEMPLATE_CACHE_ENABLED=true
EMAIL_TEMPLATE_CACHE_TTL=3600
EMAIL_TEMPLATE_DEFAULT_LANGUAGE=en
EMAIL_TEMPLATE_MAX_VERSIONS=10
EMAIL_TEMPLATE_PREVIEW_TIMEOUT=30
```

### 15.2 Database Seeding
- Default template categories
- System variable definitions
- Sample templates for each category
- Admin permissions setup

### 15.3 Migration Strategy
- Backward compatibility with existing emails
- Gradual migration of hardcoded templates
- Data migration scripts for existing templates
- Rollback procedures

## 16. API Documentation Standards

### 16.1 OpenAPI Specification
- Complete endpoint documentation
- Request/response schemas
- Authentication requirements
- Error response formats

### 16.2 Example API Responses

#### Template List Response
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "Order Confirmation",
        "slug": "order-confirmation",
        "subject": "Your Order #{{order.order_number}} is Confirmed",
        "category": {
          "id": 1,
          "name": "Order Management",
          "slug": "order-management"
        },
        "language": "en",
        "is_active": true,
        "created_at": "2025-01-15T10:30:00Z",
        "updated_at": "2025-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 45,
      "last_page": 3
    }
  }
}
```

#### Template Preview Response
```json
{
  "success": true,
  "data": {
    "html_preview": "<html>...</html>",
    "text_preview": "Plain text version...",
    "subject_preview": "Your Order #INV-2025-0001 is Confirmed",
    "variables_used": [
      "order.order_number",
      "user.name",
      "site.name"
    ]
  }
}
```

## 17. Future Enhancements

### 17.1 Advanced Features
- **A/B Testing**: Template performance comparison
- **Personalization**: AI-driven content personalization
- **Advanced Analytics**: Email open rates, click tracking
- **Template Marketplace**: Shared template library
- **Drag-and-Drop Editor**: Visual template builder

### 17.2 Integration Expansions
- **CRM Integration**: Connect with customer relationship management
- **Marketing Automation**: Advanced email campaign management
- **SMS Templates**: Extend system to support SMS templates
- **Push Notification Templates**: Mobile app notification templates

---

**Document Version**: 1.0
**Last Updated**: January 28, 2025
**Author**: Development Team
**Review Status**: Draft
