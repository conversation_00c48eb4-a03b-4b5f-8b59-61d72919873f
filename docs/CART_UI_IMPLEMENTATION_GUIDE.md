# Shopping Cart System - UI Implementation Guide

## 📋 Overview

This document provides comprehensive instructions for implementing the shopping cart system frontend. The cart system supports both guest and authenticated users, with automatic cart migration, real-time updates, and multi-vendor checkout.

## 🎯 Key Features to Implement

### Core Cart Features
- ✅ Add/Remove items from cart
- ✅ Update item quantities with validation
- ✅ Real-time cart totals calculation
- ✅ Guest cart with session persistence
- ✅ Automatic cart migration on login
- ✅ Multi-vendor cart splitting
- ✅ Coupon application and removal
- ✅ Cart validation before checkout
- ✅ Save items for later (wishlist)
- ✅ Cart recovery from abandoned carts

## 🔗 API Endpoints Reference

### Guest Cart Operations
```javascript
// Create new cart
POST /api/client/cart
{
  "currency": "AED",
  "notes": "Optional cart notes"
}

// Get cart by UUID
GET /api/client/cart/{cartId}

// Add item to cart
POST /api/client/cart/{cartId}/items
{
  "product_id": 123,
  "variant_id": 456, // Optional
  "quantity": 2,
  "customizations": {}, // Optional
  "special_instructions": "Optional instructions"
}

// Update cart item
PUT /api/client/cart/{cartId}/items/{itemId}
{
  "quantity": 5,
  "special_instructions": "Updated instructions"
}

// Remove cart item
DELETE /api/client/cart/{cartId}/items/{itemId}

// Bulk update items
POST /api/client/cart/{cartId}/items/bulk
{
  "items": [
    {"id": 1, "quantity": 3},
    {"id": 2, "quantity": 0}, // Remove item
    {"id": 3, "quantity": 1}
  ]
}

// Apply coupon
POST /api/client/cart/{cartId}/apply-coupon
{
  "coupon_code": "SAVE20"
}

// Remove coupon
DELETE /api/client/cart/{cartId}/remove-coupon
{
  "coupon_code": "SAVE20"
}

// Get vendor split for checkout
GET /api/client/cart/{cartId}/vendors

// Validate cart
POST /api/client/cart/{cartId}/validate

// Clear cart
DELETE /api/client/cart/{cartId}

// Merge carts
POST /api/client/cart/{cartId}/merge
{
  "source_cart_id": "source-cart-uuid"
}
```

### Authenticated User Cart Operations
```javascript
// Get current user cart
GET /api/client/my-cart

// Migrate guest cart
POST /api/client/my-cart/migrate
{
  "guest_session_id": "guest-session-id",
  "merge_strategy": "merge", // merge, replace, keep_both
  "clear_guest_cart": true
}

// Get cart history
GET /api/client/my-cart/history?status=abandoned&limit=10

// Save items for later
POST /api/client/my-cart/save-for-later
{
  "cart_item_ids": [1, 2, 3]
}

// Get saved items
GET /api/client/my-cart/saved-items?limit=20

// Get cart statistics
GET /api/client/my-cart/statistics

// Clear current cart
DELETE /api/client/my-cart/clear
```

## 🏗️ Frontend Architecture

### State Management Structure

```javascript
// Redux/Vuex Store Structure
const cartState = {
  // Current cart data
  currentCart: {
    id: null,
    uuid: null,
    user_id: null,
    session_id: null,
    currency: 'AED',
    status: 'active',
    
    // Totals
    subtotal: 0,
    tax_amount: 0,
    discount_amount: 0,
    shipping_amount: 0,
    total_amount: 0,
    
    // Metadata
    items_count: 0,
    total_quantity: 0,
    is_expired: false,
    
    // Items and relationships
    items: [],
    vendor_groups: [],
    applied_coupons: [],
    
    // Timestamps
    expires_at: null,
    last_activity_at: null,
    created_at: null,
    updated_at: null
  },
  
  // UI state
  loading: false,
  error: null,
  isUpdating: false,
  
  // Cart operations
  addingToCart: {},
  updatingItems: {},
  removingItems: {},
  
  // Validation
  validationErrors: [],
  validationWarnings: [],
  isValid: true,
  
  // Guest cart migration
  guestCartMigration: {
    available: false,
    sessionId: null,
    itemsCount: 0,
    totalValue: 0
  },
  
  // User cart features
  cartHistory: [],
  savedItems: [],
  statistics: {}
};
```

### Cart Service/API Layer

```javascript
// cart.service.js
class CartService {
  constructor(apiClient) {
    this.api = apiClient;
    this.cartId = this.getCartId();
  }

  // Cart ID management
  getCartId() {
    return localStorage.getItem('cart_id') || sessionStorage.getItem('cart_id');
  }

  setCartId(cartId, persistent = false) {
    if (persistent) {
      localStorage.setItem('cart_id', cartId);
    } else {
      sessionStorage.setItem('cart_id', cartId);
    }
    this.cartId = cartId;
  }

  // Core cart operations
  async createCart(data = {}) {
    const response = await this.api.post('/cart', data);
    this.setCartId(response.data.uuid);
    return response.data;
  }

  async getCart(cartId = this.cartId) {
    if (!cartId) return null;
    const response = await this.api.get(`/cart/${cartId}`);
    return response.data;
  }

  async addItem(productId, quantity, options = {}) {
    const response = await this.api.post(`/cart/${this.cartId}/items`, {
      product_id: productId,
      quantity,
      ...options
    });
    return response.data;
  }

  async updateItem(itemId, data) {
    const response = await this.api.put(`/cart/${this.cartId}/items/${itemId}`, data);
    return response.data;
  }

  async removeItem(itemId) {
    await this.api.delete(`/cart/${this.cartId}/items/${itemId}`);
  }

  async bulkUpdateItems(items) {
    const response = await this.api.post(`/cart/${this.cartId}/items/bulk`, { items });
    return response.data;
  }

  async applyCoupon(couponCode) {
    const response = await this.api.post(`/cart/${this.cartId}/apply-coupon`, {
      coupon_code: couponCode
    });
    return response.data;
  }

  async removeCoupon(couponCode) {
    await this.api.delete(`/cart/${this.cartId}/remove-coupon`, {
      data: { coupon_code: couponCode }
    });
  }

  async validateCart() {
    const response = await this.api.post(`/cart/${this.cartId}/validate`);
    return response.data;
  }

  async getVendorSplit() {
    const response = await this.api.get(`/cart/${this.cartId}/vendors`);
    return response.data;
  }

  async clearCart() {
    await this.api.delete(`/cart/${this.cartId}`);
    this.clearCartId();
  }

  clearCartId() {
    localStorage.removeItem('cart_id');
    sessionStorage.removeItem('cart_id');
    this.cartId = null;
  }

  // User cart operations
  async getCurrentUserCart() {
    const response = await this.api.get('/my-cart');
    return response.data;
  }

  async migrateGuestCart(guestSessionId, strategy = 'merge') {
    const response = await this.api.post('/my-cart/migrate', {
      guest_session_id: guestSessionId,
      merge_strategy: strategy
    });
    return response.data;
  }

  async getCartHistory(filters = {}) {
    const params = new URLSearchParams(filters);
    const response = await this.api.get(`/my-cart/history?${params}`);
    return response.data;
  }

  async saveForLater(itemIds) {
    const response = await this.api.post('/my-cart/save-for-later', {
      cart_item_ids: itemIds
    });
    return response.data;
  }

  async getSavedItems() {
    const response = await this.api.get('/my-cart/saved-items');
    return response.data;
  }

  async getCartStatistics() {
    const response = await this.api.get('/my-cart/statistics');
    return response.data;
  }
}
```

## 🎨 UI Components Structure

### 1. Cart Icon/Badge Component
```javascript
// CartBadge.vue / CartBadge.jsx
const CartBadge = {
  props: {
    itemCount: Number,
    totalAmount: Number,
    currency: String
  },
  
  template: `
    <div class="cart-badge" @click="openCart">
      <i class="cart-icon"></i>
      <span class="badge" v-if="itemCount > 0">{{ itemCount }}</span>
      <div class="cart-total" v-if="showTotal">
        {{ currency }} {{ totalAmount }}
      </div>
    </div>
  `
};
```

### 2. Add to Cart Button Component
```javascript
// AddToCartButton.vue / AddToCartButton.jsx
const AddToCartButton = {
  props: {
    product: Object,
    variant: Object,
    quantity: { type: Number, default: 1 },
    disabled: Boolean
  },
  
  data() {
    return {
      loading: false,
      added: false
    };
  },
  
  methods: {
    async addToCart() {
      this.loading = true;
      try {
        await this.$store.dispatch('cart/addItem', {
          productId: this.product.id,
          variantId: this.variant?.id,
          quantity: this.quantity
        });
        
        this.added = true;
        setTimeout(() => this.added = false, 2000);
        
        // Show success notification
        this.$toast.success('Item added to cart!');
        
      } catch (error) {
        this.$toast.error(error.message || 'Failed to add item to cart');
      } finally {
        this.loading = false;
      }
    }
  }
};
```

### 3. Cart Drawer/Modal Component
```javascript
// CartDrawer.vue / CartDrawer.jsx
const CartDrawer = {
  props: {
    visible: Boolean
  },
  
  computed: {
    cart() {
      return this.$store.state.cart.currentCart;
    },
    
    vendorGroups() {
      return this.cart.vendor_groups || [];
    }
  },
  
  methods: {
    async updateQuantity(itemId, quantity) {
      try {
        await this.$store.dispatch('cart/updateItem', { itemId, quantity });
      } catch (error) {
        this.$toast.error('Failed to update quantity');
      }
    },
    
    async removeItem(itemId) {
      try {
        await this.$store.dispatch('cart/removeItem', itemId);
        this.$toast.success('Item removed from cart');
      } catch (error) {
        this.$toast.error('Failed to remove item');
      }
    },
    
    proceedToCheckout() {
      this.$router.push('/checkout');
    }
  }
};
```

### 4. Cart Item Component
```javascript
// CartItem.vue / CartItem.jsx
const CartItem = {
  props: {
    item: Object,
    editable: { type: Boolean, default: true }
  },
  
  data() {
    return {
      quantity: this.item.quantity,
      updating: false
    };
  },
  
  watch: {
    quantity(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== this.item.quantity) {
        this.debouncedUpdate();
      }
    }
  },
  
  methods: {
    debouncedUpdate: debounce(function() {
      this.updateQuantity();
    }, 500),
    
    async updateQuantity() {
      if (this.quantity === this.item.quantity) return;
      
      this.updating = true;
      try {
        await this.$emit('update-quantity', this.item.id, this.quantity);
      } catch (error) {
        this.quantity = this.item.quantity; // Revert on error
      } finally {
        this.updating = false;
      }
    },
    
    async removeItem() {
      await this.$emit('remove-item', this.item.id);
    },
    
    async saveForLater() {
      await this.$emit('save-for-later', this.item.id);
    }
  }
};
```

### 5. Cart Summary Component
```javascript
// CartSummary.vue / CartSummary.jsx
const CartSummary = {
  props: {
    cart: Object,
    showBreakdown: { type: Boolean, default: false }
  },
  
  computed: {
    subtotal() {
      return this.cart.subtotal || 0;
    },
    
    tax() {
      return this.cart.tax_amount || 0;
    },
    
    shipping() {
      return this.cart.shipping_amount || 0;
    },
    
    discount() {
      return this.cart.discount_amount || 0;
    },
    
    total() {
      return this.cart.total_amount || 0;
    },
    
    savings() {
      return this.cart.items?.reduce((total, item) => {
        return total + (item.savings_amount || 0);
      }, 0) || 0;
    }
  }
};
```

## 🔄 State Management Actions

### Redux Actions (or Vuex Actions)
```javascript
// cart.actions.js
const cartActions = {
  // Initialize cart
  async initializeCart({ commit, state }) {
    commit('SET_LOADING', true);
    try {
      let cart = null;
      
      // Try to get existing cart
      const cartId = cartService.getCartId();
      if (cartId) {
        cart = await cartService.getCart(cartId);
      }
      
      // Create new cart if none exists
      if (!cart) {
        cart = await cartService.createCart();
      }
      
      commit('SET_CART', cart);
      
      // Check for guest cart migration
      if (this.rootState.auth.isAuthenticated) {
        await dispatch('checkGuestCartMigration');
      }
      
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  // Add item to cart
  async addItem({ commit, state }, { productId, variantId, quantity, options = {} }) {
    commit('SET_ADDING_TO_CART', { productId, loading: true });
    
    try {
      // Ensure cart exists
      if (!state.currentCart.id) {
        await dispatch('initializeCart');
      }
      
      const item = await cartService.addItem(productId, quantity, {
        variant_id: variantId,
        ...options
      });
      
      // Refresh cart to get updated totals
      await dispatch('refreshCart');
      
      commit('SET_ADDING_TO_CART', { productId, loading: false });
      return item;
      
    } catch (error) {
      commit('SET_ADDING_TO_CART', { productId, loading: false });
      commit('SET_ERROR', error.message);
      throw error;
    }
  },

  // Update cart item
  async updateItem({ commit, dispatch }, { itemId, quantity, ...data }) {
    commit('SET_UPDATING_ITEM', { itemId, loading: true });
    
    try {
      if (quantity === 0) {
        await cartService.removeItem(itemId);
      } else {
        await cartService.updateItem(itemId, { quantity, ...data });
      }
      
      await dispatch('refreshCart');
      commit('SET_UPDATING_ITEM', { itemId, loading: false });
      
    } catch (error) {
      commit('SET_UPDATING_ITEM', { itemId, loading: false });
      commit('SET_ERROR', error.message);
      throw error;
    }
  },

  // Remove item from cart
  async removeItem({ commit, dispatch }, itemId) {
    commit('SET_REMOVING_ITEM', { itemId, loading: true });
    
    try {
      await cartService.removeItem(itemId);
      await dispatch('refreshCart');
      commit('SET_REMOVING_ITEM', { itemId, loading: false });
      
    } catch (error) {
      commit('SET_REMOVING_ITEM', { itemId, loading: false });
      commit('SET_ERROR', error.message);
      throw error;
    }
  },

  // Bulk update items
  async bulkUpdateItems({ commit, dispatch }, items) {
    commit('SET_UPDATING', true);
    
    try {
      await cartService.bulkUpdateItems(items);
      await dispatch('refreshCart');
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_UPDATING', false);
    }
  },

  // Apply coupon
  async applyCoupon({ commit, dispatch }, couponCode) {
    commit('SET_UPDATING', true);
    
    try {
      const result = await cartService.applyCoupon(couponCode);
      await dispatch('refreshCart');
      return result;
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_UPDATING', false);
    }
  },

  // Remove coupon
  async removeCoupon({ commit, dispatch }, couponCode) {
    commit('SET_UPDATING', true);
    
    try {
      await cartService.removeCoupon(couponCode);
      await dispatch('refreshCart');
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_UPDATING', false);
    }
  },

  // Validate cart
  async validateCart({ commit, state }) {
    try {
      const validation = await cartService.validateCart();
      commit('SET_VALIDATION', validation);
      return validation;
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    }
  },

  // Refresh cart data
  async refreshCart({ commit, state }) {
    if (!state.currentCart.uuid) return;
    
    try {
      const cart = await cartService.getCart(state.currentCart.uuid);
      commit('SET_CART', cart);
      
    } catch (error) {
      commit('SET_ERROR', error.message);
    }
  },

  // Clear cart
  async clearCart({ commit }) {
    commit('SET_UPDATING', true);
    
    try {
      await cartService.clearCart();
      commit('CLEAR_CART');
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_UPDATING', false);
    }
  },

  // Guest cart migration
  async checkGuestCartMigration({ commit, state }) {
    const guestSessionId = sessionStorage.getItem('guest_session_id');
    if (!guestSessionId) return;
    
    try {
      // Check if guest cart exists and has items
      const guestCart = await cartService.getCart(guestSessionId);
      if (guestCart && guestCart.items_count > 0) {
        commit('SET_GUEST_CART_MIGRATION', {
          available: true,
          sessionId: guestSessionId,
          itemsCount: guestCart.items_count,
          totalValue: guestCart.total_amount
        });
      }
      
    } catch (error) {
      // Guest cart doesn't exist or error occurred
      console.warn('Guest cart migration check failed:', error);
    }
  },

  async migrateGuestCart({ commit, dispatch }, { sessionId, strategy = 'merge' }) {
    commit('SET_UPDATING', true);
    
    try {
      const cart = await cartService.migrateGuestCart(sessionId, strategy);
      commit('SET_CART', cart);
      commit('CLEAR_GUEST_CART_MIGRATION');
      
      // Clear guest session
      sessionStorage.removeItem('guest_session_id');
      
      return cart;
      
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_UPDATING', false);
    }
  }
};
```

## 🎯 User Experience Flows

### 1. Guest User Cart Flow
```mermaid
graph TD
    A[User visits site] --> B[Browse products]
    B --> C[Add item to cart]
    C --> D[Create guest cart with session]
    D --> E[Continue shopping]
    E --> F{User logs in?}
    F -->|Yes| G[Migrate guest cart]
    F -->|No| H[Proceed as guest]
    G --> I[Merged cart with user account]
    H --> J[Guest checkout]
```

### 2. Cart Migration Flow
```mermaid
graph TD
    A[User has guest cart] --> B[User logs in]
    B --> C[System detects guest cart]
    C --> D[Show migration options]
    D --> E{User choice}
    E -->|Merge| F[Combine carts]
    E -->|Replace| G[Replace user cart]
    E -->|Keep Both| H[Keep separate]
    F --> I[Update quantities]
    G --> J[Clear user cart, use guest]
    H --> K[Maintain both carts]
```

### 3. Cart Validation Flow
```mermaid
graph TD
    A[User proceeds to checkout] --> B[Validate cart]
    B --> C{All items valid?}
    C -->|Yes| D[Proceed to checkout]
    C -->|No| E[Show validation errors]
    E --> F[Update cart items]
    F --> G[Remove invalid items]
    G --> B
```

## 🔧 Implementation Guidelines

### Error Handling
```javascript
// Error handling patterns
const handleCartError = (error, context) => {
  console.error(`Cart error in ${context}:`, error);

  // Handle specific error types
  switch (error.status) {
    case 404:
      // Cart or item not found
      store.dispatch('cart/initializeCart');
      break;

    case 422:
      // Validation errors
      const errors = error.response?.data?.errors || {};
      store.commit('cart/SET_VALIDATION_ERRORS', errors);
      break;

    case 403:
      // Unauthorized access
      store.dispatch('auth/logout');
      break;

    default:
      // Generic error
      toast.error(error.message || 'An error occurred');
  }
};
```

### Loading States
```javascript
// Loading state management
const loadingStates = {
  // Global cart loading
  cartLoading: false,

  // Item-specific loading
  addingToCart: {}, // { productId: boolean }
  updatingItems: {}, // { itemId: boolean }
  removingItems: {}, // { itemId: boolean }

  // Operation loading
  applyingCoupon: false,
  validatingCart: false,
  migratingCart: false
};
```

### Optimistic Updates
```javascript
// Optimistic update pattern
const optimisticUpdate = async (mutation, apiCall, rollback) => {
  // Apply optimistic update
  commit(mutation.optimistic);

  try {
    // Make API call
    const result = await apiCall();

    // Apply success update
    commit(mutation.success, result);

  } catch (error) {
    // Rollback on error
    commit(mutation.rollback, rollback);
    throw error;
  }
};
```

### Real-time Updates
```javascript
// WebSocket integration for real-time cart updates
const cartWebSocket = {
  connect(cartId) {
    this.ws = new WebSocket(`ws://localhost:6001/cart/${cartId}`);

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'cart_updated':
          store.commit('cart/SET_CART', data.cart);
          break;

        case 'item_out_of_stock':
          store.commit('cart/SET_ITEM_UNAVAILABLE', data.itemId);
          toast.warning(`${data.productName} is now out of stock`);
          break;

        case 'price_changed':
          store.commit('cart/UPDATE_ITEM_PRICE', data);
          toast.info(`Price updated for ${data.productName}`);
          break;
      }
    };
  },

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
};
```

## 📱 Mobile Considerations

### Touch Interactions
```css
/* Mobile-friendly cart interactions */
.cart-item {
  padding: 16px;
  min-height: 44px; /* iOS touch target minimum */
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Swipe to delete */
.cart-item {
  position: relative;
  overflow: hidden;
}

.cart-item.swiping {
  transform: translateX(-80px);
  transition: transform 0.3s ease;
}

.delete-action {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 80px;
  background: #ff4444;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

### Responsive Design
```css
/* Responsive cart drawer */
.cart-drawer {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  background: white;
  z-index: 1000;
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .cart-drawer {
    width: 100vw;
    transform: translateX(100%);
  }

  .cart-drawer.open {
    transform: translateX(0);
  }
}

@media (min-width: 769px) {
  .cart-drawer {
    width: 400px;
    transform: translateX(100%);
  }

  .cart-drawer.open {
    transform: translateX(0);
  }
}
```

## 🧪 Testing Guidelines

### Unit Tests
```javascript
// cart.service.test.js
describe('CartService', () => {
  let cartService;

  beforeEach(() => {
    cartService = new CartService(mockApiClient);
  });

  test('should create new cart', async () => {
    const cart = await cartService.createCart();
    expect(cart).toHaveProperty('uuid');
    expect(cart.status).toBe('active');
  });

  test('should add item to cart', async () => {
    const item = await cartService.addItem(123, 2);
    expect(item.product_id).toBe(123);
    expect(item.quantity).toBe(2);
  });

  test('should handle API errors', async () => {
    mockApiClient.post.mockRejectedValue(new Error('Network error'));

    await expect(cartService.addItem(123, 2))
      .rejects.toThrow('Network error');
  });
});
```

### Integration Tests
```javascript
// cart.integration.test.js
describe('Cart Integration', () => {
  test('should complete full cart flow', async () => {
    // Create cart
    const cart = await cartService.createCart();

    // Add items
    await cartService.addItem(123, 2);
    await cartService.addItem(456, 1);

    // Update quantity
    const items = await cartService.getCart();
    await cartService.updateItem(items.items[0].id, { quantity: 3 });

    // Apply coupon
    await cartService.applyCoupon('SAVE10');

    // Validate cart
    const validation = await cartService.validateCart();
    expect(validation.is_valid).toBe(true);
  });
});
```

### E2E Tests
```javascript
// cart.e2e.test.js
describe('Cart E2E', () => {
  test('should complete guest cart migration', async () => {
    // Add items as guest
    await page.goto('/products/123');
    await page.click('[data-testid="add-to-cart"]');

    // Login
    await page.goto('/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password');
    await page.click('[type="submit"]');

    // Check migration prompt
    await expect(page.locator('[data-testid="cart-migration"]')).toBeVisible();

    // Accept migration
    await page.click('[data-testid="migrate-cart"]');

    // Verify cart has items
    await page.click('[data-testid="cart-icon"]');
    await expect(page.locator('[data-testid="cart-item"]')).toHaveCount(1);
  });
});
```

## 🔒 Security Considerations

### Input Validation
```javascript
// Client-side validation
const validateCartInput = {
  quantity: (value) => {
    const num = parseInt(value);
    return num > 0 && num <= 999;
  },

  couponCode: (value) => {
    return /^[A-Z0-9]{3,20}$/.test(value);
  },

  productId: (value) => {
    return Number.isInteger(value) && value > 0;
  }
};
```

### XSS Prevention
```javascript
// Sanitize user inputs
import DOMPurify from 'dompurify';

const sanitizeInput = (input) => {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] });
};

// Use in components
const specialInstructions = sanitizeInput(userInput);
```

### CSRF Protection
```javascript
// Include CSRF token in requests
const apiClient = axios.create({
  headers: {
    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
  }
});
```

## 📊 Analytics Integration

### Cart Events Tracking
```javascript
// analytics.js
const trackCartEvent = (event, data) => {
  // Google Analytics 4
  gtag('event', event, {
    currency: data.currency || 'AED',
    value: data.value || 0,
    items: data.items || []
  });

  // Facebook Pixel
  fbq('track', event, data);

  // Custom analytics
  analytics.track(event, data);
};

// Usage in cart actions
const cartAnalytics = {
  addToCart: (item) => {
    trackCartEvent('add_to_cart', {
      currency: 'AED',
      value: item.total_price,
      items: [{
        item_id: item.product_id,
        item_name: item.product_name,
        quantity: item.quantity,
        price: item.unit_price
      }]
    });
  },

  removeFromCart: (item) => {
    trackCartEvent('remove_from_cart', {
      currency: 'AED',
      value: item.total_price,
      items: [{
        item_id: item.product_id,
        item_name: item.product_name,
        quantity: item.quantity,
        price: item.unit_price
      }]
    });
  },

  beginCheckout: (cart) => {
    trackCartEvent('begin_checkout', {
      currency: cart.currency,
      value: cart.total_amount,
      items: cart.items.map(item => ({
        item_id: item.product_id,
        item_name: item.product_name,
        quantity: item.quantity,
        price: item.unit_price
      }))
    });
  }
};
```

## 🚀 Performance Optimization

### Lazy Loading
```javascript
// Lazy load cart components
const CartDrawer = lazy(() => import('./components/CartDrawer'));
const CartPage = lazy(() => import('./pages/CartPage'));
const Checkout = lazy(() => import('./pages/Checkout'));
```

### Memoization
```javascript
// Memoize expensive calculations
const cartSelectors = {
  getCartTotal: createSelector(
    [state => state.cart.currentCart],
    (cart) => cart.total_amount || 0
  ),

  getVendorGroups: createSelector(
    [state => state.cart.currentCart.items],
    (items) => {
      return items.reduce((groups, item) => {
        const vendorId = item.vendor_id;
        if (!groups[vendorId]) {
          groups[vendorId] = {
            vendor: item.vendor,
            items: [],
            subtotal: 0
          };
        }
        groups[vendorId].items.push(item);
        groups[vendorId].subtotal += item.total_price;
        return groups;
      }, {});
    }
  )
};
```

### Debouncing
```javascript
// Debounce quantity updates
import { debounce } from 'lodash';

const debouncedUpdateQuantity = debounce(async (itemId, quantity) => {
  await store.dispatch('cart/updateItem', { itemId, quantity });
}, 500);
```

## 📋 Implementation Checklist

### Phase 1: Core Cart Functionality
- [ ] Cart service implementation
- [ ] State management setup
- [ ] Add to cart functionality
- [ ] Cart icon/badge component
- [ ] Basic cart drawer/modal
- [ ] Quantity updates
- [ ] Item removal

### Phase 2: Enhanced Features
- [ ] Guest cart session management
- [ ] Cart migration on login
- [ ] Coupon application
- [ ] Cart validation
- [ ] Multi-vendor support
- [ ] Save for later functionality

### Phase 3: Advanced Features
- [ ] Real-time updates
- [ ] Cart recovery
- [ ] Analytics integration
- [ ] Performance optimization
- [ ] Mobile optimizations
- [ ] Accessibility improvements

### Phase 4: Testing & Polish
- [ ] Unit tests
- [ ] Integration tests
- [ ] E2E tests
- [ ] Error handling
- [ ] Loading states
- [ ] User feedback

## 🎨 Design System Integration

### Cart Components Library
```javascript
// Design system components
import {
  Button,
  Input,
  Badge,
  Modal,
  Drawer,
  Card,
  Spinner,
  Toast,
  Alert
} from '@/design-system';

// Cart-specific styled components
const CartButton = styled(Button)`
  background: var(--primary-color);
  color: white;
  border-radius: 8px;
  padding: 12px 24px;

  &:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
  }
`;

const CartBadge = styled(Badge)`
  background: var(--accent-color);
  color: white;
  font-size: 12px;
  min-width: 20px;
  height: 20px;
`;
```

### Theming
```css
/* Cart theme variables */
:root {
  --cart-primary: #007bff;
  --cart-success: #28a745;
  --cart-warning: #ffc107;
  --cart-danger: #dc3545;
  --cart-background: #ffffff;
  --cart-border: #e9ecef;
  --cart-text: #212529;
  --cart-text-muted: #6c757d;
}

/* Dark theme */
[data-theme="dark"] {
  --cart-background: #1a1a1a;
  --cart-border: #333333;
  --cart-text: #ffffff;
  --cart-text-muted: #cccccc;
}
```

## 🔄 Cart Recovery Implementation

### Recovery URL Handler
```javascript
// CartRecovery.vue / CartRecovery.jsx
const CartRecovery = {
  async mounted() {
    const token = this.$route.params.token;
    await this.recoverCart(token);
  },

  methods: {
    async recoverCart(token) {
      try {
        // Call recovery API
        const response = await this.$http.post('/cart/recover', { token });

        if (response.data.cart) {
          // Restore cart
          this.$store.commit('cart/SET_CART', response.data.cart);

          // Redirect to cart page
          this.$router.push('/cart');

          this.$toast.success('Your cart has been restored!');
        } else {
          this.$toast.error('Recovery link has expired or is invalid');
          this.$router.push('/');
        }

      } catch (error) {
        this.$toast.error('Failed to recover cart');
        this.$router.push('/');
      }
    }
  }
};
```

### Abandoned Cart Banner
```javascript
// AbandonedCartBanner.vue
const AbandonedCartBanner = {
  data() {
    return {
      show: false,
      cartData: null
    };
  },

  async mounted() {
    // Check for abandoned cart in localStorage
    const abandonedCart = localStorage.getItem('abandoned_cart');
    if (abandonedCart) {
      this.cartData = JSON.parse(abandonedCart);
      this.show = true;
    }
  },

  methods: {
    async restoreCart() {
      try {
        // Restore abandoned cart
        await this.$store.dispatch('cart/restoreAbandonedCart', this.cartData);

        this.dismissBanner();
        this.$toast.success('Cart restored successfully!');

      } catch (error) {
        this.$toast.error('Failed to restore cart');
      }
    },

    dismissBanner() {
      this.show = false;
      localStorage.removeItem('abandoned_cart');
    }
  }
};
```

## 🌐 Internationalization (i18n)

### Cart Text Translations
```javascript
// locales/en.json
{
  "cart": {
    "title": "Shopping Cart",
    "empty": "Your cart is empty",
    "addToCart": "Add to Cart",
    "updateCart": "Update Cart",
    "removeItem": "Remove Item",
    "quantity": "Quantity",
    "subtotal": "Subtotal",
    "tax": "Tax",
    "shipping": "Shipping",
    "discount": "Discount",
    "total": "Total",
    "checkout": "Proceed to Checkout",
    "continueShopping": "Continue Shopping",
    "applyCoupon": "Apply Coupon",
    "couponCode": "Coupon Code",
    "saveForLater": "Save for Later",
    "moveToWishlist": "Move to Wishlist",
    "outOfStock": "Out of Stock",
    "limitedStock": "Only {count} left in stock",
    "invalidQuantity": "Please enter a valid quantity",
    "itemAdded": "Item added to cart",
    "itemRemoved": "Item removed from cart",
    "cartUpdated": "Cart updated successfully",
    "migration": {
      "title": "Merge Your Carts",
      "message": "You have items in your guest cart. Would you like to merge them with your account?",
      "merge": "Merge Carts",
      "replace": "Replace My Cart",
      "keepBoth": "Keep Both",
      "dismiss": "No Thanks"
    }
  }
}

// locales/ar.json
{
  "cart": {
    "title": "سلة التسوق",
    "empty": "سلة التسوق فارغة",
    "addToCart": "أضف إلى السلة",
    "updateCart": "تحديث السلة",
    "removeItem": "إزالة العنصر",
    "quantity": "الكمية",
    "subtotal": "المجموع الفرعي",
    "tax": "الضريبة",
    "shipping": "الشحن",
    "discount": "الخصم",
    "total": "المجموع",
    "checkout": "متابعة الدفع",
    "continueShopping": "متابعة التسوق"
  }
}
```

### RTL Support
```css
/* RTL cart styles */
[dir="rtl"] .cart-drawer {
  left: 0;
  right: auto;
}

[dir="rtl"] .cart-item {
  text-align: right;
}

[dir="rtl"] .quantity-controls {
  flex-direction: row-reverse;
}

[dir="rtl"] .cart-summary {
  text-align: right;
}
```

## 🎯 Accessibility (a11y)

### ARIA Labels and Roles
```html
<!-- Cart button with accessibility -->
<button
  class="cart-button"
  aria-label="Shopping cart with {{ itemCount }} items"
  aria-expanded="false"
  aria-haspopup="dialog"
  @click="openCart"
>
  <i class="cart-icon" aria-hidden="true"></i>
  <span class="sr-only">{{ itemCount }} items in cart</span>
  <span class="cart-badge" aria-hidden="true">{{ itemCount }}</span>
</button>

<!-- Cart drawer -->
<div
  class="cart-drawer"
  role="dialog"
  aria-modal="true"
  aria-labelledby="cart-title"
  aria-describedby="cart-description"
>
  <h2 id="cart-title">Shopping Cart</h2>
  <p id="cart-description">Review and modify your cart items</p>

  <!-- Cart items list -->
  <ul role="list" aria-label="Cart items">
    <li
      v-for="item in cart.items"
      :key="item.id"
      role="listitem"
      class="cart-item"
    >
      <!-- Item content -->
    </li>
  </ul>
</div>
```

### Keyboard Navigation
```javascript
// Keyboard navigation for cart
const cartKeyboardHandler = {
  mounted() {
    document.addEventListener('keydown', this.handleKeydown);
  },

  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeydown);
  },

  methods: {
    handleKeydown(event) {
      if (!this.cartOpen) return;

      switch (event.key) {
        case 'Escape':
          this.closeCart();
          break;

        case 'Tab':
          this.handleTabNavigation(event);
          break;

        case 'Enter':
        case ' ':
          if (event.target.classList.contains('cart-item-action')) {
            event.preventDefault();
            event.target.click();
          }
          break;
      }
    },

    handleTabNavigation(event) {
      const focusableElements = this.$el.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (event.shiftKey && document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      } else if (!event.shiftKey && document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }
};
```

## 📱 Progressive Web App (PWA) Features

### Offline Cart Support
```javascript
// Service worker for offline cart
self.addEventListener('fetch', (event) => {
  if (event.request.url.includes('/api/client/cart')) {
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          if (response) {
            return response;
          }

          return fetch(event.request)
            .then((response) => {
              const responseClone = response.clone();
              caches.open('cart-cache-v1')
                .then((cache) => {
                  cache.put(event.request, responseClone);
                });
              return response;
            });
        })
        .catch(() => {
          // Return cached cart data when offline
          return caches.match('/api/client/cart/offline');
        })
    );
  }
});
```

### Background Sync
```javascript
// Background sync for cart updates
self.addEventListener('sync', (event) => {
  if (event.tag === 'cart-sync') {
    event.waitUntil(syncCartData());
  }
});

async function syncCartData() {
  const pendingUpdates = await getStoredCartUpdates();

  for (const update of pendingUpdates) {
    try {
      await fetch('/api/client/cart/sync', {
        method: 'POST',
        body: JSON.stringify(update),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      await removePendingUpdate(update.id);
    } catch (error) {
      console.error('Failed to sync cart update:', error);
    }
  }
}
```

This comprehensive UI implementation guide provides everything the frontend team needs to build a complete, production-ready shopping cart system with all modern features including guest carts, automatic migration, real-time updates, accessibility, internationalization, and PWA capabilities.
