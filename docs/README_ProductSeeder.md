# Product Seeder Documentation

## Overview
The ProductSeeder creates realistic product data for a UAE-based health and wellness marketplace. It includes products across multiple categories with proper Arabic translations and UAE-specific compliance details.

## What's Included

### Seeders Created
1. **ProductSeeder** - Main seeder for products
2. **VendorSeeder** - Creates UAE-based vendors
3. **ProductFactory** - Factory for generating additional products

### Products Created
The seeder creates **7 realistic products** across 5 categories:

#### Health Supplements (VHMS Category)
1. **Vitamin D3 5000 IU**
   - Price: AED 89.00 (Offer: AED 75.00)
   - 120 capsules, Halal certified
   - Arabic name: فيتامين د3 5000 وحدة دولية

2. **Omega-3 Fish Oil 1000mg**
   - Price: AED 125.00 (Offer: AED 99.00)
   - 90 softgels, from Norway
   - Arabic name: زيت السمك أوميغا-3 1000 ملغ

#### Beauty Products
3. **Hyaluronic Acid Serum**
   - Price: AED 199.00 (Offer: AED 159.00)
   - 30ml, K-Beauty from South Korea
   - Arabic name: سيروم حمض الهيالورونيك

#### Food & Drink
4. **Organic Manuka Honey**
   - Price: AED 299.00 (Offer: AED 249.00)
   - 500g, UMF 15+, from New Zealand
   - Arabic name: عسل مانوكا العضوي

5. **Organic Quinoa**
   - Price: AED 45.00 (Offer: AED 39.00)
   - 1kg, gluten-free superfood
   - Arabic name: الكينوا العضوية

#### Sports Nutrition
6. **Whey Protein Isolate**
   - Price: AED 299.00 (Offer: AED 259.00)
   - 2kg, Vanilla flavor, 66 servings
   - Arabic name: بروتين مصل اللبن المعزول

#### Weight Management
7. **Green Tea Extract**
   - Price: AED 149.00 (Offer: AED 119.00)
   - 60 capsules, 50% EGCG
   - Arabic name: مستخلص الشاي الأخضر

### Vendors Created
The seeder creates **4 UAE-based vendors**:

1. **HealthPlus UAE** (Dubai) - Distributor
2. **Beauty Emirates** (Abu Dhabi) - Retailer  
3. **Nutrition Hub** (Sharjah) - Importer
4. **Wellness World** (Ajman) - Distributor

Each vendor includes:
- Complete UAE business registration details
- Trade license information
- VAT registration numbers
- Director and SPOC contact details
- Bank account information

## Features

### UAE Marketplace Compliance
- **VAT Handling**: Proper VAT categories (standard_5, exempted, zero_rated)
- **Halal Certification**: Most products marked as Halal
- **Arabic Translations**: All products have Arabic names and descriptions
- **UAE Business Types**: Vendors classified as importer/distributor/retailer
- **Local Authorities**: License issuing authorities from different emirates

### Product Details
- **Realistic Pricing**: Market-appropriate pricing in AED
- **Proper Categories**: Uses existing category structure (H, B, F, S, W codes)
- **Complete Information**: Includes ingredients, usage instructions, storage
- **Package Dimensions**: Physical dimensions and weights
- **Expiry Dates**: Realistic best-before dates
- **Discount Periods**: Active promotional pricing

### Technical Features
- **Unique SKUs**: System-generated and vendor SKUs
- **Barcodes**: Realistic barcode numbers
- **Brand Integration**: Links to existing approved brands
- **User Association**: Products linked to vendor users
- **Status Management**: Products set as active and submitted

## Usage

### Running the Seeders

```bash
# Run individual seeders
php artisan db:seed --class=VendorSeeder
php artisan db:seed --class=ProductSeeder

# Or run all seeders (includes ProductSeeder)
php artisan db:seed
```

### Using the Factory

```php
// Create random products
Product::factory()->count(10)->create();

// Create specific product types
Product::factory()->healthSupplement()->count(5)->create();
Product::factory()->beauty()->onSale()->count(3)->create();
Product::factory()->food()->active()->count(8)->create();
Product::factory()->sportsNutrition()->count(4)->create();
```

### API Testing

```bash
# Test the products API
curl -X GET "http://your-domain/api/admin/products" \
  -H "Accept: application/json"
```

## Database Structure

### Key Relationships
- Products → Users (vendor relationship)
- Products → Vendors (business details)
- Products → Categories (main and sub-categories)
- Products → Brands (brand information)

### Important Fields
- `system_sku`: Auto-generated unique identifier
- `vendor_sku`: Vendor's internal SKU
- `vat_tax`: Enum (exempted, standard_5, zero_rated)
- `status`: Enum (draft, pending, submitted)
- `is_active`: Boolean for product visibility
- `is_approved`: Boolean for admin approval

## Customization

### Adding More Products
1. Extend the product arrays in each category method
2. Add new categories by creating new methods
3. Update the main `run()` method to call new methods

### Modifying Vendors
1. Edit the `$vendors` array in VendorSeeder
2. Ensure business_type matches enum values
3. Update preferred_language to use correct values

### Factory Customization
1. Add new product states in ProductFactory
2. Create category-specific methods
3. Customize pricing and attribute ranges

## Notes

- All products are created as 'submitted' status (ready for admin review)
- VAT rates follow UAE standards (5% standard, exempt for basic foods)
- Arabic text uses proper RTL characters
- Vendor Emirates ID and passport numbers are fictional
- Bank IBANs follow UAE format but are not real accounts

## Troubleshooting

### Common Issues
1. **Missing Categories**: Run CategorySeeder first
2. **Missing Brands**: Run BrandSeeder first  
3. **Missing Users**: Run UserSeeder first
4. **Enum Errors**: Check migration files for correct enum values

### Verification Commands
```bash
# Check product count
php artisan tinker --execute="echo App\Models\Product::count();"

# Check vendor count  
php artisan tinker --execute="echo App\Models\Vendor::count();"

# List products by category
php artisan tinker --execute="App\Models\Product::with('category')->get()->groupBy('category.name')->each(function(\$products, \$name) { echo \$name . ': ' . \$products->count() . PHP_EOL; });"
```
