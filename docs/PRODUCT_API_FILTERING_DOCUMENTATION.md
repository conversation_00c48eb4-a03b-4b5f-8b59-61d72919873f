# Product API Filtering and Search Documentation

## Overview

The Product API has been enhanced with comprehensive filtering and search capabilities to provide powerful querying options for the React frontend application.

## Base Endpoint

```
GET /api/admin/products
```

## Available Query Parameters

### 1. Search Parameters

#### `search` (string)
Performs a comprehensive search across multiple product fields simultaneously:
- Product title in English (`title_en`)
- Product title in Arabic (`title_ar`)
- Short description in English (`short_description_en`)
- Short description in Arabic (`short_description_ar`)
- System SKU (`system_sku`)
- Vendor SKU (`vendor_sku`)
- Barcode (`barcode`)

**Example:**
```
GET /api/admin/products?search=vitamin
```

### 2. Status Filters

#### `status` (string)
Filter products by their status field.
**Possible values:** `active`, `inactive`, `draft`, `pending`, `approved`, `rejected`

**Example:**
```
GET /api/admin/products?status=active
```

#### `is_active` (boolean)
Filter products by active status.
**Possible values:** `true`, `false`, `1`, `0`

**Example:**
```
GET /api/admin/products?is_active=true
```

#### `is_approved` (boolean)
Filter products by approval status.
**Possible values:** `true`, `false`, `1`, `0`

**Example:**
```
GET /api/admin/products?is_approved=true
```

### 3. Category Filters

#### `category_id` (integer|array)
Filter products by category ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?category_id=1
GET /api/admin/products?category_id=1,2,3
```

#### `sub_category_id` (integer|array)
Filter products by sub-category ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?sub_category_id=5
GET /api/admin/products?sub_category_id=5,6,7
```

### 4. Brand Filter

#### `brand_id` (integer|array)
Filter products by brand ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?brand_id=10
GET /api/admin/products?brand_id=10,11,12
```

### 5. Product Class Filters

#### `class_id` (integer|array)
Filter products by product class ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?class_id=3
GET /api/admin/products?class_id=3,4,5
```

#### `sub_class_id` (integer|array)
Filter products by sub-class ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?sub_class_id=8
GET /api/admin/products?sub_class_id=8,9,10
```

### 6. Vendor Filter

#### `vendor_id` (integer|array)
Filter products by vendor ID(s). Supports single ID or comma-separated multiple IDs.

**Examples:**
```
GET /api/admin/products?vendor_id=15
GET /api/admin/products?vendor_id=15,16,17
```

### 7. Stock Filters

#### `stock_status` (string)
Filter products by their inventory stock status.
**Possible values:** `in_stock`, `out_of_stock`, `low_stock`

**Example:**
```
GET /api/admin/products?stock_status=in_stock
```

#### `stock_availability` (string)
Filter products by calculated stock availability.
**Possible values:** 
- `in_stock`: Products with available stock > 0
- `out_of_stock`: Products with available stock <= 0  
- `low_stock`: Products with available stock <= threshold but > 0

**Example:**
```
GET /api/admin/products?stock_availability=low_stock
```

#### `min_stock` (integer)
Filter products with minimum stock level.

**Example:**
```
GET /api/admin/products?min_stock=10
```

#### `max_stock` (integer)
Filter products with maximum stock level.

**Example:**
```
GET /api/admin/products?max_stock=100
```

### 8. Price Filters

#### `min_price` (decimal)
Filter products with minimum regular price.

**Example:**
```
GET /api/admin/products?min_price=50.00
```

#### `max_price` (decimal)
Filter products with maximum regular price.

**Example:**
```
GET /api/admin/products?max_price=500.00
```

### 9. Product Type Filter

#### `is_variant` (boolean)
Filter products by variant type.
**Possible values:** `true`, `false`, `1`, `0`

**Example:**
```
GET /api/admin/products?is_variant=false
```

### 10. Date Range Filters

#### `created_from` (date)
Filter products created from a specific date.
**Format:** `YYYY-MM-DD`

**Example:**
```
GET /api/admin/products?created_from=2024-01-01
```

#### `created_to` (date)
Filter products created up to a specific date.
**Format:** `YYYY-MM-DD`

**Example:**
```
GET /api/admin/products?created_to=2024-12-31
```

### 11. Pagination Parameters

#### `pagination` (boolean)
Enable or disable pagination.
**Default:** `true`
**Possible values:** `true`, `false`, `1`, `0`

#### `per_page` (integer)
Number of items per page.
**Default:** `10`
**Range:** `1-100`

#### `page` (integer)
Page number for pagination.
**Default:** `1`

### 12. Sorting Parameters

#### `sortBy` (string)
Field to sort by.
**Default:** `id`
**Possible values:** Any valid product field (`id`, `title_en`, `created_at`, `regular_price`, etc.)

#### `sortDesc` (boolean)
Sort direction.
**Default:** `true` (descending)
**Possible values:** `true`, `false`, `1`, `0`

## Combined Filter Examples

### Example 1: Search with Category and Status Filter
```
GET /api/admin/products?search=protein&category_id=1&status=active&is_approved=true
```

### Example 2: Brand and Price Range Filter
```
GET /api/admin/products?brand_id=5,6&min_price=100&max_price=500&stock_status=in_stock
```

### Example 3: Complex Multi-Filter Query
```
GET /api/admin/products?category_id=1,2&brand_id=3&stock_availability=in_stock&min_price=50&max_price=200&is_active=true&sortBy=created_at&sortDesc=true&per_page=20
```

### Example 4: Date Range with Search
```
GET /api/admin/products?search=vitamin&created_from=2024-01-01&created_to=2024-06-30&status=active
```

## Response Structure

The API returns a JSON response with the following structure:

```json
{
    "status": true,
    "data": {
        "data": [
            {
                "id": 1,
                "title_en": "Product Title",
                "title_ar": "عنوان المنتج",
                "system_sku": "SKU-12345",
                "regular_price": "99.99",
                "status": "active",
                "category": {
                    "id": 1,
                    "name": "Category Name",
                    "name_ar": "اسم الفئة"
                },
                "brand": {
                    "id": 1,
                    "name_en": "Brand Name",
                    "name_ar": "اسم العلامة التجارية"
                },
                "inventory": {
                    "stock": 100,
                    "stock_status": "in_stock"
                }
            }
        ],
        "current_page": 1,
        "per_page": 10,
        "total_items": 150,
        "total_pages": 15
    },
    "message": "Product data retrieved successfully!"
}
```

## Performance Optimizations

1. **Optimized Relationships**: Only necessary fields are loaded from related models
2. **Efficient Queries**: Uses proper indexing and avoids N+1 query problems
3. **Conditional Loading**: Relationships are only loaded when needed
4. **Database Indexing**: Ensure proper indexes on filtered fields for optimal performance

## Validation Rules

All filter parameters are optional and validated as follows:
- IDs must be positive integers
- Booleans accept `true`, `false`, `1`, `0`
- Dates must be in `YYYY-MM-DD` format
- Price values must be valid decimals
- Enum fields only accept predefined values
- Array parameters can be comma-separated strings or actual arrays

## Notes

- All filters can be used independently or in combination
- Search is case-insensitive and uses partial matching
- Multiple values for the same filter use OR logic (e.g., `category_id=1,2` means category 1 OR 2)
- Different filters use AND logic (e.g., `category_id=1&status=active` means category 1 AND active status)
- Stock filters require products to have inventory records
