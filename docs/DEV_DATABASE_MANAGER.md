# Development Database Manager

A secure web interface for executing database migration and seeding commands during development when direct terminal access is not available.

## 🔒 Security Features

### Environment Protection
- **Only accessible in development environments** (`local` or `development`)
- Automatically returns 404 in production environments
- No way to bypass environment checks

### Static Credential Authentication
- Uses configurable static credentials for access
- Credentials are stored in environment variables
- Default credentials: `admin` / `dev123!@#`

### Command Restrictions
- Only allows predefined, safe artisan commands
- Prevents execution of arbitrary system commands
- Includes confirmation for destructive operations

## 🚀 Setup Instructions

### 1. Environment Configuration

Add these variables to your `.env` file:

```env
# Development Database Manager Credentials
DEV_DB_USERNAME=your_username
DEV_DB_PASSWORD=your_secure_password
```

If not set, defaults to:
- Username: `admin`
- Password: `dev123!@#`

### 2. Ensure Development Environment

Make sure your `.env` file has:
```env
APP_ENV=local
# or
APP_ENV=development
```

## 📱 Usage

### Access the Interface

Navigate to: `http://your-domain/dev/db-manager`

### Available Commands

The interface supports these artisan commands:

#### Database Operations
- `migrate:fresh --seed` - Reset database and run seeders
- `migrate:fresh` - Reset database without seeders
- `migrate` - Run pending migrations
- `migrate:rollback` - Rollback last migration batch
- `migrate:reset` - Rollback all migrations
- `db:seed` - Run database seeders

#### Cache Operations
- `cache:clear` - Clear application cache
- `config:clear` - Clear configuration cache
- `route:clear` - Clear route cache
- `view:clear` - Clear compiled views
- `optimize:clear` - Clear all cached data

#### Other Operations
- `storage:link` - Create symbolic link for storage
- `queue:work --stop-when-empty` - Process queue jobs

### Quick Actions

- **Quick Refresh Button**: Executes `migrate:fresh --seed` with one click
- **Command Selector**: Choose from dropdown of available commands
- **Destruction Confirmation**: Required checkbox for destructive operations

## 🛡️ Safety Features

### Destructive Command Protection
Commands that can destroy data require explicit confirmation:
- `migrate:fresh --seed`
- `migrate:fresh`
- `migrate:reset`

### Process Management
- 5-minute timeout for long-running commands
- Real-time output display
- Proper error handling and reporting
- Exit code reporting

### Input Validation
- Command whitelist validation
- Credential verification
- CSRF protection
- Environment checks

## 🔧 Technical Details

### Routes
- `GET /dev/db-manager` - Main interface
- `POST /dev/execute-command` - Execute selected command
- `POST /dev/quick-refresh` - Quick migrate:fresh --seed

### Response Format
```json
{
    "success": true|false,
    "message": "Status message",
    "output": "Command output",
    "exit_code": 0
}
```

### Error Handling
- Network errors are caught and displayed
- Process failures are properly reported
- Authentication errors return 401 status
- Invalid commands return 400 status

## 🚨 Security Considerations

### Production Safety
- **Never deploy with these routes enabled in production**
- Environment checks prevent production access
- Consider removing routes entirely for production builds

### Credential Management
- Use strong, unique passwords
- Store credentials in environment variables only
- Never commit credentials to version control
- Consider rotating credentials regularly

### Access Control
- Only provide credentials to authorized developers
- Monitor access logs if available
- Consider IP restrictions for additional security

## 🐛 Troubleshooting

### Common Issues

**404 Error**
- Check `APP_ENV` is set to `local` or `development`
- Ensure routes are properly cached: `php artisan route:clear`

**Authentication Failed**
- Verify credentials in `.env` file
- Check for typos in username/password
- Ensure environment variables are loaded

**Command Not Allowed**
- Only predefined commands are permitted
- Check the allowed commands list in the code
- Request additional commands if needed

**Timeout Errors**
- Commands have a 5-minute timeout
- Large database operations may need optimization
- Consider running commands in smaller batches

### Debug Mode
Enable debug mode in `.env` for detailed error messages:
```env
APP_DEBUG=true
```

## 📝 Customization

### Adding New Commands
Edit the `$allowedCommands` array in `routes/web.php`:

```php
$allowedCommands = [
    'migrate:fresh --seed',
    'your:custom:command',
    // ... other commands
];
```

### Changing Timeout
Modify the timeout value in the route handler:
```php
$process->setTimeout(600); // 10 minutes
```

### Custom Styling
Edit the CSS in `resources/views/dev/db-manager.blade.php` to match your preferences.

## ⚠️ Important Notes

1. **Development Only**: This tool is designed for development environments only
2. **Backup First**: Always backup your database before running destructive commands
3. **Test Thoroughly**: Test commands in a safe environment before using on important data
4. **Monitor Usage**: Keep track of who has access and when commands are executed
5. **Remove for Production**: Ensure these routes are not accessible in production

## 📞 Support

If you encounter issues or need additional features:
1. Check the troubleshooting section above
2. Review Laravel logs for detailed error information
3. Ensure all dependencies are properly installed
4. Contact your development team for assistance
