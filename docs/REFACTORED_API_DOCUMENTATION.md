# Refactored Category Page APIs Documentation

## Overview

The CategoryPageService has been successfully refactored into three separate, focused APIs for better performance and flexibility:

1. **Category Information API** - Returns category details and navigation data
2. **Filters API** - Returns dynamic filters based on context
3. **Universal Products API** - Returns products with comprehensive filtering

## Benefits

- **Improved Performance**: Client applications can fetch only the data they need
- **Better Caching**: Each API can be cached independently based on its update frequency
- **Reduced Data Transfer**: Smaller, focused responses reduce bandwidth usage
- **Enhanced Flexibility**: APIs can be used independently across different pages
- **Better Scalability**: Each service can be optimized and scaled independently

---

## 1. Category Information API

### Endpoints

#### Get Category Information
```
GET /api/client/categories/{category_slug}
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "category": {
      "id": 1,
      "name_en": "VHMS",
      "name_ar": null,
      "slug": "vhms",
      "banner_url": null,
      "cover_image_url": null,
      "icon_url": null,
      "description_en": null,
      "description_ar": null
    },
    "breadcrumb": [
      {
        "name_en": "Home",
        "name_ar": "الرئيسية",
        "url": "/"
      },
      {
        "name_en": "VHMS",
        "name_ar": null,
        "url": "/web/categories/vhms"
      }
    ],
    "subcategories": [
      {
        "id": 6,
        "name_en": "Antioxidants",
        "name_ar": null,
        "slug": "antioxidants",
        "icon_url": null,
        "cover_image_url": null,
        "product_count": 0
      }
    ]
  },
  "message": "Category information retrieved successfully"
}
```

#### Get Subcategory Information
```
GET /api/client/subcategories/{subcategory_slug}
```

#### Get All Categories
```
GET /api/client/categories
```

#### Get Subcategories by Parent
```
GET /api/client/categories/{parent_id}/subcategories
```

---

## 2. Filters API

### Endpoint

```
GET /api/client/filters
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `category_id` | integer | Filter context by category |
| `subcategory_id` | integer | Filter context by subcategory |
| `brand_id` | integer | Filter context by brand |
| `search` | string | Filter context by search term |

### Response Example

```json
{
  "status": true,
  "data": {
    "brands": [
      {
        "id": 1,
        "name_en": "Spencer-Grimes",
        "name_ar": "Bergnaum-Kub",
        "product_count": 3
      }
    ],
    "user_groups": [
      {
        "id": 9,
        "value_en": "UniSex",
        "value_ar": "محايد",
        "product_count": 5
      }
    ],
    "countries_of_origin": [
      {
        "id": 298,
        "value_en": "United States",
        "value_ar": "الولايات المتحدة الأمريكية",
        "product_count": 4
      }
    ],
    "formulations": [],
    "flavours": [],
    "storage_conditions": [
      {
        "id": 288,
        "value_en": "Store in Cool, Dry Place",
        "value_ar": "يحفظ في مكان بارد وجاف",
        "product_count": 5
      }
    ],
    "return_policies": [
      {
        "id": 323,
        "value_en": "Yes Within 7 Days",
        "value_ar": "نعم خلال 7 أيام",
        "product_count": 7
      }
    ],
    "warranties": [
      {
        "id": 326,
        "value_en": "No Warranty",
        "value_ar": "بدون ضمان",
        "product_count": 7
      }
    ],
    "price_range": {
      "min_price": "89.00",
      "max_price": "179.00"
    },
    "boolean_filters": {
      "vegan": {
        "label_en": "Vegan",
        "label_ar": "نباتي",
        "product_count": "0"
      },
      "vegetarian": {
        "label_en": "Vegetarian",
        "label_ar": "نباتي",
        "product_count": "6"
      },
      "halal": {
        "label_en": "Halal",
        "label_ar": "حلال",
        "product_count": "6"
      }
    }
  },
  "message": "Filters retrieved successfully"
}
```

---

## 3. Universal Products API

### Endpoint

```
GET /api/client/products
```

### Query Parameters

#### Context Filters
| Parameter | Type | Description |
|-----------|------|-------------|
| `category_id` | integer | Filter by category |
| `subcategory_id` | integer | Filter by subcategory |
| `brand_id` | array | Filter by brand(s) |

#### Product Filters
| Parameter | Type | Description |
|-----------|------|-------------|
| `user_group_id` | array | Filter by user group(s) |
| `country_of_origin_id` | array | Filter by country of origin |
| `formulation_id` | integer | Filter by formulation |
| `flavour_id` | integer | Filter by flavour |
| `storage_conditions_id` | integer | Filter by storage conditions |
| `is_returnable_id` | integer | Filter by return policy |
| `warranty_id` | integer | Filter by warranty |
| `min_price` | numeric | Minimum price filter |
| `max_price` | numeric | Maximum price filter |
| `is_vegan` | boolean | Filter vegan products |
| `is_vegetarian` | boolean | Filter vegetarian products |
| `is_halal` | boolean | Filter halal products |

#### Search & Sorting
| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search term |
| `sort_by` | string | Sort field (created_at, title_en, title_ar, regular_price, offer_price, short_name) |
| `sort_order` | string | Sort order (asc, desc) |

#### Pagination
| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number |
| `per_page` | integer | Items per page (max 50) |

### Response Example

```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 62,
        "title_en": "Vitamin D3 5000 IU",
        "title_ar": "فيتامين د3 5000 وحدة دولية",
        "short_name": "Vitamin D3",
        "regular_price": "89.00",
        "offer_price": "75.00",
        "primary_image": {
          "path_url": "https://uaecommerce.s3.ap-southeast-1.amazonaws.com/uploads/fdbadcf8-1ea5-44b5-8a39-f71c0d19196a.png",
          "alt_text": "Product Image"
        },
        "brand": {
          "id": 1,
          "name_en": "Spencer-Grimes",
          "name_ar": "Bergnaum-Kub"
        },
        "user_group": {
          "id": 9,
          "value_en": "UniSex",
          "value_ar": "محايد"
        },
        "country_of_origin": {
          "id": 298,
          "value_en": "United States",
          "value_ar": "الولايات المتحدة الأمريكية"
        }
      }
    ],
    "current_page": 1,
    "per_page": 2,
    "total_items": 7,
    "total_pages": 4,
    "has_more_pages": true
  },
  "message": "Products retrieved successfully"
}
```

---

## Usage Examples

### Category Page Implementation

```javascript
// 1. Load category information
const categoryInfo = await fetch('/api/client/categories/vhms');

// 2. Load filters for the category
const filters = await fetch('/api/client/filters?category_id=1');

// 3. Load products for the category
const products = await fetch('/api/client/products?category_id=1&per_page=20');
```

### Search Page Implementation

```javascript
// 1. Load filters for search context
const filters = await fetch('/api/client/filters?search=vitamin');

// 2. Load products with search and filters
const products = await fetch('/api/client/products?search=vitamin&brand_id[]=1&brand_id[]=2&min_price=50&max_price=200');
```

### Brand Page Implementation

```javascript
// 1. Load filters for brand context
const filters = await fetch('/api/client/filters?brand_id=1');

// 2. Load products for the brand
const products = await fetch('/api/client/products?brand_id[]=1&sort_by=regular_price&sort_order=asc');
```

---

## Migration Guide

### From Legacy API

**Before (Single API):**
```javascript
const data = await fetch('/api/client/categories/vhms');
// Returns: category, breadcrumb, subcategories, products, filters
```

**After (Focused APIs):**
```javascript
// Load only what you need
const categoryInfo = await fetch('/api/client/categories/vhms');
const filters = await fetch('/api/client/filters?category_id=1');
const products = await fetch('/api/client/products?category_id=1');
```

### Route Changes

The original route paths now point to the new focused APIs:
- `GET /api/client/categories/{category_slug}` - Now returns only category information (no products/filters)
- `GET /api/client/subcategories/{subcategory_slug}` - Now returns only subcategory information (no products/filters)
- `GET /api/client/products` - New universal products endpoint
- `GET /api/client/filters` - New filters endpoint

---

## Performance Benefits

1. **Reduced Initial Load Time**: Category information loads immediately without waiting for products
2. **Independent Caching**: Each API can be cached with different TTL values
3. **Lazy Loading**: Products and filters can be loaded on-demand
4. **Parallel Requests**: Multiple APIs can be called simultaneously
5. **Reduced Bandwidth**: Only fetch the data you actually need

## Error Handling

All APIs return consistent error responses:

```json
{
  "status": false,
  "message": "Error message",
  "data": null
}
```

Common HTTP status codes:
- `200` - Success
- `404` - Resource not found
- `422` - Validation error
- `500` - Internal server error
