# Support Ticket System Enhancement Documentation

## Overview
This document outlines the enhancements made to the multivendor e-commerce platform's support ticket system based on client feedback. The system now includes automatic ticket code generation, auto-routing, admin CC functionality, and threaded messaging.

## Enhanced Features

### 1. Ticket Code Generation System

#### Format: `[Prefix][YYMMDD][6-digit serial]`
- **Example**: `CV250629000125`
- **Components**:
  - Prefix (2 chars): Indicates sender/receiver relationship
  - Date (6 chars): YYMMDD format
  - Serial (6 chars): Daily sequence number with leading zeros

#### Prefix Mapping
| Prefix | Meaning | Description |
|--------|---------|-------------|
| `CA` | Customer → Admin | Customer tickets directed to admin |
| `CV` | Customer → Vendor | Customer tickets directed to vendor |
| `CT` | Customer → TPL | Customer tickets directed to TPL service provider |
| `VA` | Vendor → Admin | Vendor tickets directed to admin |
| `TA` | TPL → Admin | TPL tickets directed to admin |

#### Implementation Details
- Automatic generation on ticket creation
- Daily sequence reset (starts from 000001 each day)
- Unique constraint on ticket codes
- Prefix determination based on reason routing or sender/receiver relationship

### 2. Support Reason Auto-Assignment

#### Features
- Dropdown selection system for support reasons
- Automatic routing to correct resolver based on selected reason
- Pre-configured reasons with routing rules

#### Routing Logic
```php
// Auto-assignment based on reason routing
switch ($reason->route_to) {
    case 'vendor':
        $ticket->assigned_to = $vendor->user_id;
        break;
    case 'tpl':
        $ticket->assigned_to = $tpl->user_id;
        break;
    case 'admin':
    default:
        $ticket->assigned_to = $admin->id;
        break;
}
```

#### Pre-configured Support Reasons
- **Customer → Admin**: Account Issues, Payment Problems, Technical Support
- **Customer → Vendor**: Product Quality, Refund Requests, Product Information
- **Customer → TPL**: Delivery Issues, Shipping Delays, Package Damage
- **Vendor → Admin**: Commission Disputes, Platform Issues, Policy Questions
- **TPL → Admin**: Service Agreement Issues, Payment Disputes, System Integration

### 3. Admin CC System

#### Features
- Admin receives copy of ALL tickets directed to vendors or TPL providers
- Admin does NOT receive CC for tickets they are the primary recipient of
- Automatic notification system

#### Implementation
```php
// Admin CC logic
if (in_array($reason->route_to, ['vendor', 'tpl'])) {
    $adminUsers = User::role('admin')->get();
    foreach ($adminUsers as $admin) {
        // Create notification record
        Notification::create([
            'type' => 'support_ticket_cc',
            'notifiable_id' => $admin->id,
            'data' => [
                'ticket_id' => $ticket->id,
                'ticket_code' => $ticket->code,
                'subject' => $ticket->subject,
                'assigned_to' => $ticket->assigned_to,
                'route_to' => $reason->route_to
            ]
        ]);
    }
}
```

### 4. Threaded Message System

#### Features
- Proper message threading within tickets
- Follow-up messages and structured conversations
- Message order maintenance
- Read status tracking
- Automatic ticket status updates

#### New API Endpoint
```
GET /api/admin/support-tickets/{ticketId}/messages
```

#### Message Threading Logic
- Messages ordered chronologically
- Read status updates for current user
- Automatic ticket status change from 'resolved'/'closed' to 'in_progress' on new messages
- Notification system for all relevant parties

## Database Schema Changes

### New Migrations
1. `2025_06_29_120000_add_reason_id_to_support_tickets_table.php`
   - Adds `reason_id` foreign key to support_tickets
   - Adds `tpl_id` foreign key to support_tickets

2. `2025_06_29_120001_add_code_prefix_to_support_reasons_table.php`
   - Adds `code_prefix` column to support_reasons

### Updated Models

#### SupportReason Model
```php
protected $fillable = [
    'label', 'route_to', 'code_prefix', 'status'
];

// Auto-generate prefix if not set
public function getCodePrefixAttribute($value) {
    if ($value) return $value;
    
    return match($this->route_to) {
        'vendor' => 'CV',
        'tpl' => 'CT', 
        'admin' => 'CA',
        default => 'CA'
    };
}
```

#### SupportTicket Model
- Enhanced code generation logic
- New relationships (tpl, reason)
- Automatic code generation on creation

#### SupportTicketMessage Model
- Enhanced with notification system
- Automatic read status management
- Ticket status updates

## API Compatibility

### Existing Endpoints (Unchanged)
- `GET /api/admin/support-categories`
- `POST /api/admin/support-categories`
- `GET /api/admin/support-topics`
- `POST /api/admin/support-topics`
- `GET /api/admin/support-reasons`
- `POST /api/admin/support-reasons`
- `GET /api/admin/support-tickets`
- `POST /api/admin/support-tickets`
- `GET /api/admin/support-ticket-messages`
- `POST /api/admin/support-ticket-messages`

### New Endpoints
- `GET /api/admin/support-tickets/{ticketId}/messages` - Get threaded messages
- `GET /api/admin/support-reasons/active-list` - Get active support reasons for dropdown selection

### Enhanced Request/Response

#### Creating Support Tickets
```json
// Request
{
    "subject": "Product Quality Issue",
    "message": "Product received is damaged",
    "reason_id": 4,
    "vendor_id": 123,
    "category_id": 1,
    "topic_id": 2
}

// Response
{
    "data": {
        "id": 1,
        "code": "CV250629000001",
        "subject": "Product Quality Issue",
        "status": "open",
        "assigned_to": 456,
        "reason": {
            "id": 4,
            "label": "Product Quality Issues",
            "route_to": "vendor"
        }
    }
}
```

#### Active Support Reasons List
```json
// GET /api/admin/support-reasons/active-list
// Response
{
    "status": true,
    "message": "Active support reasons retrieved successfully!",
    "data": [
        {
            "id": 1,
            "label": "Account Issues",
            "route_to": "admin",
            "code_prefix": "CA"
        },
        {
            "id": 2,
            "label": "Product Quality Issues",
            "route_to": "vendor",
            "code_prefix": "CV"
        },
        {
            "id": 3,
            "label": "Delivery Issues",
            "route_to": "tpl",
            "code_prefix": "CT"
        }
    ]
}
```

## Testing

### Test Coverage
- Ticket code generation accuracy
- Auto-assignment logic
- Admin CC functionality
- Threaded messaging
- API compatibility
- Code uniqueness validation

### Manual Testing Checklist
1. Create tickets with different reasons
2. Verify correct code generation
3. Check auto-assignment works
4. Confirm admin receives CC notifications
5. Test message threading
6. Verify backward compatibility

## Deployment Notes

### Required Steps
1. Run migrations: `php artisan migrate`
2. Seed support reasons: `php artisan db:seed --class=SupportReasonSeeder`
3. Clear cache: `php artisan cache:clear`
4. Update frontend/mobile apps to handle new response fields (optional)

### Backward Compatibility
- All existing API endpoints remain functional
- New fields are optional in requests
- Response structure maintains existing fields
- Mobile/frontend integrations continue to work without changes

## Future Enhancements

### Potential Improvements
1. Email notifications for admin CC
2. Real-time notifications via WebSocket
3. Ticket escalation system
4. SLA tracking and alerts
5. Advanced reporting and analytics
6. File attachment support in messages
7. Ticket templates for common issues

### Performance Considerations
- Index on ticket codes for fast lookups
- Pagination for message threads
- Caching for frequently accessed support reasons
- Background job processing for notifications

## Support and Maintenance

### Monitoring
- Track ticket creation rates
- Monitor code generation performance
- Watch for notification delivery issues
- Check message threading accuracy

### Troubleshooting
- Verify role permissions for auto-assignment
- Check notification table for CC records
- Validate ticket code uniqueness
- Monitor message ordering in threads
