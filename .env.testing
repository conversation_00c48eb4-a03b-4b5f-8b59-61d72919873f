APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:od+K/e/uIFbyYr3QSskpXxlI8wc5RmF3fy+D0tHTmoQ=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Testing Database Configuration
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
DB_FOREIGN_KEYS=false

SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array
CACHE_PREFIX=

MAIL_MAILER=array
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Disable external services for testing
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_URL=

VITE_APP_NAME="${APP_NAME}"

# Passport for testing
PASSPORT_PASSWORD_CLIENT_ID=9ef2435a-9f0c-4a2e-b55e-d45e03efdb89
PASSPORT_PASSWORD_SECRET=0wue2LDJryzH7jdhzikWhZH136DU3JWEWewd81U1

# Testing specific settings
TELESCOPE_ENABLED=false
PULSE_ENABLED=false
