<?php

namespace Tests\Feature;

use App\Models\Coupon;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CouponTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role for testing
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'api']);

        // Create a user for authentication with admin role
        $this->user = User::factory()->create();
        $this->user->assignRole('admin');

        // Create a vendor for testing vendor-specific coupons
        $this->vendor = Vendor::factory()->create();
    }

    /** @test */
    public function it_can_list_coupons()
    {
        // Create some coupons
        Coupon::factory()->count(5)->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'code',
                            'title',
                            'title_ar',
                            'type',
                            'value',
                            'is_active',
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_create_a_coupon()
    {
        $couponData = [
            'code' => 'SAVE20',
            'title_en' => '20% Off Everything',
            'title_ar' => 'خصم 20% على كل شيء',
            'description_en' => 'Get 20% off on all products',
            'description_ar' => 'احصل على خصم 20% على جميع المنتجات',
            'type' => 'percentage',
            'value' => 20,
            'min_order_value' => 100,
            'usage_limit' => 1000,
            'per_user_limit' => 1,
            'vendor_id' => null, // Platform-wide coupon
            'start_date' => now()->addDay()->format('Y-m-d H:i:s'),
            'end_date' => now()->addYear()->format('Y-m-d H:i:s'),
            'is_active' => true,
        ];

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons', $couponData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'code',
                    'title',
                    'title_ar',
                    'type',
                    'value',
                    'is_active',
                ]
            ]);

        $this->assertDatabaseHas('coupons', [
            'code' => 'SAVE20',
            'title_en' => '20% Off Everything',
            'type' => 'percentage',
            'value' => 20,
        ]);
    }

    /** @test */
    public function it_can_show_a_coupon()
    {
        $coupon = Coupon::factory()->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/admin/coupons/{$coupon->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'code',
                    'title',
                    'title_ar',
                    'type',
                    'value',
                    'is_active',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $coupon->id,
                    'code' => $coupon->code,
                    'title_en' => $coupon->title_en,
                ]
            ]);
    }

    /** @test */
    public function it_can_update_a_coupon()
    {
        $coupon = Coupon::factory()->create();

        $updateData = [
            'title_en' => 'Updated Coupon Title',
            'value' => 25,
            'is_active' => false,
        ];

        $response = $this->actingAs($this->user, 'api')
            ->putJson("/api/admin/coupons/{$coupon->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'title_en',
                    'value',
                    'is_active',
                ]
            ]);

        $this->assertDatabaseHas('coupons', [
            'id' => $coupon->id,
            'title_en' => 'Updated Coupon Title',
            'value' => 25,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_delete_a_coupon()
    {
        $coupon = Coupon::factory()->create();

        $response = $this->actingAs($this->user, 'api')
            ->deleteJson("/api/admin/coupons/{$coupon->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Coupon deleted successfully!',
            ]);

        $this->assertDatabaseMissing('coupons', [
            'id' => $coupon->id,
        ]);
    }



    /** @test */
    public function it_can_validate_coupon_code()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'VALID20',
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
        ]);

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons/validate', [
                'code' => 'VALID20',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Coupon is valid!',
            ]);
    }

    /** @test */
    public function it_can_toggle_coupon_status()
    {
        $coupon = Coupon::factory()->create(['is_active' => true]);

        $response = $this->actingAs($this->user, 'api')
            ->patchJson("/api/admin/coupons/{$coupon->id}/toggle-status");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'is_active',
                ]
            ]);

        $this->assertDatabaseHas('coupons', [
            'id' => $coupon->id,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_coupon()
    {
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['code', 'title_en', 'type', 'value']);
    }

    /** @test */
    public function it_validates_unique_coupon_code()
    {
        $existingCoupon = Coupon::factory()->create(['code' => 'EXISTING']);

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons', [
                'code' => 'EXISTING',
                'title_en' => 'Test Coupon',
                'type' => 'percentage',
                'value' => 10,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['code']);
    }

    /** @test */
    public function it_validates_percentage_value_limit()
    {
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons', [
                'code' => 'INVALID',
                'title' => 'Test Coupon',
                'type' => 'percentage',
                'value' => 150, // Invalid: over 100%
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['value']);
    }

    /** @test */
    public function it_can_search_coupons_by_various_fields()
    {
        // Create coupons with different searchable content
        $coupon1 = Coupon::factory()->create([
            'code' => 'SEARCH123',
            'title_en' => 'Test Coupon',
            'title_ar' => 'كوبون تجريبي',
            'description_en' => 'This is a test description',
            'description_ar' => 'هذا وصف تجريبي',
        ]);

        $coupon2 = Coupon::factory()->create([
            'code' => 'ANOTHER456',
            'title_en' => 'Another Coupon',
            'title_ar' => 'كوبون آخر',
            'description_en' => 'Different description',
            'description_ar' => 'وصف مختلف',
        ]);

        // Test search by code
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?search=SEARCH123');

        $response->assertStatus(200);
        $data = $response->json('data.data');
        $this->assertCount(1, $data);
        $this->assertEquals('SEARCH123', $data[0]['code']);

        // Test search by title_en
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?search=test');

        $response->assertStatus(200);
        $data = $response->json('data.data');
        $this->assertGreaterThanOrEqual(1, count($data));

        // Test search by description_en (this tests the fix)
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?search=description');

        $response->assertStatus(200);
        $data = $response->json('data.data');
        $this->assertGreaterThanOrEqual(1, count($data));
    }

    /** @test */
    public function it_can_filter_coupons_by_is_active_status()
    {
        // Create active and inactive coupons
        $activeCoupon = Coupon::factory()->create([
            'code' => 'ACTIVE123',
            'is_active' => true,
        ]);

        $inactiveCoupon = Coupon::factory()->create([
            'code' => 'INACTIVE456',
            'is_active' => false,
        ]);

        // Test filter by is_active=1 (active coupons)
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?is_active=1');

        $response->assertStatus(200);
        $data = $response->json('data.data');

        // Verify all returned coupons are active
        foreach ($data as $coupon) {
            $this->assertTrue($coupon['is_active'], 'All coupons should be active when filtering by is_active=1');
        }

        // Test filter by is_active=0 (inactive coupons)
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?is_active=0');

        $response->assertStatus(200);
        $data = $response->json('data.data');

        // Verify all returned coupons are inactive
        foreach ($data as $coupon) {
            $this->assertFalse($coupon['is_active'], 'All coupons should be inactive when filtering by is_active=0');
        }

        // Test combined filter: search + is_active
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons?search=ACTIVE&is_active=1');

        $response->assertStatus(200);
        $data = $response->json('data.data');

        // Should find the active coupon with 'ACTIVE' in the code
        $this->assertGreaterThanOrEqual(1, count($data));
        foreach ($data as $coupon) {
            $this->assertTrue($coupon['is_active'], 'Filtered coupons should be active');
            $this->assertStringContainsString('ACTIVE', $coupon['code'], 'Filtered coupons should contain search term');
        }
    }

    /** @test */
    public function it_can_get_coupon_statistics()
    {
        // Create some test coupons
        Coupon::factory()->count(5)->active()->create();
        Coupon::factory()->count(2)->inactive()->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data',
            ]);
    }

    /** @test */
    public function it_returns_404_for_non_existent_coupon()
    {
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/coupons/99999');

        $response->assertStatus(404);
    }

    /** @test */
    public function it_validates_invalid_coupon_code()
    {
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/coupons/validate', [
                'code' => 'NONEXISTENT',
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'status' => false,
                'message' => 'Coupon not found or expired',
            ]);
    }

    /** @test */
    public function it_handles_string_boolean_values_in_multipart_requests()
    {
        // Test with string "true"
        $couponData = [
            'code' => 'BOOL_TEST',
            'title_en' => 'Boolean Test Coupon',
            'type' => 'fixed',
            'value' => 25,
            'is_active' => 'true', // String instead of boolean
        ];

        $response = $this->actingAs($this->user, 'api')
            ->post('/api/admin/coupons', $couponData, [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('coupons', [
            'code' => 'BOOL_TEST',
            'is_active' => true, // Should be converted to boolean true
        ]);

        // Test with string "false"
        $couponData2 = [
            'code' => 'BOOL_TEST2',
            'title_en' => 'Boolean Test Coupon 2',
            'type' => 'fixed',
            'value' => 25,
            'is_active' => 'false', // String instead of boolean
        ];

        $response2 = $this->actingAs($this->user, 'api')
            ->post('/api/admin/coupons', $couponData2, [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]);

        $response2->assertStatus(201);

        $this->assertDatabaseHas('coupons', [
            'code' => 'BOOL_TEST2',
            'is_active' => false, // Should be converted to boolean false
        ]);
    }
}
