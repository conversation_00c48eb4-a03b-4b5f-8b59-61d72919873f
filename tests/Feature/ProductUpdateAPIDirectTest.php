<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductUpdateAPIDirectTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_with_your_complete_payload()
    {
        // Create minimal test data using factories
        $user = User::factory()->create();
        $product = Product::factory()->create(['user_id' => $user->id]);
        
        $this->actingAs($user);

        // Your exact payload
        $payload = [
            "vendor_id" => 1,
            "category_id" => 2,
            "sub_category_id" => 3,
            "class_id" => 1,
            "sub_class_id" => 2,
            "brand_id" => 5,
            "vendor_sku" => "VSKU-12345",
            "system_sku" => "SSKU-54321",
            "barcode" => "1234567890123",
            "model_number" => "MDL-2025",
            "title_en" => "Sample Product Title",
            "title_ar" => "عنوان المنتج",
            "short_name" => "Sample Short Name",
            "short_description_en" => "A short description in English.",
            "short_description_ar" => "وصف قصير بالعربية.",
            "description_en" => "Full product description in English.",
            "description_ar" => "الوصف الكامل للمنتج بالعربية.",
            "key_ingredients" => "Ingredient1, Ingredient2",
            "usage_instructions" => "Use as directed.",
            "user_group_id" => 1,
            "net_weight_id" => 1,
            "net_weight_unit_id" => 1,
            "formulation_id" => 1,
            "servings" => 30,
            "flavour_id" => 1,
            "is_variant" => true, // Changed to true to test variants
            "has_varient" => true,
            "regular_price" => 99.99,
            "offer_price" => 89.99,
            "vat_tax" => "5%",
            "discount_start_date" => "2025-07-10",
            "discount_end_date" => "2025-07-20",
            "approx_commission" => 10.00,
            "dietary_need_ids" => [1, 2],
            "is_vegan" => true,
            "is_vegetarian" => false,
            "is_halal" => true,
            "allergen_info_ids" => [3, 4],
            "storage_conditions" => "Store in a cool, dry place.",
            "vat_tax_utl" => "VAT-UTL-001",
            "regulatory_product_registration" => "REG-2025-001",
            "country_of_origin" => "UAE",
            "bbe_date" => "2026-12-31",
            "fulfillment_id" => 1,
            "package_length" => 10.5,
            "package_width" => 8.2,
            "package_height" => 5.0,
            "package_weight" => 0.75,
            "is_active" => true,
            "is_approved" => false,
            "status" => "pending",
            "product_seo" => [
                "meta_title_en" => "SEO Title EN",
                "meta_description_en" => "SEO Description EN",
                "keywords_en" => "keyword1, keyword2",
                "meta_title_ar" => "عنوان سيو",
                "meta_description_ar" => "وصف سيو",
                "keywords_ar" => "كلمة1, كلمة2"
            ],
            "product_faqs" => [
                [
                    "question" => "What is the shelf life?",
                    "answer" => "2 years from the date of manufacture."
                ],
                [
                    "question" => "Is it suitable for vegans?",
                    "answer" => "Yes, this product is 100% vegan."
                ]
            ],
            "product_variants" => [
                [
                    "regular_price" => 99.99,
                    "offer_price" => 89.99,
                    "vat_tax" => "5%",
                    "discount_start_date" => "2025-07-10",
                    "discount_end_date" => "2025-07-20",
                    "stock" => 100,
                    "reserved" => 10,
                    "threshold" => 5,
                    "location" => "Warehouse A",
                    "note" => "Best seller",
                    "sku" => "VSKU-12345-RED",
                    "barcode" => "1234567890124",
                    "weight" => 0.75,
                    "length" => 10.5,
                    "width" => 8.2,
                    "height" => 5.0,
                    "path" => "images/variant1.jpg",
                    "is_active" => 1,
                    "stock_status" => "in_stock",
                    "attribute_id" => 1,
                    "attribute_value_id" => 2
                ]
            ]
        ];

        // Make the API call
        $response = $this->putJson("/api/admin/products/{$product->id}", $payload);

        // Output results
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "🧪 TESTING PRODUCT UPDATE API WITH COMPLETE PAYLOAD\n";
        echo str_repeat("=", 80) . "\n";
        
        echo "📤 Request URL: PUT /api/admin/products/{$product->id}\n";
        echo "📤 Payload Size: " . count($payload) . " fields\n";
        echo "📤 Variants Count: " . count($payload['product_variants']) . "\n";
        
        echo "\n📥 RESPONSE:\n";
        echo "Status Code: " . $response->status() . "\n";
        
        if ($response->status() === 200) {
            echo "✅ SUCCESS! API call completed successfully\n";
            
            $responseData = $response->json();
            echo "✅ Response has 'status': " . ($responseData['status'] ? 'true' : 'false') . "\n";
            echo "✅ Response message: " . ($responseData['message'] ?? 'N/A') . "\n";
            
            // Check if variants were created
            $variantCount = ProductVariant::where('product_id', $product->id)->count();
            echo "✅ Variants created in database: {$variantCount}\n";
            
            // Check main product fields
            $updatedProduct = Product::find($product->id);
            echo "✅ Product title updated: " . $updatedProduct->title_en . "\n";
            echo "✅ Product is_variant: " . ($updatedProduct->is_variant ? 'true' : 'false') . "\n";
            echo "✅ VAT tax UTL: " . ($updatedProduct->vat_tax_utl ?? 'null') . "\n";
            
        } else {
            echo "❌ FAILED! API call returned error\n";
            echo "❌ Response Body:\n" . $response->getContent() . "\n";
            
            if ($response->status() === 422) {
                $errors = $response->json('errors');
                if ($errors) {
                    echo "❌ Validation Errors:\n";
                    foreach ($errors as $field => $messages) {
                        echo "  - {$field}: " . implode(', ', $messages) . "\n";
                    }
                }
            }
        }
        
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "🏁 TEST COMPLETED\n";
        echo str_repeat("=", 80) . "\n\n";

        // Assert for PHPUnit
        if ($response->status() === 200) {
            $this->assertTrue(true, "API test passed");
        } else {
            $this->fail("API test failed with status: " . $response->status());
        }
    }

    public function test_variant_detection_scenarios()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create(['user_id' => $user->id]);
        $this->actingAs($user);

        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🧪 TESTING VARIANT DETECTION LOGIC\n";
        echo str_repeat("=", 60) . "\n";

        $testCases = [
            [
                'name' => 'is_variant = true',
                'payload' => [
                    'is_variant' => true,
                    'has_varient' => false,
                    'title_en' => 'Test Product 1',
                    'product_variants' => [
                        ['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1, 'sku' => 'TEST-1']
                    ]
                ],
                'should_create_variants' => true
            ],
            [
                'name' => 'has_varient = true',
                'payload' => [
                    'is_variant' => false,
                    'has_varient' => true,
                    'title_en' => 'Test Product 2',
                    'product_variants' => [
                        ['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1, 'sku' => 'TEST-2']
                    ]
                ],
                'should_create_variants' => true
            ],
            [
                'name' => 'product_variants array present',
                'payload' => [
                    'is_variant' => false,
                    'has_varient' => false,
                    'title_en' => 'Test Product 3',
                    'product_variants' => [
                        ['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1, 'sku' => 'TEST-3']
                    ]
                ],
                'should_create_variants' => true
            ]
        ];

        foreach ($testCases as $index => $testCase) {
            // Clear existing variants
            ProductVariant::where('product_id', $product->id)->delete();
            
            echo "\n📋 Test Case: {$testCase['name']}\n";
            
            $response = $this->putJson("/api/admin/products/{$product->id}", $testCase['payload']);
            
            $variantCount = ProductVariant::where('product_id', $product->id)->count();
            
            if ($testCase['should_create_variants']) {
                if ($variantCount > 0) {
                    echo "✅ PASS: Variants created ({$variantCount})\n";
                } else {
                    echo "❌ FAIL: Expected variants but none created\n";
                }
            } else {
                if ($variantCount === 0) {
                    echo "✅ PASS: No variants created as expected\n";
                } else {
                    echo "❌ FAIL: Unexpected variants created ({$variantCount})\n";
                }
            }
            
            echo "   Response Status: " . $response->status() . "\n";
        }

        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🏁 VARIANT DETECTION TESTS COMPLETED\n";
        echo str_repeat("=", 60) . "\n\n";

        $this->assertTrue(true, "Variant detection tests completed");
    }
}
