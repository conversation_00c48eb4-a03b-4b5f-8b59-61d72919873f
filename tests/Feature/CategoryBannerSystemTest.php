<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Category;
use App\Models\Banner;
use App\Models\BannerItem;
use App\Models\User;
use App\Services\CategoryService;
use App\Services\CategoryInformationService;

class CategoryBannerSystemTest extends TestCase
{
    use RefreshDatabase;

    private $user;
    private $banner;
    private $categoryService;
    private $categoryInformationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with admin role
        $this->user = User::factory()->create();
        
        // Assign admin role to user (assuming Spatie permissions)
        try {
            if (class_exists(\Spatie\Permission\Models\Role::class)) {
                $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
                $this->user->assignRole($adminRole);
            }
        } catch (\Exception $e) {
            // Role system might not be set up
        }
        
        $this->actingAs($this->user, 'api');
        
        // Create a test banner with items
        $this->banner = Banner::create([
            'user_id' => $this->user->id,
            'title' => 'Test Category Banner',
            'description' => 'Test banner for categories',
            'type' => 'category',
            'is_active' => true,
        ]);
        
        // Create banner items
        BannerItem::create([
            'banner_id' => $this->banner->id,
            'user_id' => $this->user->id,
            'title_en' => 'Banner Item 1',
            'title_ar' => 'عنصر البانر 1',
            'media_path' => 'banners/test-image-1.jpg',
            'link_url' => 'https://example.com/1',
            'target' => '_blank',
            'alt_text' => 'Test banner image 1',
            'position' => 1,
            'is_active' => true,
        ]);
        
        BannerItem::create([
            'banner_id' => $this->banner->id,
            'user_id' => $this->user->id,
            'title_en' => 'Banner Item 2',
            'title_ar' => 'عنصر البانر 2',
            'media_path' => 'banners/test-image-2.jpg',
            'link_url' => 'https://example.com/2',
            'target' => '_self',
            'alt_text' => 'Test banner image 2',
            'position' => 2,
            'is_active' => true,
        ]);
        
        $this->categoryService = new CategoryService();
        $this->categoryInformationService = new CategoryInformationService();
    }

    /** @test */
    public function it_can_create_category_with_banner_id()
    {
        $response = $this->postJson('/api/admin/categories', [
            'name_en' => 'Test Category',
            'name_ar' => 'فئة الاختبار',
            'type' => 'main',
            'slug' => 'test-category',
            'banner_id' => $this->banner->id,
            'icon' => 'categories/test-icon.png',
            'meta_title' => 'Test Category Meta',
            'meta_description' => 'Test category description',
        ]);
        
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name_en',
                'name_ar',
                'banner_id',
                'slug'
            ]
        ]);
        
        $this->assertDatabaseHas('categories', [
            'name_en' => 'Test Category',
            'banner_id' => $this->banner->id,
        ]);
    }

    /** @test */
    public function it_can_update_category_with_banner_id()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Original Category',
            'name_ar' => 'الفئة الأصلية',
            'type' => 'main',
            'slug' => 'original-category',
            'banner_id' => null,
        ]);
        
        $response = $this->putJson("/api/admin/categories/{$category->id}", [
            'name_en' => 'Updated Category',
            'name_ar' => 'الفئة المحدثة',
            'type' => 'main',
            'slug' => 'updated-category',
            'banner_id' => $this->banner->id,
        ]);
        
        $response->assertStatus(200);
        $this->assertDatabaseHas('categories', [
            'id' => $category->id,
            'name_en' => 'Updated Category',
            'banner_id' => $this->banner->id,
        ]);
    }

    /** @test */
    public function it_shows_category_with_banner_information()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Category with Banner',
            'name_ar' => 'فئة مع البانر',
            'type' => 'main',
            'slug' => 'category-with-banner',
            'banner_id' => $this->banner->id,
        ]);
        
        $response = $this->getJson("/api/admin/categories/{$category->id}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name_en',
                'banner_id',
                'banner' => [
                    'id',
                    'title',
                    'description',
                    'items' => [
                        '*' => [
                            'id',
                            'title_en',
                            'title_ar',
                            'media_path_url',
                            'link_url',
                            'position'
                        ]
                    ]
                ]
            ]
        ]);
    }

    /** @test */
    public function it_returns_banner_information_in_category_information_endpoint()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Info Category',
            'name_ar' => 'فئة المعلومات',
            'type' => 'main',
            'slug' => 'info-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $response = $this->getJson("/api/client/categories/info-category");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'category' => [
                    'id',
                    'name_en',
                    'name_ar',
                    'banner' => [
                        'id',
                        'title',
                        'description',
                        'items' => [
                            '*' => [
                                'id',
                                'title_en',
                                'title_ar',
                                'media_path_url',
                                'link_url'
                            ]
                        ]
                    ]
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertEquals('Test Category Banner', $responseData['data']['category']['banner']['title']);
        $this->assertCount(2, $responseData['data']['category']['banner']['items']);
    }

    /** @test */
    public function it_returns_banner_information_in_subcategory_information_endpoint()
    {
        $parentCategory = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Parent Category',
            'name_ar' => 'الفئة الأب',
            'type' => 'main',
            'slug' => 'parent-category',
            'status' => 'active',
        ]);
        
        $subcategory = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Sub Category',
            'name_ar' => 'الفئة الفرعية',
            'type' => 'sub',
            'parent_id' => $parentCategory->id,
            'slug' => 'sub-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $response = $this->getJson("/api/client/subcategories/sub-category");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'category' => [
                    'subcategory' => [
                        'id',
                        'name_en',
                        'banner' => [
                            'id',
                            'title',
                            'items'
                        ]
                    ],
                    'parent_category' => [
                        'id',
                        'name_en'
                    ]
                ]
            ]
        ]);
    }

    /** @test */
    public function it_handles_categories_without_banners_gracefully()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Category without Banner',
            'name_ar' => 'فئة بدون بانر',
            'type' => 'main',
            'slug' => 'category-without-banner',
            'banner_id' => null,
            'status' => 'active',
        ]);
        
        $response = $this->getJson("/api/client/categories/category-without-banner");
        
        $response->assertStatus(200);
        $responseData = $response->json();
        $this->assertNull($responseData['data']['category']['banner']);
    }

    /** @test */
    public function it_validates_invalid_banner_id()
    {
        $response = $this->postJson('/api/admin/categories', [
            'name_en' => 'Test Category',
            'name_ar' => 'فئة الاختبار',
            'type' => 'main',
            'slug' => 'test-category-invalid',
            'banner_id' => 99999, // Non-existent banner ID
        ]);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['banner_id']);
    }

    /** @test */
    public function it_allows_null_banner_id()
    {
        $response = $this->postJson('/api/admin/categories', [
            'name_en' => 'Test Category No Banner',
            'name_ar' => 'فئة بدون بانر',
            'type' => 'main',
            'slug' => 'test-category-no-banner',
            'banner_id' => null,
        ]);
        
        $response->assertStatus(201);
        $this->assertDatabaseHas('categories', [
            'name_en' => 'Test Category No Banner',
            'banner_id' => null,
        ]);
    }
}