<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\SupportReason;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Laravel\Passport\Passport;

class SupportReasonActiveListTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin role and user
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'api']);
        
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');
    }

    /** @test */
    public function it_returns_only_active_support_reasons()
    {
        Passport::actingAs($this->admin);

        // Create active support reasons
        $activeReason1 = SupportReason::create([
            'label' => 'Product Quality Issues',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);

        $activeReason2 = SupportReason::create([
            'label' => 'Account Issues',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);

        // Create inactive support reason
        SupportReason::create([
            'label' => 'Inactive Reason',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(2, $responseData);

        // Check that only active reasons are returned
        $labels = array_column($responseData, 'label');
        $this->assertContains('Product Quality Issues', $labels);
        $this->assertContains('Account Issues', $labels);
        $this->assertNotContains('Inactive Reason', $labels);
    }

    /** @test */
    public function it_returns_reasons_ordered_alphabetically_by_label()
    {
        Passport::actingAs($this->admin);

        // Create support reasons in non-alphabetical order
        SupportReason::create([
            'label' => 'Zebra Issue',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);

        SupportReason::create([
            'label' => 'Apple Issue',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);

        SupportReason::create([
            'label' => 'Beta Issue',
            'route_to' => 'tpl',
            'code_prefix' => 'CT',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $labels = array_column($responseData, 'label');
        
        // Check alphabetical order
        $this->assertEquals(['Apple Issue', 'Beta Issue', 'Zebra Issue'], $labels);
    }

    /** @test */
    public function it_returns_essential_fields_for_dropdown()
    {
        Passport::actingAs($this->admin);

        SupportReason::create([
            'label' => 'Test Reason',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(1, $responseData);

        $reason = $responseData[0];
        
        // Check that all essential fields are present
        $this->assertArrayHasKey('id', $reason);
        $this->assertArrayHasKey('label', $reason);
        $this->assertArrayHasKey('route_to', $reason);
        $this->assertArrayHasKey('code_prefix', $reason);
        $this->assertArrayHasKey('auto_assignment', $reason);

        // Check field values
        $this->assertEquals('Test Reason', $reason['label']);
        $this->assertEquals('vendor', $reason['route_to']);
        $this->assertEquals('CV', $reason['code_prefix']);

        // Check auto-assignment information
        $autoAssignment = $reason['auto_assignment'];
        $this->assertArrayHasKey('type', $autoAssignment);
        $this->assertArrayHasKey('description', $autoAssignment);
        $this->assertArrayHasKey('requires_selection', $autoAssignment);

        $this->assertEquals('vendor', $autoAssignment['type']);
        $this->assertEquals('Ticket will be automatically assigned to the selected vendor', $autoAssignment['description']);
        $this->assertTrue($autoAssignment['requires_selection']);
    }

    /** @test */
    public function it_returns_empty_array_when_no_active_reasons_exist()
    {
        Passport::actingAs($this->admin);

        // Create only inactive reasons
        SupportReason::create([
            'label' => 'Inactive Reason',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(0, $responseData);
    }

    /** @test */
    public function it_follows_standard_response_format()
    {
        Passport::actingAs($this->admin);

        SupportReason::create([
            'label' => 'Test Reason',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        
        // Check standard response structure
        $response->assertJsonStructure([
            'status',
            'message',
            'data'
        ]);

        $this->assertTrue($response->json('status'));
        $this->assertEquals('Active support reasons retrieved successfully!', $response->json('message'));
    }

    /** @test */
    public function it_includes_correct_auto_assignment_info_for_different_route_types()
    {
        Passport::actingAs($this->admin);

        // Create reasons for different route types
        SupportReason::create([
            'label' => 'Admin Reason',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);

        SupportReason::create([
            'label' => 'Vendor Reason',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);

        SupportReason::create([
            'label' => 'TPL Reason',
            'route_to' => 'tpl',
            'code_prefix' => 'CT',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');
        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        // Check admin reason
        $adminReason = collect($responseData)->firstWhere('route_to', 'admin');
        $this->assertEquals('admin', $adminReason['auto_assignment']['type']);
        $this->assertEquals('Ticket will be automatically assigned to admin team', $adminReason['auto_assignment']['description']);
        $this->assertFalse($adminReason['auto_assignment']['requires_selection']);

        // Check vendor reason
        $vendorReason = collect($responseData)->firstWhere('route_to', 'vendor');
        $this->assertEquals('vendor', $vendorReason['auto_assignment']['type']);
        $this->assertEquals('Ticket will be automatically assigned to the selected vendor', $vendorReason['auto_assignment']['description']);
        $this->assertTrue($vendorReason['auto_assignment']['requires_selection']);

        // Check TPL reason
        $tplReason = collect($responseData)->firstWhere('route_to', 'tpl');
        $this->assertEquals('tpl', $tplReason['auto_assignment']['type']);
        $this->assertEquals('Ticket will be automatically assigned to the selected TPL service provider', $tplReason['auto_assignment']['description']);
        $this->assertTrue($tplReason['auto_assignment']['requires_selection']);
    }
}
