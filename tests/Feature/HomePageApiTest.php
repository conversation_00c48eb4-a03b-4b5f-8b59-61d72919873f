<?php

namespace Tests\Feature;

use App\Models\Banner;
use App\Models\BannerItem;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductClass;
use App\Models\ProductMedia;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HomePageApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create user
        $user = User::factory()->create();

        // Create categories
        $category = Category::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'status' => 'active',
            'ordering_number' => 1
        ]);

        // Create brand
        $brand = Brand::create([
            'name_en' => 'Test Brand',
            'slug' => 'test-brand',
            'is_active' => true
        ]);

        // Create product class
        $productClass = ProductClass::create([
            'name' => 'Smartphones',
            'code' => 'SP001',
            'category_id' => $category->id,
            'is_popular' => true,
            'status' => 'active'
        ]);

        // Create products
        for ($i = 1; $i <= 5; $i++) {
            $product = Product::create([
                'user_id' => $user->id,
                'title_en' => "Test Product $i",
                'short_name' => "Product $i",
                'regular_price' => 100 + ($i * 10),
                'offer_price' => 80 + ($i * 10),
                'category_id' => $category->id,
                'sub_category_id' => $category->id,
                'class_id' => $productClass->id,
                'brand_id' => $brand->id,
                'is_active' => true,
                'is_approved' => true,
                'status' => 'active'
            ]);

            // Create product media
            ProductMedia::create([
                'product_id' => $product->id,
                'type' => 'image',
                'path' => "products/product-$i.jpg",
                'is_primary' => true,
                'position' => 1
            ]);
        }

        // Create banner
        $banner = Banner::create([
            'user_id' => $user->id,
            'title' => 'Test Banner',
            'description' => 'Test banner description',
            'is_active' => true
        ]);

        BannerItem::create([
            'banner_id' => $banner->id,
            'media_path' => 'banners/test-banner.jpg',
            'link_url' => '/test-link',
            'alt_text' => 'Test Banner',
            'position' => 1,
            'is_active' => true,
            'user_id' => $user->id
        ]);
    }

    public function test_home_page_index_returns_complete_data()
    {
        $response = $this->getJson('/api/client/');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'banners' => [
                            '*' => [
                                'id',
                                'title',
                                'description',
                                'items' => [
                                    '*' => [
                                        'id',
                                        'media_url',
                                        'link_url',
                                        'alt_text',
                                        'position'
                                    ]
                                ]
                            ]
                        ],
                        'latest_products' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'short_name',
                                'regular_price',
                                'offer_price',
                                'has_discount',
                                'discount_percentage',
                                'image_url',
                                'category',
                                'product_class',
                                'brand',
                                'dietary_info'
                            ]
                        ],
                        'categories' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'icon_url',
                                'cover_image_url',
                                'products_count'
                            ]
                        ],
                        'best_sellers' => [
                            '*' => [
                                'category' => [
                                    'id',
                                    'name',
                                    'name_ar'
                                ],
                                'products' => [
                                    '*' => [
                                        'id',
                                        'name_en',
                                        'name_ar',
                                        'short_name',
                                        'regular_price',
                                        'offer_price',
                                        'has_discount',
                                        'discount_percentage',
                                        'image_url',
                                        'category',
                                        'product_class',
                                        'brand',
                                        'dietary_info'
                                    ]
                                ]
                            ]
                        ],
                        'discount_products',
                        'discounted_product_banners',
                        'featured_products',
                        'popular_brands',
                        'trending_products',
                        'brands',
                        'recently_viewed_products'
                    ],
                    'message'
                ]);

        $this->assertTrue($response->json('status'));
        $this->assertEquals('Home page data retrieved successfully!', $response->json('message'));
    }

    public function test_featured_products_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'regular_price',
                            'offer_price',
                            'has_discount',
                            'discount_percentage',
                            'image_url',
                            'category',
                            'product_class',
                            'brand',
                            'dietary_info'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertLessThanOrEqual(3, count($data));
    }

    public function test_latest_products_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'regular_price',
                            'offer_price'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertLessThanOrEqual(2, count($data));
    }

    public function test_categories_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'products_count'
                        ]
                    ]
                ]);
    }

    public function test_banners_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                            'description',
                            'items' => [
                                '*' => [
                                    'id',
                                    'media_url',
                                    'link_url',
                                    'alt_text',
                                    'position'
                                ]
                            ]
                        ]
                    ]
                ]);
    }

    public function test_best_sellers_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'category' => [
                                'id',
                                'name'
                            ],
                            'products'
                        ]
                    ]
                ]);
    }

    public function test_recommendations_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data'
                ]);

        $data = $response->json('data');
        $this->assertLessThanOrEqual(4, count($data));
    }

    public function test_popular_brands_endpoint()
    {
        $response = $this->getJson('/api/home/<USER>');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'products_count'
                        ]
                    ]
                ]);
    }

    public function test_endpoints_return_only_active_data()
    {
        // Create inactive product
        Product::create([
            'user_id' => User::first()->id,
            'title_en' => 'Inactive Product',
            'regular_price' => 100,
            'category_id' => Category::first()->id,
            'sub_category_id' => Category::first()->id,
            'class_id' => ProductClass::first()->id,
            'brand_id' => Brand::first()->id,
            'is_active' => false, // Inactive
            'is_approved' => true,
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/home/<USER>');
        $products = $response->json('data');

        // Should not include inactive products
        foreach ($products as $product) {
            $this->assertNotEquals('Inactive Product', $product['name']);
        }
    }
}
