<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CategoryInformationAndFiltersApiTest extends TestCase
{
    /**
     * Test category information API with valid slug
     */
    public function test_category_information_api_returns_successful_response()
    {
        $response = $this->getJson('/api/client/categories/vhms');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'category' => [
                            'id',
                            'name_en',
                            'name_ar',
                            'slug',
                            'banner_url',
                            'cover_image_url',
                            'icon_url',
                            'description_en',
                            'description_ar'
                        ],
                        'breadcrumb' => [
                            '*' => [
                                'name_en',
                                'name_ar',
                                'url'
                            ]
                        ],
                        'subcategories' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'icon_url',
                                'cover_image_url',
                                'product_count'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson(['status' => true]);
    }

    /**
     * Test category information API with invalid slug
     */
    public function test_category_information_api_with_invalid_slug()
    {
        $response = $this->getJson('/api/client/categories/invalid-category-slug');

        $response->assertStatus(404)
                ->assertJson([
                    'status' => false,
                    'message' => 'Category not found'
                ]);
    }

    /**
     * Test category information response structure validation
     */
    public function test_category_information_response_structure()
    {
        $response = $this->getJson('/api/client/categories/vhms');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Validate category structure
        $this->assertArrayHasKey('category', $data);
        $this->assertArrayHasKey('breadcrumb', $data);
        $this->assertArrayHasKey('subcategories', $data);
        
        // Validate category fields
        $category = $data['category'];
        $this->assertArrayHasKey('id', $category);
        $this->assertArrayHasKey('slug', $category);
        $this->assertEquals('vhms', $category['slug']);
        
        // Validate breadcrumb structure
        $breadcrumb = $data['breadcrumb'];
        $this->assertIsArray($breadcrumb);
        $this->assertGreaterThan(0, count($breadcrumb));
        
        // Validate subcategories structure
        $subcategories = $data['subcategories'];
        $this->assertIsArray($subcategories);
    }

    /**
     * Test subcategory information API with valid slug
     */
    public function test_subcategory_information_api_returns_successful_response()
    {
        $response = $this->getJson('/api/client/subcategories/antioxidants');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'category' => [
                            'subcategory' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'banner_url',
                                'cover_image_url',
                                'icon_url',
                                'description_en',
                                'description_ar'
                            ],
                            'parent_category' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug'
                            ]
                        ],
                        'breadcrumb' => [
                            '*' => [
                                'name_en',
                                'name_ar',
                                'url'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson(['status' => true]);
    }

    /**
     * Test subcategory information API with invalid slug
     */
    public function test_subcategory_information_api_with_invalid_slug()
    {
        $response = $this->getJson('/api/client/subcategories/invalid-subcategory-slug');

        $response->assertStatus(404)
                ->assertJson([
                    'status' => false,
                    'message' => 'Subcategory not found'
                ]);
    }

    /**
     * Test filters API basic functionality
     */
    public function test_filters_api_returns_successful_response()
    {
        $response = $this->getJson('/api/client/filters');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'brands' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'product_count'
                            ]
                        ],
                        'user_groups' => [
                            '*' => [
                                'id',
                                'value_en',
                                'value_ar',
                                'product_count'
                            ]
                        ],
                        'countries_of_origin' => [
                            '*' => [
                                'id',
                                'value_en',
                                'value_ar',
                                'product_count'
                            ]
                        ],
                        'formulations',
                        'flavours',
                        'storage_conditions',
                        'return_policies',
                        'warranties',
                        'price_range' => [
                            'min_price',
                            'max_price'
                        ],
                        'boolean_filters' => [
                            'vegan' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ],
                            'vegetarian' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ],
                            'halal' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson(['status' => true]);
    }

    /**
     * Test filters API with category context
     */
    public function test_filters_api_with_category_context()
    {
        $response = $this->getJson('/api/client/filters?category_id=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        $data = $response->json('data');
        
        // Validate that filters are returned
        $this->assertArrayHasKey('brands', $data);
        $this->assertArrayHasKey('price_range', $data);
        $this->assertArrayHasKey('boolean_filters', $data);
        
        // Validate price range structure
        $priceRange = $data['price_range'];
        $this->assertArrayHasKey('min_price', $priceRange);
        $this->assertArrayHasKey('max_price', $priceRange);
        $this->assertIsNumeric($priceRange['min_price']);
        $this->assertIsNumeric($priceRange['max_price']);
    }

    /**
     * Test filters API with subcategory context
     */
    public function test_filters_api_with_subcategory_context()
    {
        $response = $this->getJson('/api/client/filters?subcategory_id=2');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test filters API with brand context
     */
    public function test_filters_api_with_brand_context()
    {
        $response = $this->getJson('/api/client/filters?brand_id=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test filters API with search context
     */
    public function test_filters_api_with_search_context()
    {
        $response = $this->getJson('/api/client/filters?search=vitamin');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test filters API with multiple context parameters
     */
    public function test_filters_api_with_multiple_context_parameters()
    {
        $response = $this->getJson('/api/client/filters?category_id=1&brand_id=1&search=vitamin');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test filters API response data validation
     */
    public function test_filters_api_response_data_validation()
    {
        $response = $this->getJson('/api/client/filters?category_id=1');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Validate brands structure
        if (!empty($data['brands'])) {
            foreach ($data['brands'] as $brand) {
                $this->assertArrayHasKey('id', $brand);
                $this->assertArrayHasKey('name_en', $brand);
                $this->assertArrayHasKey('product_count', $brand);
                $this->assertIsInt($brand['id']);
                $this->assertIsInt($brand['product_count']);
            }
        }
        
        // Validate boolean filters structure
        $booleanFilters = $data['boolean_filters'];
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
        
        foreach (['vegan', 'vegetarian', 'halal'] as $filter) {
            $this->assertArrayHasKey('label_en', $booleanFilters[$filter]);
            $this->assertArrayHasKey('label_ar', $booleanFilters[$filter]);
            $this->assertArrayHasKey('product_count', $booleanFilters[$filter]);
        }
    }

    /**
     * Test filters API with invalid parameters
     */
    public function test_filters_api_with_invalid_parameters()
    {
        // Test with invalid category_id (should still return 200 but with empty/minimal filters)
        $response = $this->getJson('/api/client/filters?category_id=99999');
        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Test with invalid brand_id
        $response = $this->getJson('/api/client/filters?brand_id=99999');
        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test all categories endpoint
     */
    public function test_all_categories_endpoint()
    {
        $response = $this->getJson('/api/client/categories');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'categories' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'icon_url'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson(['status' => true]);
    }

    /**
     * Test subcategories by parent endpoint
     */
    public function test_subcategories_by_parent_endpoint()
    {
        $response = $this->getJson('/api/client/categories/1/subcategories');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'subcategories' => [
                            '*' => [
                                'id',
                                'name_en',
                                'name_ar',
                                'slug',
                                'icon_url'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson(['status' => true]);
    }
}
