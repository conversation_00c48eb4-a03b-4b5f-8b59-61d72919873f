<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\ProductClass;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductClassFilterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user for authentication with admin role
        $user = User::factory()->create();
        $user->assignRole('admin'); // Assign admin role
        $this->actingAs($user, 'api');
    }

    public function test_can_filter_product_classes_by_is_parent()
    {
        // Create category
        $category = Category::create([
            'name' => 'Electronics',
            'type' => 'main',
            'slug' => 'electronics',
            'status' => 'active'
        ]);

        // Create parent product class
        $parentClass = ProductClass::create([
            'name' => 'Mobile Devices',
            'code' => 'MD001',
            'category_id' => $category->id,
            'status' => 'active'
        ]);

        // Create child product class
        ProductClass::create([
            'name' => 'Smartphones',
            'code' => 'SP001',
            'category_id' => $category->id,
            'parent_id' => $parentClass->id,
            'status' => 'active'
        ]);

        // Create standalone product class (no children)
        ProductClass::create([
            'name' => 'Accessories',
            'code' => 'AC001',
            'category_id' => $category->id,
            'status' => 'active'
        ]);

        // Test filtering for parent classes (is_parent=true)
        $response = $this->getJson('/api/admin/product-classes?is_parent=true&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(1, $data);
        $this->assertEquals('Mobile Devices', $data[0]['name']);

        // Test filtering for non-parent classes (is_parent=false)
        $response = $this->getJson('/api/admin/product-classes?is_parent=false&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(2, $data);
        $classNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Smartphones', $classNames);
        $this->assertContains('Accessories', $classNames);
    }

    public function test_can_filter_product_classes_by_category_and_parent()
    {
        // Create categories
        $category1 = Category::create([
            'name' => 'Electronics',
            'type' => 'main',
            'slug' => 'electronics',
            'status' => 'active'
        ]);

        $category2 = Category::create([
            'name' => 'Books',
            'type' => 'main',
            'slug' => 'books',
            'status' => 'active'
        ]);

        // Create parent product class
        $parentClass = ProductClass::create([
            'name' => 'Mobile Devices',
            'code' => 'MD001',
            'category_id' => $category1->id,
            'status' => 'active'
        ]);

        // Create child product classes
        ProductClass::create([
            'name' => 'Smartphones',
            'code' => 'SP001',
            'category_id' => $category1->id,
            'parent_id' => $parentClass->id,
            'status' => 'active'
        ]);

        ProductClass::create([
            'name' => 'Fiction',
            'code' => 'FC001',
            'category_id' => $category2->id,
            'status' => 'active'
        ]);

        // Test filtering by category
        $response = $this->getJson("/api/product-classes?category_id={$category1->id}&pagination=false");
        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(2, $data);
        $classNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Mobile Devices', $classNames);
        $this->assertContains('Smartphones', $classNames);

        // Test filtering by parent_id
        $response = $this->getJson("/api/product-classes?parent_id={$parentClass->id}&pagination=false");
        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data);
        $this->assertEquals('Smartphones', $data[0]['name']);

        // Test filtering for root classes (parent_id=null)
        $response = $this->getJson('/api/product-classes?parent_id=null&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(2, $data);
        $classNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Mobile Devices', $classNames);
        $this->assertContains('Fiction', $classNames);
    }
}
