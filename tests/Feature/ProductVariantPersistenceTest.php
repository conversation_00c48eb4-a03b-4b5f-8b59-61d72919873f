<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ProductVariantPersistenceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vendor;
    protected $category;
    protected $brand;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $this->category = Category::factory()->create();
        $this->brand = Brand::factory()->create();
        
        $this->product = Product::factory()->create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'is_variant' => false,
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_create_product_variants_when_is_variant_is_true()
    {
        $variantData = [
            [
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                'regular_price' => 29.99,
                'offer_price' => 25.99,
                'stock' => 100,
                'sku' => 'TEST-001',
                'weight' => 1.5,
                'is_active' => true,
                'reserved' => 0,
                'threshold' => 5,
            ],
            [
                'attribute_id' => 1,
                'attribute_value_id' => 2,
                'regular_price' => 31.99,
                'offer_price' => 27.99,
                'stock' => 150,
                'sku' => 'TEST-002',
                'weight' => 1.7,
                'is_active' => true,
                'reserved' => 0,
                'threshold' => 5,
            ]
        ];

        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => true,
            'has_varient' => true,
            'product_variants' => $variantData,
            'regular_price' => 30.00,
            'offer_price' => 26.00,
        ]);

        $response->assertStatus(200);
        
        // Verify variants were created
        $this->assertDatabaseCount('product_variants', 2);
        
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        $this->assertCount(2, $variants);
        
        // Verify variant data
        $firstVariant = $variants->first();
        $this->assertEquals(29.99, $firstVariant->regular_price);
        $this->assertEquals('TEST-001', $firstVariant->sku);
        $this->assertTrue($firstVariant->is_active);
    }

    /** @test */
    public function it_can_update_existing_product_variants()
    {
        // First create variants
        $this->product->update(['is_variant' => true]);
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'regular_price' => 25.00,
            'sku' => 'OLD-SKU',
        ]);

        $updatedVariantData = [
            [
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                'regular_price' => 35.99,
                'offer_price' => 30.99,
                'stock' => 200,
                'sku' => 'NEW-SKU',
                'weight' => 2.0,
                'is_active' => true,
                'reserved' => 0,
                'threshold' => 10,
            ]
        ];

        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => true,
            'product_variants' => $updatedVariantData,
        ]);

        $response->assertStatus(200);
        
        // Verify old variant was replaced
        $this->assertDatabaseMissing('product_variants', [
            'sku' => 'OLD-SKU'
        ]);
        
        // Verify new variant exists
        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'sku' => 'NEW-SKU',
            'regular_price' => 35.99,
        ]);
    }

    /** @test */
    public function it_clears_variants_when_is_variant_is_false()
    {
        // First create variants
        $this->product->update(['is_variant' => true]);
        ProductVariant::factory()->count(3)->create([
            'product_id' => $this->product->id,
        ]);

        $this->assertDatabaseCount('product_variants', 3);

        // Disable variants
        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => false,
            'has_varient' => false,
            'regular_price' => 30.00,
        ]);

        $response->assertStatus(200);
        
        // Verify variants were cleared
        $this->assertDatabaseCount('product_variants', 0);
    }

    /** @test */
    public function it_handles_has_varient_field_for_compatibility()
    {
        $variantData = [
            [
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                'regular_price' => 29.99,
                'stock' => 100,
                'sku' => 'COMPAT-001',
                'is_active' => true,
                'reserved' => 0,
                'threshold' => 5,
            ]
        ];

        // Test with has_varient = true (frontend compatibility)
        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'has_varient' => true,  // Using frontend field name
            'is_variant' => false,  // Backend field is false
            'product_variants' => $variantData,
        ]);

        $response->assertStatus(200);
        
        // Verify variant was still created due to has_varient = true
        $this->assertDatabaseCount('product_variants', 1);
        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'sku' => 'COMPAT-001',
        ]);
    }

    /** @test */
    public function it_processes_variants_when_product_variants_array_is_present()
    {
        $variantData = [
            [
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                'regular_price' => 29.99,
                'stock' => 100,
                'sku' => 'AUTO-001',
                'is_active' => true,
                'reserved' => 0,
                'threshold' => 5,
            ]
        ];

        // Test with both flags false but variants array present
        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => false,
            'has_varient' => false,
            'product_variants' => $variantData,  // This should trigger variant processing
        ]);

        $response->assertStatus(200);
        
        // Verify variant was created due to presence of product_variants array
        $this->assertDatabaseCount('product_variants', 1);
        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'sku' => 'AUTO-001',
        ]);
    }

    /** @test */
    public function it_validates_required_variant_fields()
    {
        $invalidVariantData = [
            [
                // Missing required fields
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                // regular_price is missing
                'stock' => 100,
            ]
        ];

        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => true,
            'product_variants' => $invalidVariantData,
        ]);

        // Should return validation error
        $response->assertStatus(422);
        
        // Verify no variants were created
        $this->assertDatabaseCount('product_variants', 0);
    }

    /** @test */
    public function it_creates_inventory_records_for_variants()
    {
        $variantData = [
            [
                'attribute_id' => 1,
                'attribute_value_id' => 1,
                'regular_price' => 29.99,
                'stock' => 100,
                'reserved' => 10,
                'threshold' => 5,
                'sku' => 'INV-001',
                'is_active' => true,
            ]
        ];

        $response = $this->putJson("/api/admin/products/{$this->product->id}", [
            'is_variant' => true,
            'product_variants' => $variantData,
        ]);

        $response->assertStatus(200);
        
        // Verify variant was created
        $variant = ProductVariant::where('product_id', $this->product->id)->first();
        $this->assertNotNull($variant);
        
        // Verify inventory record was created
        $this->assertDatabaseHas('inventories', [
            'product_id' => $this->product->id,
            'product_variant_id' => $variant->id,
            'stock' => 100,
            'reserved' => 10,
            'threshold' => 5,
        ]);
    }
}
