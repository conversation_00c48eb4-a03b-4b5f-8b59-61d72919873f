<?php

namespace Tests\Feature;

use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\SupportReason;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SupportSystemSeedersTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function support_category_seeder_creates_correct_number_of_categories()
    {
        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);

        $totalCategories = SupportCategory::count();
        $activeCategories = SupportCategory::where('status', 'active')->count();
        $inactiveCategories = SupportCategory::where('status', 'inactive')->count();

        $this->assertEquals(41, $totalCategories);
        $this->assertEquals(39, $activeCategories);
        $this->assertEquals(2, $inactiveCategories);
    }

    /** @test */
    public function support_category_seeder_creates_required_categories()
    {
        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);

        $requiredCategories = [
            'Account & Profile',
            'Login & Authentication',
            'Payment & Billing',
            'Order Management',
            'Vendor Onboarding',
            'Commission & Fees',
            'Logistics & Warehousing',
            'Security Concerns',
            'Mobile App Issues'
        ];

        foreach ($requiredCategories as $categoryName) {
            $this->assertDatabaseHas('support_categories', [
                'name' => $categoryName,
                'status' => 'active'
            ]);
        }
    }

    /** @test */
    public function support_topic_seeder_creates_topics_for_categories()
    {
        // First seed categories
        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);
        
        // Then seed topics
        $this->artisan('db:seed', ['--class' => 'SupportTopicSeeder']);

        $totalTopics = SupportTopic::count();
        $this->assertGreaterThan(100, $totalTopics);

        // Check that topics are properly linked to categories
        $accountCategory = SupportCategory::where('name', 'Account & Profile')->first();
        $this->assertNotNull($accountCategory);

        $accountTopics = $accountCategory->topics()->count();
        $this->assertGreaterThan(0, $accountTopics);

        // Check specific topics exist
        $this->assertDatabaseHas('support_topics', [
            'category_id' => $accountCategory->id,
            'name' => 'Profile Information Update',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function support_reason_seeder_creates_reasons_with_proper_routing()
    {
        $this->artisan('db:seed', ['--class' => 'SupportReasonSeeder']);

        $totalReasons = SupportReason::count();
        $this->assertEquals(16, $totalReasons);

        // Check different routing types exist
        $adminReasons = SupportReason::where('route_to', 'admin')->count();
        $vendorReasons = SupportReason::where('route_to', 'vendor')->count();
        $tplReasons = SupportReason::where('route_to', 'tpl')->count();

        $this->assertGreaterThan(0, $adminReasons);
        $this->assertGreaterThan(0, $vendorReasons);
        $this->assertGreaterThan(0, $tplReasons);

        // Check specific reasons exist
        $this->assertDatabaseHas('support_reasons', [
            'label' => 'Account Issues',
            'route_to' => 'admin',
            'status' => 'active'
        ]);

        $this->assertDatabaseHas('support_reasons', [
            'label' => 'Product Quality Issues',
            'route_to' => 'vendor',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function seeders_create_system_admin_user_when_needed()
    {
        // Ensure no admin user exists initially
        $this->assertEquals(0, User::count());

        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);

        // Check that a system admin user was created
        $systemAdmin = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($systemAdmin);
        $this->assertEquals('System Admin', $systemAdmin->name);

        // Check that categories are linked to this user
        $categoriesCount = SupportCategory::where('user_id', $systemAdmin->id)->count();
        $this->assertGreaterThan(0, $categoriesCount);
    }

    /** @test */
    public function all_support_seeders_work_together()
    {
        // Run all support seeders in sequence
        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);
        $this->artisan('db:seed', ['--class' => 'SupportTopicSeeder']);
        $this->artisan('db:seed', ['--class' => 'SupportReasonSeeder']);

        // Verify data integrity
        $categories = SupportCategory::count();
        $topics = SupportTopic::count();
        $reasons = SupportReason::count();

        $this->assertEquals(41, $categories);
        $this->assertGreaterThan(100, $topics);
        $this->assertEquals(16, $reasons);

        // Verify relationships work
        $categoryWithTopics = SupportCategory::with('topics')->first();
        $this->assertNotNull($categoryWithTopics);
        $this->assertGreaterThan(0, $categoryWithTopics->topics->count());

        // Verify user relationships
        $user = User::first();
        $this->assertNotNull($user);
        
        $userCategories = $user->supportCategories ?? SupportCategory::where('user_id', $user->id)->count();
        $this->assertGreaterThan(0, $userCategories);
    }

    /** @test */
    public function seeders_handle_existing_data_gracefully()
    {
        // Run seeders twice to test idempotency
        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);
        $firstRunCategories = SupportCategory::count();

        $this->artisan('db:seed', ['--class' => 'SupportCategorySeeder']);
        $secondRunCategories = SupportCategory::count();

        // Should have more categories after second run (duplicates)
        $this->assertGreaterThan($firstRunCategories, $secondRunCategories);
    }
}
