<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DevDatabaseManagerTest extends TestCase
{
    /**
     * Test that the database manager interface is accessible in development environment.
     */
    public function test_database_manager_accessible_in_development()
    {
        // Set environment to local for testing
        config(['app.env' => 'local']);
        
        $response = $this->get('/dev/db-manager');
        
        $response->assertStatus(200);
        $response->assertSee('Development Database Manager');
        $response->assertSee('Authentication Required');
    }
    
    /**
     * Test that the database manager interface returns 404 in production.
     */
    public function test_database_manager_blocked_in_production()
    {
        // Set environment to production
        config(['app.env' => 'production']);
        
        $response = $this->get('/dev/db-manager');
        
        $response->assertStatus(404);
    }
    
    /**
     * Test command execution with invalid credentials.
     */
    public function test_command_execution_with_invalid_credentials()
    {
        config(['app.env' => 'local']);
        
        $response = $this->postJson('/dev/execute-command', [
            'username' => 'wrong',
            'password' => 'wrong',
            'command' => 'cache:clear'
        ]);
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'message' => 'Invalid credentials'
        ]);
    }
    
    /**
     * Test command execution with valid credentials.
     */
    public function test_command_execution_with_valid_credentials()
    {
        config(['app.env' => 'local']);
        config(['app.dev_username' => 'testuser']);
        config(['app.dev_password' => 'testpass']);
        
        $response = $this->postJson('/dev/execute-command', [
            'username' => 'testuser',
            'password' => 'testpass',
            'command' => 'cache:clear'
        ]);
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'output'
        ]);
    }
    
    /**
     * Test that invalid commands are rejected.
     */
    public function test_invalid_command_rejected()
    {
        config(['app.env' => 'local']);
        config(['app.dev_username' => 'testuser']);
        config(['app.dev_password' => 'testpass']);
        
        $response = $this->postJson('/dev/execute-command', [
            'username' => 'testuser',
            'password' => 'testpass',
            'command' => 'rm -rf /'
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Command not allowed. Only specific database and cache commands are permitted.'
        ]);
    }
    
    /**
     * Test that destructive commands require confirmation.
     */
    public function test_destructive_command_requires_confirmation()
    {
        config(['app.env' => 'local']);
        config(['app.dev_username' => 'testuser']);
        config(['app.dev_password' => 'testpass']);
        
        $response = $this->postJson('/dev/execute-command', [
            'username' => 'testuser',
            'password' => 'testpass',
            'command' => 'migrate:fresh --seed',
            'confirm_destruction' => false
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Destructive command requires confirmation'
        ]);
    }
    
    /**
     * Test quick refresh endpoint.
     */
    public function test_quick_refresh_endpoint()
    {
        config(['app.env' => 'local']);
        config(['app.dev_username' => 'testuser']);
        config(['app.dev_password' => 'testpass']);
        
        $response = $this->postJson('/dev/quick-refresh', [
            'username' => 'testuser',
            'password' => 'testpass'
        ]);
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'output'
        ]);
    }
    
    /**
     * Test that routes are blocked in production environment.
     */
    public function test_all_routes_blocked_in_production()
    {
        config(['app.env' => 'production']);
        
        // Test main interface
        $response = $this->get('/dev/db-manager');
        $response->assertStatus(404);
        
        // Test command execution
        $response = $this->postJson('/dev/execute-command', [
            'username' => 'admin',
            'password' => 'dev123!@#',
            'command' => 'cache:clear'
        ]);
        $response->assertStatus(404);
        
        // Test quick refresh
        $response = $this->postJson('/dev/quick-refresh', [
            'username' => 'admin',
            'password' => 'dev123!@#'
        ]);
        $response->assertStatus(404);
    }
}
