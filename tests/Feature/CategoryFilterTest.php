<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryFilterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user for authentication with admin role
        $user = User::factory()->create();
        $user->assignRole('admin'); // Assign admin role
        $this->actingAs($user, 'api');
    }

    public function test_can_filter_categories_by_is_parent()
    {
        // Create parent category
        $parentCategory = Category::create([
            'name' => 'Electronics',
            'type' => 'main',
            'slug' => 'electronics',
            'status' => 'active'
        ]);

        // Create child category
        Category::create([
            'name' => 'Smartphones',
            'type' => 'sub',
            'parent_id' => $parentCategory->id,
            'slug' => 'smartphones',
            'status' => 'active'
        ]);

        // Create standalone category (no children)
        Category::create([
            'name' => 'Books',
            'type' => 'main',
            'slug' => 'books',
            'status' => 'active'
        ]);

        // Test filtering for parent categories (is_parent=true)
        $response = $this->getJson('/api/admin/categories?is_parent=true&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(1, $data);
        $this->assertEquals('Electronics', $data[0]['name']);

        // Test filtering for non-parent categories (is_parent=false)
        $response = $this->getJson('/api/admin/categories?is_parent=false&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(2, $data);
        $categoryNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Smartphones', $categoryNames);
        $this->assertContains('Books', $categoryNames);
    }

    public function test_can_filter_categories_by_parent_id()
    {
        // Create parent category
        $parentCategory = Category::create([
            'name' => 'Electronics',
            'type' => 'main',
            'slug' => 'electronics',
            'status' => 'active'
        ]);

        // Create child categories
        Category::create([
            'name' => 'Smartphones',
            'type' => 'sub',
            'parent_id' => $parentCategory->id,
            'slug' => 'smartphones',
            'status' => 'active'
        ]);

        Category::create([
            'name' => 'Laptops',
            'type' => 'sub',
            'parent_id' => $parentCategory->id,
            'slug' => 'laptops',
            'status' => 'active'
        ]);

        // Test filtering by parent_id
        $response = $this->getJson("/api/admin/categories?parent_id={$parentCategory->id}&pagination=false");
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(2, $data);
        $categoryNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Smartphones', $categoryNames);
        $this->assertContains('Laptops', $categoryNames);

        // Test filtering for root categories (parent_id=null)
        $response = $this->getJson('/api/admin/categories?parent_id=null&pagination=false');
        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(1, $data);
        $this->assertEquals('Electronics', $data[0]['name']);
    }
}
