<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ProductSeo;
use App\Models\ProductFaq;
use App\Models\ProductFulfillment;
use App\Models\Inventory;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Brand;
use App\Models\DropdownOption;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ProductUpdateAPICompleteTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vendor;
    protected $category;
    protected $subCategory;
    protected $brand;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        // Create related models manually
        $this->vendor = Vendor::create([
            'id' => 1,
            'name' => 'Test Vendor',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 'active'
        ]);

        $this->category = Category::create([
            'id' => 2,
            'name_en' => 'Test Category',
            'name_ar' => 'فئة اختبار',
            'slug' => 'test-category',
            'is_active' => true
        ]);

        $this->subCategory = Category::create([
            'id' => 3,
            'name_en' => 'Test Sub Category',
            'name_ar' => 'فئة فرعية اختبار',
            'slug' => 'test-sub-category',
            'parent_id' => 2,
            'is_active' => true
        ]);

        $this->brand = Brand::create([
            'id' => 5,
            'name_en' => 'Test Brand',
            'name_ar' => 'علامة تجارية اختبار',
            'slug' => 'test-brand',
            'is_active' => true
        ]);

        // Create test product
        $this->product = Product::create([
            'id' => 27,
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'category_id' => 2,
            'brand_id' => 5,
            'title_en' => 'Test Product',
            'title_ar' => 'منتج اختبار',
            'regular_price' => 50.00,
            'is_variant' => false,
            'is_active' => true,
            'uuid' => \Str::uuid(),
            'vendor_sku' => 'TEST-SKU',
            'system_sku' => 'SYS-TEST-SKU'
        ]);

        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_update_product_with_complete_payload()
    {
        $completePayload = [
            "vendor_id" => 1,
            "category_id" => 2,
            "sub_category_id" => 3,
            "class_id" => 1,
            "sub_class_id" => 2,
            "brand_id" => 5,
            "vendor_sku" => "VSKU-12345",
            "system_sku" => "SSKU-54321",
            "barcode" => "1234567890123",
            "model_number" => "MDL-2025",
            "title_en" => "Sample Product Title",
            "title_ar" => "عنوان المنتج",
            "short_name" => "Sample Short Name",
            "short_description_en" => "A short description in English.",
            "short_description_ar" => "وصف قصير بالعربية.",
            "description_en" => "Full product description in English.",
            "description_ar" => "الوصف الكامل للمنتج بالعربية.",
            "key_ingredients" => "Ingredient1, Ingredient2",
            "usage_instruction_en" => "Use as directed.",
            "user_group_id" => 1,
            "net_weight" => 500,
            "net_weight_unit_id" => 1,
            "formulation_id" => 1,
            "servings" => 30,
            "flavour_id" => 1,
            "is_variant" => true, // Changed to true to test variants
            "has_varient" => true,
            "regular_price" => 99.99,
            "offer_price" => 89.99,
            "vat_tax" => "5%",
            "discount_start_date" => "2025-07-10T00:00:00.000Z",
            "discount_end_date" => "2025-07-20T00:00:00.000Z",
            "approx_commission" => 10.00,
            "dietary_need_ids" => "1,2",
            "is_vegan" => true,
            "is_vegetarian" => false,
            "is_halal" => true,
            "allergen_info_ids" => "3,4",
            "storage_conditions" => 1,
            "vat_tax_utl" => "https://example.com/vat-document.pdf",
            "regulatory_product_registration" => "REG-2025-001",
            "country_of_origin" => 1,
            "bbe_date" => "2026-12-31T00:00:00.000Z",
            "fulfillment_id" => 1,
            "package_length" => 10.5,
            "package_width" => 8.2,
            "package_height" => 5.0,
            "package_weight" => 0.75,
            "is_active" => true,
            "is_approved" => false,
            "status" => "pending",
            "product_seo" => [
                "meta_title_en" => "SEO Title EN",
                "meta_description_en" => "SEO Description EN",
                "keywords_en" => "keyword1, keyword2",
                "meta_title_ar" => "عنوان سيو",
                "meta_description_ar" => "وصف سيو",
                "keywords_ar" => "كلمة1, كلمة2"
            ],
            "product_faqs" => [
                [
                    "question_en" => "What is the shelf life?",
                    "answer_en" => "2 years from the date of manufacture.",
                    "question_ar" => "ما هي مدة الصلاحية؟",
                    "answer_ar" => "سنتان من تاريخ التصنيع."
                ],
                [
                    "question_en" => "Is it suitable for vegans?",
                    "answer_en" => "Yes, this product is 100% vegan.",
                    "question_ar" => "هل هو مناسب للنباتيين؟",
                    "answer_ar" => "نعم، هذا المنتج نباتي 100%."
                ]
            ],
            "product_variants" => [
                [
                    "regular_price" => 99.99,
                    "offer_price" => 89.99,
                    "vat_tax" => "5%",
                    "discount_start_date" => "2025-07-10T00:00:00.000Z",
                    "discount_end_date" => "2025-07-20T00:00:00.000Z",
                    "stock" => 100,
                    "reserved" => 10,
                    "threshold" => 5,
                    "location" => "Warehouse A",
                    "note" => "Best seller",
                    "sku" => "VSKU-12345-RED",
                    "barcode" => "1234567890124",
                    "weight" => 0.75,
                    "length" => 10.5,
                    "width" => 8.2,
                    "height" => 5.0,
                    "path" => "images/variant1.jpg",
                    "is_active" => true,
                    "stock_status" => "in_stock",
                    "attribute_id" => 1,
                    "attribute_value_id" => 2
                ],
                [
                    "regular_price" => 109.99,
                    "offer_price" => 99.99,
                    "vat_tax" => "5%",
                    "stock" => 50,
                    "reserved" => 5,
                    "threshold" => 3,
                    "location" => "Warehouse B",
                    "note" => "Limited edition",
                    "sku" => "VSKU-12345-BLUE",
                    "barcode" => "1234567890125",
                    "weight" => 0.80,
                    "is_active" => true,
                    "attribute_id" => 1,
                    "attribute_value_id" => 3
                ]
            ]
        ];

        // Make the API request
        $response = $this->putJson("/api/admin/products/{$this->product->id}", $completePayload);

        // Log the response for debugging
        if ($response->status() !== 200) {
            dump('Response Status: ' . $response->status());
            dump('Response Body: ' . $response->getContent());
        }

        // Assert successful response
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'data' => [
                'id',
                'title_en',
                'title_ar',
                'regular_price',
                'offer_price',
                'is_variant',
                'vat_tax_utl',
                'fulfillment_id',
                'productSeo',
                'productFaqs',
                'productVariants'
            ],
            'message'
        ]);

        // Verify main product data was updated
        $this->assertDatabaseHas('products', [
            'id' => $this->product->id,
            'title_en' => 'Sample Product Title',
            'title_ar' => 'عنوان المنتج',
            'vendor_sku' => 'VSKU-12345',
            'regular_price' => 99.99,
            'offer_price' => 89.99,
            'vat_tax' => '5%',
            'vat_tax_utl' => 'https://example.com/vat-document.pdf',
            'is_variant' => true,
            'approx_commission' => 10.00,
            'key_ingredients' => 'Ingredient1, Ingredient2',
            'is_vegan' => true,
            'is_vegetarian' => false,
            'is_halal' => true,
            'status' => 'pending'
        ]);

        // Verify product SEO was created/updated
        $this->assertDatabaseHas('product_seos', [
            'product_id' => $this->product->id,
            'meta_title_en' => 'SEO Title EN',
            'meta_description_en' => 'SEO Description EN',
            'keywords_en' => 'keyword1, keyword2',
            'meta_title_ar' => 'عنوان سيو'
        ]);

        // Verify product FAQs were created
        $this->assertDatabaseHas('product_faqs', [
            'product_id' => $this->product->id,
            'question_en' => 'What is the shelf life?',
            'answer_en' => '2 years from the date of manufacture.'
        ]);

        $this->assertDatabaseHas('product_faqs', [
            'product_id' => $this->product->id,
            'question_en' => 'Is it suitable for vegans?',
            'answer_en' => 'Yes, this product is 100% vegan.'
        ]);

        // Verify product variants were created
        $this->assertDatabaseCount('product_variants', 2);
        
        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'sku' => 'VSKU-12345-RED',
            'regular_price' => 99.99,
            'offer_price' => 89.99,
            'weight' => 0.75,
            'is_active' => true
        ]);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $this->product->id,
            'sku' => 'VSKU-12345-BLUE',
            'regular_price' => 109.99,
            'offer_price' => 99.99,
            'weight' => 0.80,
            'is_active' => true
        ]);

        // Verify inventory records were created for variants
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        
        foreach ($variants as $variant) {
            $this->assertDatabaseHas('inventories', [
                'product_id' => $this->product->id,
                'product_variant_id' => $variant->id
            ]);
        }

        // Verify variant attributes were created
        $this->assertDatabaseHas('product_variant_attributes', [
            'product_attribute_id' => 1,
            'product_attribute_value_id' => 2
        ]);

        $this->assertDatabaseHas('product_variant_attributes', [
            'product_attribute_id' => 1,
            'product_attribute_value_id' => 3
        ]);

        // Verify response data structure
        $responseData = $response->json('data');
        $this->assertEquals('Sample Product Title', $responseData['title_en']);
        $this->assertEquals(true, $responseData['is_variant']);
        $this->assertEquals('https://example.com/vat-document.pdf', $responseData['vat_tax_utl']);
        $this->assertNotNull($responseData['productSeo']);
        $this->assertCount(2, $responseData['productFaqs']);
        $this->assertCount(2, $responseData['productVariants']);

        // Test that the API correctly handles the enhanced variant detection
        $this->assertTrue($responseData['is_variant']);
        
        echo "\n✅ COMPLETE API TEST PASSED!\n";
        echo "✅ Main product data updated correctly\n";
        echo "✅ Product SEO created/updated\n";
        echo "✅ Product FAQs created (2 items)\n";
        echo "✅ Product variants created (2 items)\n";
        echo "✅ Inventory records created for variants\n";
        echo "✅ Variant attributes mapped correctly\n";
        echo "✅ All fields from payload processed successfully\n";
    }

    /** @test */
    public function it_handles_variant_detection_with_multiple_indicators()
    {
        $testCases = [
            // Test case 1: is_variant = true
            [
                'is_variant' => true,
                'has_varient' => false,
                'product_variants' => [['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1]],
                'should_create_variants' => true,
                'description' => 'is_variant = true'
            ],
            // Test case 2: has_varient = true
            [
                'is_variant' => false,
                'has_varient' => true,
                'product_variants' => [['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1]],
                'should_create_variants' => true,
                'description' => 'has_varient = true'
            ],
            // Test case 3: product_variants array present
            [
                'is_variant' => false,
                'has_varient' => false,
                'product_variants' => [['regular_price' => 99.99, 'attribute_id' => 1, 'attribute_value_id' => 1]],
                'should_create_variants' => true,
                'description' => 'product_variants array present'
            ],
            // Test case 4: no variants
            [
                'is_variant' => false,
                'has_varient' => false,
                'product_variants' => [],
                'should_create_variants' => false,
                'description' => 'no variants'
            ]
        ];

        foreach ($testCases as $index => $testCase) {
            // Create a fresh product for each test
            $testProduct = Product::factory()->create([
                'user_id' => $this->user->id,
                'vendor_id' => $this->vendor->id,
            ]);

            $payload = [
                'is_variant' => $testCase['is_variant'],
                'has_varient' => $testCase['has_varient'],
                'product_variants' => $testCase['product_variants'],
                'regular_price' => 99.99,
                'title_en' => "Test Product {$index}"
            ];

            $response = $this->putJson("/api/admin/products/{$testProduct->id}", $payload);
            
            $response->assertStatus(200);

            if ($testCase['should_create_variants']) {
                $this->assertDatabaseHas('product_variants', [
                    'product_id' => $testProduct->id,
                    'regular_price' => 99.99
                ]);
                echo "✅ Test case '{$testCase['description']}': Variants created as expected\n";
            } else {
                $this->assertDatabaseMissing('product_variants', [
                    'product_id' => $testProduct->id
                ]);
                echo "✅ Test case '{$testCase['description']}': No variants created as expected\n";
            }
        }
    }
}
