<?php

namespace Tests\Feature\Client;

use App\Models\Order;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vendor $vendor;
    protected Product $product;
    protected ShoppingCart $cart;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->active()->create([
            'vendor_id' => $this->vendor->id,
            'regular_price' => 100.00,
            'offer_price' => 80.00,
        ]);

        // Create a cart with items for testing
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'currency' => 'AED',
        ]);

        $this->cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 80.00,
            'total_price' => 160.00,
            'product_snapshot' => [],
        ]);

        Passport::actingAs($this->user);
    }

    public function test_create_order_from_cart_success(): void
    {
        $orderData = [
            'cart_uuid' => $this->cart->uuid,
            'payment_method' => 'card',
            'customer_note' => 'Test order',
            'shipping_address' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address_line_1' => '123 Test Street',
                'city' => 'Dubai',
                'country' => 'AE',
                'phone' => '+************',
            ],
            'terms_accepted' => true,
        ];

        $response = $this->postJson('/api/client/orders/create-from-cart', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'uuid',
                    'user_id',
                    'status',
                    'items',
                    'addresses',
                ],
            ]);

        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'cart_id' => $this->cart->id,
            'payment_method' => 'card',
            'customer_note' => 'Test order',
        ]);
    }

    public function test_create_order_from_cart_unauthorized_cart(): void
    {
        $otherUser = User::factory()->create();
        $otherCart = ShoppingCart::factory()->create([
            'user_id' => $otherUser->id,
            'status' => 'active',
        ]);

        $orderData = [
            'cart_uuid' => $otherCart->uuid,
            'payment_method' => 'card',
            'shipping_address' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address_line_1' => '123 Test Street',
                'city' => 'Dubai',
                'country' => 'AE',
            ],
            'terms_accepted' => true,
        ];

        $response = $this->postJson('/api/client/orders/create-from-cart', $orderData);

        $response->assertStatus(403);
    }

    public function test_create_order_from_cart_validation_errors(): void
    {
        $response = $this->postJson('/api/client/orders/create-from-cart', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cart_uuid',
                'shipping_address',
                'terms_accepted',
            ]);
    }

    public function test_convert_cart_to_order_success(): void
    {
        $orderData = [
            'cart_uuid' => $this->cart->uuid,
            'payment_method' => 'cod',
            'customer_note' => 'Convert cart test',
            'shipping_address' => [
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'address_line_1' => '456 Convert Street',
                'city' => 'Abu Dhabi',
                'country' => 'AE',
                'phone' => '+************',
            ],
            'split_by_vendor' => false,
            'apply_member_pricing' => true,
            'recalculate_pricing' => true,
            'terms_accepted' => true,
        ];

        $response = $this->postJson('/api/client/orders/convert-cart', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'uuid',
                    'user_id',
                    'status',
                    'items',
                    'addresses',
                ],
            ]);

        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'cart_id' => $this->cart->id,
            'payment_method' => 'cod',
            'customer_note' => 'Convert cart test',
        ]);

        // Cart should be marked as converted
        $this->assertEquals('converted', $this->cart->fresh()->status);
    }

    public function test_convert_cart_with_vendor_split(): void
    {
        // Create another vendor and product
        $vendor2 = Vendor::factory()->active()->create();
        $product2 = Product::factory()->active()->create([
            'vendor_id' => $vendor2->id,
            'regular_price' => 50.00,
        ]);

        // Add item from second vendor to cart
        $this->cart->items()->create([
            'product_id' => $product2->id,
            'vendor_id' => $vendor2->id,
            'quantity' => 1,
            'unit_price' => 50.00,
            'total_price' => 50.00,
            'product_snapshot' => [],
        ]);

        $orderData = [
            'cart_uuid' => $this->cart->uuid,
            'payment_method' => 'card',
            'shipping_address' => [
                'first_name' => 'Multi',
                'last_name' => 'Vendor',
                'address_line_1' => '789 Split Street',
                'city' => 'Sharjah',
                'country' => 'AE',
            ],
            'split_by_vendor' => true,
            'terms_accepted' => true,
        ];

        $response = $this->postJson('/api/client/orders/convert-cart', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'uuid',
                        'vendor_id',
                        'user_id',
                    ],
                ],
            ]);

        // Should create two separate orders
        $this->assertEquals(2, Order::where('user_id', $this->user->id)->count());
    }

    public function test_list_customer_orders(): void
    {
        // Create some orders for the user
        Order::factory()->count(3)->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->getJson('/api/client/orders');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'uuid',
                            'status',
                            'total_amount',
                            'created_at',
                        ],
                    ],
                    'meta' => [
                        'current_page',
                        'total',
                    ],
                ],
            ]);
    }

    public function test_get_order_summary(): void
    {
        Order::factory()->count(5)->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->getJson('/api/client/orders/summary');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'total_orders',
                    'pending_orders',
                    'completed_orders',
                    'total_spent',
                ],
            ]);
    }

    public function test_get_order_analytics(): void
    {
        $response = $this->getJson('/api/client/orders/analytics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
            ]);
    }

    public function test_show_order(): void
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->getJson("/api/client/orders/{$order->uuid}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'uuid',
                    'user_id',
                    'status',
                    'items',
                    'addresses',
                ],
            ]);
    }

    public function test_show_order_unauthorized(): void
    {
        $otherUser = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->getJson("/api/client/orders/{$order->uuid}");

        $response->assertStatus(403);
    }

    public function test_cancel_order(): void
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $cancelData = [
            'reason' => 'Changed my mind',
        ];

        $response = $this->patchJson("/api/client/orders/{$order->uuid}/cancel", $cancelData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'uuid',
                    'status',
                ],
            ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'status' => 'cancelled',
        ]);
    }

    public function test_unauthenticated_access_denied(): void
    {
        Passport::actingAs(null);

        $response = $this->getJson('/api/client/orders');

        $response->assertStatus(401);
    }
}
