<?php

namespace Tests\Feature\Client;

use App\Models\User;
use App\Models\Customer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Passport\Passport;
use Tests\TestCase;

class CustomerControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Customer $customer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->customer = Customer::factory()->create([
            'user_id' => $this->user->id,
        ]);

        Passport::actingAs($this->user);
    }

    public function test_get_customer_profile(): void
    {
        $response = $this->getJson('/api/client/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'phone',
                    'customer' => [
                        'id',
                        'first_name',
                        'last_name',
                        'date_of_birth',
                        'gender',
                        'phone',
                        'avatar_url',
                    ],
                ],
                'message',
            ])
            ->assertJson([
                'status' => true,
                'data' => [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ],
            ]);
    }

    public function test_update_customer_profile_basic_info(): void
    {
        $updateData = [
            'name' => 'Updated Name',
            'phone' => '+971501234567',
            'customer' => [
                'first_name' => 'Updated',
                'last_name' => 'Customer',
                'date_of_birth' => '1990-01-01',
                'gender' => 'male',
                'phone' => '+971501234567',
            ],
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'name',
                    'phone',
                    'customer',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'name' => 'Updated Name',
            'phone' => '+971501234567',
        ]);

        $this->assertDatabaseHas('customers', [
            'user_id' => $this->user->id,
            'first_name' => 'Updated',
            'last_name' => 'Customer',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
        ]);
    }

    public function test_update_customer_profile_with_avatar(): void
    {
        Storage::fake('public');

        $avatar = UploadedFile::fake()->image('avatar.jpg', 200, 200);

        $updateData = [
            'name' => 'Avatar User',
            'customer' => [
                'first_name' => 'Avatar',
                'last_name' => 'User',
            ],
            'avatar' => $avatar,
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(200);

        // Check that avatar was uploaded
        $customer = $this->customer->fresh();
        $this->assertNotNull($customer->avatar_url);
        
        // Check file exists in storage
        $avatarPath = str_replace('/storage/', '', parse_url($customer->avatar_url, PHP_URL_PATH));
        Storage::disk('public')->assertExists($avatarPath);
    }

    public function test_update_profile_validation_errors(): void
    {
        $invalidData = [
            'name' => '', // Required field
            'email' => 'invalid-email', // Invalid email format
            'customer' => [
                'date_of_birth' => 'invalid-date', // Invalid date format
                'gender' => 'invalid-gender', // Invalid gender value
                'phone' => '123', // Invalid phone format
            ],
        ];

        $response = $this->postJson('/api/client/update-profile', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'name',
                'email',
                'customer.date_of_birth',
                'customer.gender',
                'customer.phone',
            ]);
    }

    public function test_update_profile_email_uniqueness(): void
    {
        $otherUser = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $updateData = [
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_update_profile_phone_uniqueness(): void
    {
        $otherUser = User::factory()->create([
            'phone' => '+971501111111',
        ]);

        $updateData = [
            'phone' => '+971501111111',
            'name' => 'Test User',
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['phone']);
    }

    public function test_update_profile_avatar_validation(): void
    {
        Storage::fake('public');

        // Test with invalid file type
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1000);

        $updateData = [
            'name' => 'Test User',
            'avatar' => $invalidFile,
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['avatar']);
    }

    public function test_update_profile_avatar_size_validation(): void
    {
        Storage::fake('public');

        // Test with file too large (over 2MB)
        $largeFile = UploadedFile::fake()->image('large.jpg')->size(3000);

        $updateData = [
            'name' => 'Test User',
            'avatar' => $largeFile,
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['avatar']);
    }

    public function test_update_profile_preserves_existing_data(): void
    {
        $originalEmail = $this->user->email;
        $originalPhone = $this->user->phone;

        $updateData = [
            'name' => 'New Name Only',
            'customer' => [
                'first_name' => 'New First Name',
            ],
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(200);

        $updatedUser = $this->user->fresh();
        $this->assertEquals('New Name Only', $updatedUser->name);
        $this->assertEquals($originalEmail, $updatedUser->email);
        $this->assertEquals($originalPhone, $updatedUser->phone);

        $updatedCustomer = $this->customer->fresh();
        $this->assertEquals('New First Name', $updatedCustomer->first_name);
    }

    public function test_unauthenticated_access_denied(): void
    {
        Passport::actingAs(null);

        $response = $this->getJson('/api/client/profile');
        $response->assertStatus(401);

        $response = $this->postJson('/api/client/update-profile', []);
        $response->assertStatus(401);
    }

    public function test_profile_includes_customer_statistics(): void
    {
        $response = $this->getJson('/api/client/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'customer' => [
                        'statistics' => [
                            'total_orders',
                            'total_spent',
                            'loyalty_points',
                            'member_since',
                        ],
                    ],
                ],
            ]);
    }

    public function test_update_profile_updates_customer_preferences(): void
    {
        $updateData = [
            'name' => 'Preference User',
            'customer' => [
                'first_name' => 'Preference',
                'last_name' => 'User',
                'preferences' => [
                    'newsletter_subscription' => true,
                    'sms_notifications' => false,
                    'language' => 'ar',
                    'currency' => 'AED',
                ],
            ],
        ];

        $response = $this->postJson('/api/client/update-profile', $updateData);

        $response->assertStatus(200);

        $customer = $this->customer->fresh();
        $preferences = $customer->preferences;
        
        $this->assertTrue($preferences['newsletter_subscription']);
        $this->assertFalse($preferences['sms_notifications']);
        $this->assertEquals('ar', $preferences['language']);
        $this->assertEquals('AED', $preferences['currency']);
    }
}
