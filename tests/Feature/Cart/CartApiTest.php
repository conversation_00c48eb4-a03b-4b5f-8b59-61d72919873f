<?php

namespace Tests\Feature\Cart;

use App\Models\Inventory;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CartApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'regular_price' => 100.00,
        ]);

        // Create inventory for the product
        Inventory::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'stock' => 100,
            'reserved' => 0,
            'stock_status' => 'in_stock',
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
    }

    public function test_create_cart_as_guest(): void
    {
        $response = $this->postJson('/api/client/cart', [
            'currency' => 'AED',
            'notes' => 'Test cart',
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'uuid',
                    'currency',
                    'status',
                    'subtotal',
                    'total_amount',
                    'session_id',
                ],
                'message',
            ]);

        $this->assertDatabaseHas('shopping_carts', [
            'currency' => 'AED',
            'notes' => 'Test cart',
            'status' => 'active',
        ]);
    }

    public function test_get_cart_by_uuid(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
            'currency' => 'AED',
        ]);

        $response = $this->getJson("/api/client/cart/{$cart->uuid}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'uuid',
                    'currency',
                    'status',
                    'subtotal',
                    'total_amount',
                ],
            ]);

        $this->assertEquals($cart->uuid, $response->json('data.uuid'));
    }

    public function test_get_cart_unauthorized_access(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => 'different-session',
        ]);

        $response = $this->getJson("/api/client/cart/{$cart->uuid}");

        $response->assertStatus(403);
    }

    public function test_add_item_to_cart(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $response = $this->postJson("/api/client/cart/{$cart->uuid}/items", [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'cart_id',
                    'product_id',
                    'vendor_id',
                    'quantity',
                    'unit_price',
                    'total_price',
                ],
            ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
        ]);
    }

    public function test_add_item_validation_errors(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $response = $this->postJson("/api/client/cart/{$cart->uuid}/items", [
            'product_id' => 999999, // Non-existent product
            'quantity' => 0, // Invalid quantity
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_id', 'quantity']);
    }

    public function test_update_cart_item_quantity(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $cartItem = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->putJson("/api/client/cart/{$cart->uuid}/items/{$cartItem->id}", [
            'quantity' => 5,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.quantity', 5)
            ->assertJsonPath('data.total_price', 500.00);

        $this->assertDatabaseHas('cart_items', [
            'id' => $cartItem->id,
            'quantity' => 5,
            'total_price' => 500.00,
        ]);
    }

    public function test_remove_cart_item_with_zero_quantity(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $cartItem = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->putJson("/api/client/cart/{$cart->uuid}/items/{$cartItem->id}", [
            'quantity' => 0,
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $cartItem->id,
        ]);
    }

    public function test_remove_cart_item(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $cartItem = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->deleteJson("/api/client/cart/{$cart->uuid}/items/{$cartItem->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $cartItem->id,
        ]);
    }

    public function test_bulk_update_cart_items(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $item1 = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $item2 = $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->postJson("/api/client/cart/{$cart->uuid}/items/bulk", [
            'items' => [
                ['id' => $item1->id, 'quantity' => 5],
                ['id' => $item2->id, 'quantity' => 0], // Remove this item
            ],
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('cart_items', [
            'id' => $item1->id,
            'quantity' => 5,
        ]);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $item2->id,
        ]);
    }

    public function test_clear_cart(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        // Add some items
        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->deleteJson("/api/client/cart/{$cart->uuid}");

        $response->assertStatus(200);

        $this->assertEquals(0, $cart->fresh()->items()->count());
        $this->assertEquals(0, $cart->fresh()->subtotal);
    }

    public function test_get_vendor_split(): void
    {
        $vendor2 = Vendor::factory()->active()->create();
        $product2 = Product::factory()->create(['vendor_id' => $vendor2->id]);

        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        // Add items from different vendors
        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $cart->items()->create([
            'product_id' => $product2->id,
            'vendor_id' => $vendor2->id,
            'quantity' => 1,
            'unit_price' => 150.00,
            'total_price' => 150.00,
            'product_snapshot' => [],
        ]);

        $response = $this->getJson("/api/client/cart/{$cart->uuid}/vendors");

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        $vendorGroups = $response->json('data');
        $this->assertCount(2, $vendorGroups);
    }

    public function test_validate_cart(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
            'total_amount' => 200.00,
        ]);

        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        $response = $this->postJson("/api/client/cart/{$cart->uuid}/validate");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'errors',
                    'warnings',
                    'is_valid',
                ],
            ]);
    }

    public function test_cart_not_found(): void
    {
        $response = $this->getJson('/api/client/cart/non-existent-uuid');

        $response->assertStatus(404);
    }

    public function test_cart_item_not_found(): void
    {
        $cart = ShoppingCart::factory()->create([
            'session_id' => session()->getId(),
        ]);

        $response = $this->putJson("/api/client/cart/{$cart->uuid}/items/999999", [
            'quantity' => 5,
        ]);

        $response->assertStatus(404);
    }

    public function test_cart_middleware_creates_session(): void
    {
        $response = $this->postJson('/api/client/cart', [
            'currency' => 'AED',
        ]);

        $response->assertStatus(201);

        // Session should be created
        $this->assertNotNull(session()->getId());
    }
}
