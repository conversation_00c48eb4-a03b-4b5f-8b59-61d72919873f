<?php

namespace Tests\Feature\Cart;

use App\Jobs\Cart\ProcessAbandonedCartsJob;
use App\Models\AbandonedCart;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use App\Notifications\Cart\AbandonedCartReminderNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CartRecoveryTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vendor $vendor;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);

        $this->vendor = Vendor::factory()->active()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'active',
            'price' => 100.00,
        ]);
    }

    public function test_abandoned_cart_identification(): void
    {
        // Create a cart that was last active 25 hours ago (should be abandoned)
        $abandonedCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'last_activity_at' => now()->subHours(25),
            'total_amount' => 200.00,
        ]);

        $abandonedCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
            'product_snapshot' => [],
        ]);

        // Create a recent cart (should not be abandoned)
        $recentCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'last_activity_at' => now()->subHours(12),
        ]);

        $recentCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Check that the old cart was marked as abandoned
        $this->assertEquals('abandoned', $abandonedCart->fresh()->status);
        $this->assertEquals('active', $recentCart->fresh()->status);

        // Check that abandoned cart record was created
        $this->assertDatabaseHas('abandoned_carts', [
            'cart_id' => $abandonedCart->id,
            'customer_id' => $this->user->id,
            'email' => $this->user->email,
            'cart_value' => 200.00,
            'items_count' => 1,
            'recovery_status' => 'pending',
        ]);
    }

    public function test_abandoned_cart_reminder_email_sent(): void
    {
        Notification::fake();

        // Create abandoned cart record
        $abandonedCart = AbandonedCart::factory()->create([
            'customer_id' => $this->user->id,
            'email' => $this->user->email,
            'cart_value' => 250.00,
            'items_count' => 2,
            'recovery_status' => 'pending',
            'reminder_sent_at' => null,
            'recovery_expires_at' => now()->addDays(7),
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Check that notification was sent
        Notification::assertSentTo(
            $this->user,
            AbandonedCartReminderNotification::class,
            function ($notification) use ($abandonedCart) {
                return $notification->toArray($this->user)['cart_id'] === $abandonedCart->cart_id;
            }
        );

        // Check that abandoned cart record was updated
        $abandonedCart->refresh();
        $this->assertEquals('sent', $abandonedCart->recovery_status);
        $this->assertNotNull($abandonedCart->reminder_sent_at);
        $this->assertEquals(1, $abandonedCart->recovery_attempts);
    }

    public function test_abandoned_cart_reminder_not_sent_if_expired(): void
    {
        Notification::fake();

        // Create expired abandoned cart record
        $expiredAbandonedCart = AbandonedCart::factory()->create([
            'customer_id' => $this->user->id,
            'email' => $this->user->email,
            'recovery_status' => 'pending',
            'reminder_sent_at' => null,
            'recovery_expires_at' => now()->subDay(), // Expired
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Check that no notification was sent
        Notification::assertNotSentTo($this->user, AbandonedCartReminderNotification::class);
    }

    public function test_abandoned_cart_cleanup_expires_old_records(): void
    {
        // Create old abandoned cart that should be expired
        $oldAbandonedCart = AbandonedCart::factory()->create([
            'customer_id' => $this->user->id,
            'recovery_status' => 'sent',
            'recovery_expires_at' => now()->subDays(2),
        ]);

        // Create recent abandoned cart that should remain active
        $recentAbandonedCart = AbandonedCart::factory()->create([
            'customer_id' => $this->user->id,
            'recovery_status' => 'sent',
            'recovery_expires_at' => now()->addDays(5),
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Check that old record was expired
        $this->assertEquals('expired', $oldAbandonedCart->fresh()->recovery_status);
        $this->assertEquals('sent', $recentAbandonedCart->fresh()->recovery_status);
    }

    public function test_old_guest_carts_are_deleted(): void
    {
        // Create old guest cart (should be deleted)
        $oldGuestCart = ShoppingCart::factory()->create([
            'user_id' => null,
            'session_id' => 'old-session',
            'status' => 'active',
            'created_at' => now()->subDays(35),
        ]);

        // Create recent guest cart (should be kept)
        $recentGuestCart = ShoppingCart::factory()->create([
            'user_id' => null,
            'session_id' => 'recent-session',
            'status' => 'active',
            'created_at' => now()->subDays(5),
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Check that old guest cart was deleted
        $this->assertDatabaseMissing('shopping_carts', ['id' => $oldGuestCart->id]);
        $this->assertDatabaseHas('shopping_carts', ['id' => $recentGuestCart->id]);
    }

    public function test_abandoned_cart_recovery_url_generation(): void
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'recovery_token' => 'test-recovery-token-123',
        ]);

        $notification = new AbandonedCartReminderNotification($abandonedCart);
        $mailMessage = $notification->toMail($this->user);

        // Check that the recovery URL is correctly generated
        $expectedUrl = url('/cart/recover/test-recovery-token-123');
        
        // The action URL should match our expected recovery URL
        $this->assertStringContains('Complete Your Purchase', $mailMessage->actionText);
        $this->assertEquals($expectedUrl, $mailMessage->actionUrl);
    }

    public function test_abandoned_cart_notification_content(): void
    {
        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'currency' => 'AED',
            'total_amount' => 350.00,
        ]);

        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 3,
            'unit_price' => 100.00,
            'total_price' => 300.00,
            'product_snapshot' => [],
        ]);

        $abandonedCart = AbandonedCart::factory()->create([
            'cart_id' => $cart->id,
            'customer_id' => $this->user->id,
            'cart_value' => 350.00,
            'items_count' => 1,
        ]);

        $notification = new AbandonedCartReminderNotification($abandonedCart);
        $mailMessage = $notification->toMail($this->user);

        // Check email content
        $this->assertEquals('You left something in your cart!', $mailMessage->subject);
        $this->assertStringContains("Hi {$this->user->name},", $mailMessage->greeting);
        $this->assertStringContains('Cart value: AED 350', implode(' ', $mailMessage->introLines));
        $this->assertStringContains('Items: 1', implode(' ', $mailMessage->introLines));
    }

    public function test_process_abandoned_carts_command(): void
    {
        Queue::fake();

        $this->artisan('cart:process-abandoned --queue')
            ->expectsOutput('Abandoned cart processing job dispatched to queue.')
            ->assertExitCode(0);

        Queue::assertPushed(ProcessAbandonedCartsJob::class);
    }

    public function test_process_abandoned_carts_command_sync(): void
    {
        // Create a cart that should be abandoned
        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'last_activity_at' => now()->subHours(25),
        ]);

        $cart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $this->artisan('cart:process-abandoned')
            ->expectsOutput('Processing abandoned carts...')
            ->expectsOutput('Abandoned cart processing completed.')
            ->assertExitCode(0);

        // Check that cart was processed
        $this->assertEquals('abandoned', $cart->fresh()->status);
    }

    public function test_empty_carts_are_not_marked_as_abandoned(): void
    {
        // Create empty cart that's old
        $emptyCart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'last_activity_at' => now()->subHours(25),
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Empty cart should not be marked as abandoned
        $this->assertEquals('active', $emptyCart->fresh()->status);
        $this->assertDatabaseMissing('abandoned_carts', [
            'cart_id' => $emptyCart->id,
        ]);
    }

    public function test_guest_carts_are_not_marked_as_abandoned(): void
    {
        // Create guest cart that's old
        $guestCart = ShoppingCart::factory()->create([
            'user_id' => null,
            'session_id' => 'guest-session',
            'status' => 'active',
            'last_activity_at' => now()->subHours(25),
        ]);

        $guestCart->items()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [],
        ]);

        $job = new ProcessAbandonedCartsJob();
        $job->handle();

        // Guest cart should not be marked as abandoned (no user to send email to)
        $this->assertEquals('active', $guestCart->fresh()->status);
        $this->assertDatabaseMissing('abandoned_carts', [
            'cart_id' => $guestCart->id,
        ]);
    }
}
