<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;

class ProductUpdateAPISimpleTest extends TestCase
{
    use RefreshDatabase;

    public function test_product_update_api_with_complete_payload()
    {
        // Create a test user
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        // Create a test product
        $product = Product::create([
            'user_id' => $user->id,
            'vendor_id' => 1,
            'category_id' => 1,
            'sub_category_id' => 1,
            'brand_id' => 1,
            'title_en' => 'Original Product',
            'title_ar' => 'منتج أصلي',
            'regular_price' => 50.00,
            'is_variant' => false,
            'is_active' => true,
            'uuid' => Str::uuid(),
            'vendor_sku' => 'ORIG-SKU',
            'system_sku' => 'SYS-ORIG-SKU'
        ]);

        // Authenticate as the user
        $this->actingAs($user);

        // Complete payload based on your specification
        $completePayload = [
            "vendor_id" => 1,
            "category_id" => 2,
            "sub_category_id" => 3,
            "class_id" => 1,
            "sub_class_id" => 2,
            "brand_id" => 5,
            "vendor_sku" => "VSKU-12345",
            "system_sku" => "SSKU-54321",
            "barcode" => "1234567890123",
            "model_number" => "MDL-2025",
            "title_en" => "Sample Product Title",
            "title_ar" => "عنوان المنتج",
            "short_name" => "Sample Short Name",
            "short_description_en" => "A short description in English.",
            "short_description_ar" => "وصف قصير بالعربية.",
            "description_en" => "Full product description in English.",
            "description_ar" => "الوصف الكامل للمنتج بالعربية.",
            "key_ingredients" => "Ingredient1, Ingredient2",
            "usage_instruction_en" => "Use as directed.",
            "user_group_id" => 1,
            "net_weight" => 500,
            "net_weight_unit_id" => 1,
            "formulation_id" => 1,
            "servings" => 30,
            "flavour_id" => 1,
            "is_variant" => true, // Enable variants
            "has_varient" => true, // Frontend compatibility
            "regular_price" => 99.99,
            "offer_price" => 89.99,
            "vat_tax" => "5%",
            "discount_start_date" => "2025-07-10T00:00:00.000Z",
            "discount_end_date" => "2025-07-20T00:00:00.000Z",
            "approx_commission" => 10.00,
            "dietary_need_ids" => "1,2",
            "is_vegan" => true,
            "is_vegetarian" => false,
            "is_halal" => true,
            "allergen_info_ids" => "3,4",
            "storage_conditions" => 1,
            "vat_tax_utl" => "https://example.com/vat-document.pdf",
            "regulatory_product_registration" => "REG-2025-001",
            "country_of_origin" => 1,
            "bbe_date" => "2026-12-31T00:00:00.000Z",
            "fulfillment_id" => 1,
            "package_length" => 10.5,
            "package_width" => 8.2,
            "package_height" => 5.0,
            "package_weight" => 0.75,
            "is_active" => true,
            "is_approved" => false,
            "status" => "pending",
            "product_variants" => [
                [
                    "regular_price" => 99.99,
                    "offer_price" => 89.99,
                    "vat_tax" => "5%",
                    "discount_start_date" => "2025-07-10T00:00:00.000Z",
                    "discount_end_date" => "2025-07-20T00:00:00.000Z",
                    "stock" => 100,
                    "reserved" => 10,
                    "threshold" => 5,
                    "location" => "Warehouse A",
                    "note" => "Best seller",
                    "sku" => "VSKU-12345-RED",
                    "barcode" => "1234567890124",
                    "weight" => 0.75,
                    "length" => 10.5,
                    "width" => 8.2,
                    "height" => 5.0,
                    "path" => "images/variant1.jpg",
                    "is_active" => true,
                    "stock_status" => "in_stock",
                    "attribute_id" => 1,
                    "attribute_value_id" => 2
                ],
                [
                    "regular_price" => 109.99,
                    "offer_price" => 99.99,
                    "vat_tax" => "5%",
                    "stock" => 50,
                    "reserved" => 5,
                    "threshold" => 3,
                    "location" => "Warehouse B",
                    "note" => "Limited edition",
                    "sku" => "VSKU-12345-BLUE",
                    "barcode" => "1234567890125",
                    "weight" => 0.80,
                    "is_active" => true,
                    "attribute_id" => 1,
                    "attribute_value_id" => 3
                ]
            ]
        ];

        // Make the API request
        $response = $this->putJson("/api/admin/products/{$product->id}", $completePayload);

        // Debug output if there's an error
        if ($response->status() !== 200) {
            echo "\n❌ API Response Status: " . $response->status() . "\n";
            echo "❌ Response Body: " . $response->getContent() . "\n";
            
            // Also check for validation errors
            if ($response->status() === 422) {
                $errors = $response->json('errors');
                if ($errors) {
                    echo "❌ Validation Errors:\n";
                    foreach ($errors as $field => $messages) {
                        echo "  - {$field}: " . implode(', ', $messages) . "\n";
                    }
                }
            }
        }

        // Assert successful response
        $response->assertStatus(200);
        
        // Get the response data
        $responseData = $response->json();
        
        // Verify the response structure
        $this->assertTrue($responseData['status']);
        $this->assertEquals('Product updated successfully!', $responseData['message']);
        $this->assertArrayHasKey('data', $responseData);

        // Verify main product data was updated
        $updatedProduct = Product::find($product->id);
        $this->assertEquals('Sample Product Title', $updatedProduct->title_en);
        $this->assertEquals('عنوان المنتج', $updatedProduct->title_ar);
        $this->assertEquals('VSKU-12345', $updatedProduct->vendor_sku);
        $this->assertEquals(99.99, (float)$updatedProduct->regular_price);
        $this->assertEquals(89.99, (float)$updatedProduct->offer_price);
        $this->assertEquals('5%', $updatedProduct->vat_tax);
        $this->assertEquals('https://example.com/vat-document.pdf', $updatedProduct->vat_tax_utl);
        $this->assertTrue($updatedProduct->is_variant);
        $this->assertEquals(10.00, (float)$updatedProduct->approx_commission);

        // Verify product variants were created
        $variants = ProductVariant::where('product_id', $product->id)->get();
        $this->assertCount(2, $variants);

        // Check first variant
        $redVariant = $variants->where('sku', 'VSKU-12345-RED')->first();
        $this->assertNotNull($redVariant);
        $this->assertEquals(99.99, (float)$redVariant->regular_price);
        $this->assertEquals(89.99, (float)$redVariant->offer_price);
        $this->assertEquals(0.75, (float)$redVariant->weight);
        $this->assertTrue($redVariant->is_active);

        // Check second variant
        $blueVariant = $variants->where('sku', 'VSKU-12345-BLUE')->first();
        $this->assertNotNull($blueVariant);
        $this->assertEquals(109.99, (float)$blueVariant->regular_price);
        $this->assertEquals(99.99, (float)$blueVariant->offer_price);
        $this->assertEquals(0.80, (float)$blueVariant->weight);
        $this->assertTrue($blueVariant->is_active);

        // Verify inventory records were created
        $this->assertDatabaseHas('inventories', [
            'product_id' => $product->id,
            'product_variant_id' => $redVariant->id,
            'stock' => 100,
            'reserved' => 10,
            'threshold' => 5
        ]);

        $this->assertDatabaseHas('inventories', [
            'product_id' => $product->id,
            'product_variant_id' => $blueVariant->id,
            'stock' => 50,
            'reserved' => 5,
            'threshold' => 3
        ]);

        // Success output
        echo "\n✅ COMPLETE API TEST PASSED!\n";
        echo "✅ Main product data updated correctly\n";
        echo "✅ Product variants created (2 items)\n";
        echo "✅ Inventory records created for variants\n";
        echo "✅ VAT tax document URL saved: {$updatedProduct->vat_tax_utl}\n";
        echo "✅ All fields from payload processed successfully\n";
        echo "✅ API Response Status: 200 OK\n";
        
        return true;
    }

    public function test_variant_detection_logic()
    {
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        $product = Product::create([
            'user_id' => $user->id,
            'vendor_id' => 1,
            'category_id' => 1,
            'sub_category_id' => 1,
            'brand_id' => 1,
            'title_en' => 'Test Product 2',
            'regular_price' => 50.00,
            'is_variant' => false,
            'is_active' => true,
            'uuid' => Str::uuid(),
            'vendor_sku' => 'TEST-SKU-2',
            'system_sku' => 'SYS-TEST-SKU-2'
        ]);

        $this->actingAs($user);

        // Test case: has_varient = true, is_variant = false, but variants array present
        $payload = [
            'is_variant' => false,
            'has_varient' => true,
            'regular_price' => 99.99,
            'title_en' => 'Updated Test Product',
            'product_variants' => [
                [
                    'regular_price' => 99.99,
                    'stock' => 100,
                    'sku' => 'TEST-VARIANT-001',
                    'attribute_id' => 1,
                    'attribute_value_id' => 1,
                    'is_active' => true,
                    'reserved' => 0,
                    'threshold' => 5
                ]
            ]
        ];

        $response = $this->putJson("/api/admin/products/{$product->id}", $payload);
        
        $response->assertStatus(200);
        
        // Verify variant was created despite is_variant = false
        $variants = ProductVariant::where('product_id', $product->id)->get();
        $this->assertCount(1, $variants);
        $this->assertEquals('TEST-VARIANT-001', $variants->first()->sku);
        
        echo "✅ Variant detection logic working correctly\n";
        echo "✅ Variants created when has_varient = true\n";
        
        return true;
    }
}
