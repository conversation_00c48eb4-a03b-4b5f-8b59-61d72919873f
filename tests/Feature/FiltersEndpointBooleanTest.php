<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\DropdownOption;

class FiltersEndpointBooleanTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run seeders to ensure we have the necessary data
        $this->artisan('db:seed', ['--class' => 'CategorySeeder']);
        $this->artisan('db:seed', ['--class' => 'BrandSeeder']);
        $this->artisan('db:seed', ['--class' => 'DropdownOptionSeeder']);
        $this->artisan('db:seed', ['--class' => 'ProductSeeder']);
    }

    /**
     * Test that boolean filters endpoint works without PostgreSQL errors
     */
    public function test_boolean_filters_endpoint_works_without_postgresql_errors()
    {
        $response = $this->getJson('/api/client/filters?category_id=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true])
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'boolean_filters' => [
                            'vegan' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ],
                            'vegetarian' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ],
                            'halal' => [
                                'label_en',
                                'label_ar',
                                'product_count'
                            ]
                        ]
                    ],
                    'message'
                ]);
    }

    /**
     * Test boolean filters return correct counts
     */
    public function test_boolean_filters_return_correct_counts()
    {
        // Create test products with boolean values
        $category = Category::first();
        $brand = Brand::first();
        
        // Create products with different boolean combinations
        Product::factory()->create([
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'is_vegan' => true,
            'is_vegetarian' => true,
            'is_halal' => false,
            'is_active' => true,
            'is_approved' => true,
        ]);

        Product::factory()->create([
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'is_vegan' => false,
            'is_vegetarian' => true,
            'is_halal' => true,
            'is_active' => true,
            'is_approved' => true,
        ]);

        Product::factory()->create([
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'is_vegan' => true,
            'is_vegetarian' => false,
            'is_halal' => true,
            'is_active' => true,
            'is_approved' => true,
        ]);

        $response = $this->getJson("/api/client/filters?category_id={$category->id}");

        $response->assertStatus(200);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify the counts are correct (at least the ones we created)
        $this->assertGreaterThanOrEqual(2, $booleanFilters['vegan']['product_count']);
        $this->assertGreaterThanOrEqual(2, $booleanFilters['vegetarian']['product_count']);
        $this->assertGreaterThanOrEqual(2, $booleanFilters['halal']['product_count']);
    }

    /**
     * Test boolean filters with no category filter
     */
    public function test_boolean_filters_without_category_filter()
    {
        $response = $this->getJson('/api/client/filters');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify structure
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
        
        // Verify each filter has required fields
        foreach (['vegan', 'vegetarian', 'halal'] as $filter) {
            $this->assertArrayHasKey('label_en', $booleanFilters[$filter]);
            $this->assertArrayHasKey('label_ar', $booleanFilters[$filter]);
            $this->assertArrayHasKey('product_count', $booleanFilters[$filter]);
            $this->assertIsInt($booleanFilters[$filter]['product_count']);
            $this->assertGreaterThanOrEqual(0, $booleanFilters[$filter]['product_count']);
        }
    }

    /**
     * Test boolean filters with subcategory filter
     */
    public function test_boolean_filters_with_subcategory_filter()
    {
        $response = $this->getJson('/api/client/filters?subcategory_id=2');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify structure is maintained
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
    }

    /**
     * Test boolean filters with brand filter
     */
    public function test_boolean_filters_with_brand_filter()
    {
        $response = $this->getJson('/api/client/filters?brand_id=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify structure is maintained
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
    }

    /**
     * Test boolean filters with search filter
     */
    public function test_boolean_filters_with_search_filter()
    {
        $response = $this->getJson('/api/client/filters?search=vitamin');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify structure is maintained
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
    }

    /**
     * Test boolean filters with multiple filters combined
     */
    public function test_boolean_filters_with_multiple_filters()
    {
        $response = $this->getJson('/api/client/filters?category_id=1&brand_id=1&search=protein');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify structure is maintained even with complex filtering
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
        
        // Verify counts are non-negative integers
        foreach (['vegan', 'vegetarian', 'halal'] as $filter) {
            $this->assertIsInt($booleanFilters[$filter]['product_count']);
            $this->assertGreaterThanOrEqual(0, $booleanFilters[$filter]['product_count']);
        }
    }

    /**
     * Test that boolean filters labels are correct
     */
    public function test_boolean_filters_labels_are_correct()
    {
        $response = $this->getJson('/api/client/filters');

        $response->assertStatus(200);
        
        $booleanFilters = $response->json('data.boolean_filters');
        
        // Verify vegan labels
        $this->assertEquals('Vegan', $booleanFilters['vegan']['label_en']);
        $this->assertEquals('نباتي', $booleanFilters['vegan']['label_ar']);
        
        // Verify vegetarian labels
        $this->assertEquals('Vegetarian', $booleanFilters['vegetarian']['label_en']);
        $this->assertEquals('نباتي', $booleanFilters['vegetarian']['label_ar']);
        
        // Verify halal labels
        $this->assertEquals('Halal', $booleanFilters['halal']['label_en']);
        $this->assertEquals('حلال', $booleanFilters['halal']['label_ar']);
    }

    /**
     * Test that the original PostgreSQL error is fixed
     */
    public function test_postgresql_boolean_comparison_error_is_fixed()
    {
        // This test specifically targets the original error:
        // SQLSTATE[42883]: Undefined function: 7 ERROR: operator does not exist: boolean = integer
        
        $response = $this->getJson('/api/client/filters?category_id=1');

        // If the fix works, this should return 200, not 500
        $response->assertStatus(200)
                ->assertJson(['status' => true]);
        
        // Verify that boolean filters are returned without errors
        $booleanFilters = $response->json('data.boolean_filters');
        $this->assertIsArray($booleanFilters);
        $this->assertArrayHasKey('vegan', $booleanFilters);
        $this->assertArrayHasKey('vegetarian', $booleanFilters);
        $this->assertArrayHasKey('halal', $booleanFilters);
    }
}
