<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\SupportReason;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Laravel\Passport\Passport;

class SupportTicketIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $supportCategory;
    protected $supportTopic;
    protected $supportReason;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin role and user
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'api']);
        
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        // Create support data
        $this->supportCategory = SupportCategory::create([
            'user_id' => $this->admin->id,
            'name' => 'General Support',
            'status' => 'active'
        ]);

        $this->supportTopic = SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->supportCategory->id,
            'name' => 'Product Issues',
            'status' => 'active'
        ]);

        $this->supportReason = SupportReason::create([
            'label' => 'Technical Support',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function admin_can_create_support_ticket()
    {
        Passport::actingAs($this->admin);

        $response = $this->postJson('/api/admin/support-tickets', [
            'subject' => 'Test Support Ticket',
            'message' => 'This is a test ticket message',
            'category_id' => $this->supportCategory->id,
            'topic_id' => $this->supportTopic->id,
            'reason_id' => $this->supportReason->id,
            'priority' => 'medium'
        ]);

        $response->assertStatus(201);
        
        $responseData = $response->json('data');
        $this->assertArrayHasKey('code', $responseData);
        $this->assertStringStartsWith('CA', $responseData['code']);
        $this->assertEquals('Test Support Ticket', $responseData['subject']);
    }

    /** @test */
    public function admin_can_list_support_tickets()
    {
        Passport::actingAs($this->admin);

        // Create a test ticket
        $ticket = SupportTicket::create([
            'user_id' => $this->admin->id,
            'subject' => 'Test Ticket',
            'message' => 'Test message',
            'category_id' => $this->supportCategory->id,
            'priority' => 'medium',
            'status' => 'open'
        ]);

        $response = $this->getJson('/api/admin/support-tickets');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
    }

    /** @test */
    public function admin_can_create_ticket_message()
    {
        Passport::actingAs($this->admin);

        $ticket = SupportTicket::create([
            'user_id' => $this->admin->id,
            'subject' => 'Test Ticket',
            'message' => 'Test message',
            'category_id' => $this->supportCategory->id,
            'priority' => 'medium',
            'status' => 'open'
        ]);

        $response = $this->postJson('/api/admin/support-ticket-messages', [
            'ticket_id' => $ticket->id,
            'message' => 'This is a reply message'
        ]);

        $response->assertStatus(201);
        
        $responseData = $response->json('data');
        $this->assertEquals('This is a reply message', $responseData['message']);
        $this->assertEquals($ticket->id, $responseData['ticket_id']);
    }

    /** @test */
    public function admin_can_get_threaded_messages()
    {
        Passport::actingAs($this->admin);

        $ticket = SupportTicket::create([
            'user_id' => $this->admin->id,
            'subject' => 'Test Ticket',
            'message' => 'Test message',
            'category_id' => $this->supportCategory->id,
            'priority' => 'medium',
            'status' => 'open'
        ]);

        // Create some messages
        SupportTicketMessage::create([
            'ticket_id' => $ticket->id,
            'sender_id' => $this->admin->id,
            'sender_type' => User::class,
            'message' => 'First message'
        ]);

        SupportTicketMessage::create([
            'ticket_id' => $ticket->id,
            'sender_id' => $this->admin->id,
            'sender_type' => User::class,
            'message' => 'Second message'
        ]);

        $response = $this->getJson("/api/admin/support-tickets/{$ticket->id}/messages");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertIsArray($responseData);

        // Just check that we have some messages
        $this->assertGreaterThanOrEqual(2, count($responseData));
    }

    /** @test */
    public function support_reasons_endpoint_works()
    {
        Passport::actingAs($this->admin);

        $response = $this->getJson('/api/admin/support-reasons');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
    }

    /** @test */
    public function support_reasons_active_list_endpoint_works()
    {
        Passport::actingAs($this->admin);

        // Create an active support reason
        SupportReason::create([
            'label' => 'Test Active Reason',
            'route_to' => 'admin',
            'code_prefix' => 'CA',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/admin/support-reasons/active-list');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);

        // Check that we have at least one active reason
        $this->assertGreaterThanOrEqual(1, count($responseData));

        // Check that the response contains the expected fields
        if (count($responseData) > 0) {
            $reason = $responseData[0];
            $this->assertArrayHasKey('id', $reason);
            $this->assertArrayHasKey('label', $reason);
            $this->assertArrayHasKey('route_to', $reason);
            $this->assertArrayHasKey('code_prefix', $reason);
        }
    }

    /** @test */
    public function support_categories_endpoint_works()
    {
        Passport::actingAs($this->admin);

        $response = $this->getJson('/api/admin/support-categories');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
    }

    /** @test */
    public function support_topics_endpoint_works()
    {
        Passport::actingAs($this->admin);

        $response = $this->getJson('/api/admin/support-topics');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
    }

    /** @test */
    public function ticket_auto_assignment_works()
    {
        Passport::actingAs($this->admin);

        $response = $this->postJson('/api/admin/support-tickets', [
            'subject' => 'Auto Assignment Test',
            'message' => 'Testing auto assignment',
            'reason_id' => $this->supportReason->id,
            'priority' => 'medium'
        ]);

        $response->assertStatus(201);
        
        $ticket = SupportTicket::where('subject', 'Auto Assignment Test')->first();
        $this->assertNotNull($ticket->assigned_to);
        $this->assertEquals($this->admin->id, $ticket->assigned_to);
    }
}
