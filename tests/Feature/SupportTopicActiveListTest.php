<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Laravel\Passport\Passport;

class SupportTopicActiveListTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $category1;
    protected $category2;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin role and user
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'api']);
        
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        // Create test categories
        $this->category1 = SupportCategory::create([
            'user_id' => $this->admin->id,
            'name' => 'Technical Issues',
            'status' => 'active'
        ]);

        $this->category2 = SupportCategory::create([
            'user_id' => $this->admin->id,
            'name' => 'Account Issues',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_returns_only_active_support_topics()
    {
        Passport::actingAs($this->admin);

        // Create active support topics
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Login Problems',
            'status' => 'active'
        ]);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Password Reset',
            'status' => 'active'
        ]);

        // Create inactive support topic
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Inactive Topic',
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(2, $responseData);

        // Check that only active topics are returned
        $names = array_column($responseData, 'name');
        $this->assertContains('Login Problems', $names);
        $this->assertContains('Password Reset', $names);
        $this->assertNotContains('Inactive Topic', $names);
    }

    /** @test */
    public function it_filters_topics_by_category_id_when_provided()
    {
        Passport::actingAs($this->admin);

        // Create topics in category 1
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Technical Issue 1',
            'status' => 'active'
        ]);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Technical Issue 2',
            'status' => 'active'
        ]);

        // Create topics in category 2
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category2->id,
            'name' => 'Account Issue 1',
            'status' => 'active'
        ]);

        // Test filtering by category 1
        $response = $this->getJson('/api/general/support-topics/active-list?category_id=' . $this->category1->id);

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(2, $responseData);

        // Check that only topics from category 1 are returned
        $names = array_column($responseData, 'name');
        $this->assertContains('Technical Issue 1', $names);
        $this->assertContains('Technical Issue 2', $names);
        $this->assertNotContains('Account Issue 1', $names);

        // Verify all returned topics have the correct category_id
        foreach ($responseData as $topic) {
            $this->assertEquals($this->category1->id, $topic['category_id']);
        }
    }

    /** @test */
    public function it_returns_all_active_topics_when_no_category_filter_provided()
    {
        Passport::actingAs($this->admin);

        // Create topics in different categories
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Technical Issue',
            'status' => 'active'
        ]);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category2->id,
            'name' => 'Account Issue',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(2, $responseData);

        // Check that topics from both categories are returned
        $names = array_column($responseData, 'name');
        $this->assertContains('Technical Issue', $names);
        $this->assertContains('Account Issue', $names);
    }

    /** @test */
    public function it_returns_topics_ordered_alphabetically_by_name()
    {
        Passport::actingAs($this->admin);

        // Create topics in non-alphabetical order
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Zebra Topic',
            'status' => 'active'
        ]);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Apple Topic',
            'status' => 'active'
        ]);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Beta Topic',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $names = array_column($responseData, 'name');
        
        // Check alphabetical order
        $this->assertEquals(['Apple Topic', 'Beta Topic', 'Zebra Topic'], $names);
    }

    /** @test */
    public function it_returns_essential_fields_for_dropdown()
    {
        Passport::actingAs($this->admin);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Test Topic',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(1, $responseData);

        $topic = $responseData[0];
        
        // Check that all essential fields are present
        $this->assertArrayHasKey('id', $topic);
        $this->assertArrayHasKey('name', $topic);
        $this->assertArrayHasKey('category_id', $topic);

        // Check field values
        $this->assertEquals('Test Topic', $topic['name']);
        $this->assertEquals($this->category1->id, $topic['category_id']);
    }

    /** @test */
    public function it_validates_category_id_parameter()
    {
        Passport::actingAs($this->admin);

        // Test with invalid category_id
        $response = $this->getJson('/api/general/support-topics/active-list?category_id=999999');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['category_id']);
    }

    /** @test */
    public function it_follows_standard_response_format()
    {
        Passport::actingAs($this->admin);

        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Test Topic',
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        // Check standard response structure
        $response->assertJsonStructure([
            'status',
            'message',
            'data'
        ]);

        $this->assertTrue($response->json('status'));
        $this->assertEquals('Active support topics retrieved successfully!', $response->json('message'));
    }

    /** @test */
    public function it_returns_empty_array_when_no_active_topics_exist()
    {
        Passport::actingAs($this->admin);

        // Create only inactive topics
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Inactive Topic',
            'status' => 'inactive'
        ]);

        $response = $this->getJson('/api/general/support-topics/active-list');

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(0, $responseData);
    }

    /** @test */
    public function it_returns_empty_array_when_category_has_no_active_topics()
    {
        Passport::actingAs($this->admin);

        // Create active topic in category 1
        SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->category1->id,
            'name' => 'Active Topic',
            'status' => 'active'
        ]);

        // Filter by category 2 (which has no topics)
        $response = $this->getJson('/api/general/support-topics/active-list?category_id=' . $this->category2->id);

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        $this->assertIsArray($responseData);
        $this->assertCount(0, $responseData);
    }
}
