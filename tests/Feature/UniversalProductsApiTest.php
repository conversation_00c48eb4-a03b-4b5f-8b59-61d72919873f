<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UniversalProductsApiTest extends TestCase
{
    /**
     * Test basic products API endpoint
     */
    public function test_products_api_returns_successful_response()
    {
        $response = $this->getJson('/api/client/products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'title_ar',
                                'short_name',
                                'regular_price',
                                'offer_price',
                                'primary_image',
                                'brand',
                                'user_group',
                                'country_of_origin'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total_items',
                        'total_pages',
                        'has_more_pages'
                    ],
                    'message'
                ]);
    }

    /**
     * Test category filtering
     */
    public function test_category_filtering()
    {
        $response = $this->getJson('/api/client/products?category_id=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify all products belong to category 1
        $products = $response->json('data.data');
        $this->assertNotEmpty($products);
    }

    /**
     * Test subcategory filtering
     */
    public function test_subcategory_filtering()
    {
        $response = $this->getJson('/api/client/products?subcategory_id=2');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test single brand filtering
     */
    public function test_single_brand_filtering()
    {
        $response = $this->getJson('/api/client/products?brand_id[]=1');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify all products belong to brand 1
        $products = $response->json('data.data');
        foreach ($products as $product) {
            if ($product['brand']) {
                $this->assertEquals(1, $product['brand']['id']);
            }
        }
    }

    /**
     * Test multiple brand filtering
     */
    public function test_multiple_brand_filtering()
    {
        $response = $this->getJson('/api/client/products?brand_id[]=1&brand_id[]=2');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify all products belong to brand 1 or 2
        $products = $response->json('data.data');
        foreach ($products as $product) {
            if ($product['brand']) {
                $this->assertContains($product['brand']['id'], [1, 2]);
            }
        }
    }

    /**
     * Test user group filtering
     */
    public function test_user_group_filtering()
    {
        $response = $this->getJson('/api/client/products?user_group_id[]=9');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test country of origin filtering
     */
    public function test_country_of_origin_filtering()
    {
        $response = $this->getJson('/api/client/products?country_of_origin_id[]=298');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);
    }

    /**
     * Test price range filtering
     */
    public function test_price_range_filtering()
    {
        $response = $this->getJson('/api/client/products?min_price=90&max_price=150');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify all products are within price range
        $products = $response->json('data.data');
        foreach ($products as $product) {
            $price = floatval($product['regular_price']);
            $this->assertGreaterThanOrEqual(90, $price);
            $this->assertLessThanOrEqual(150, $price);
        }
    }

    /**
     * Test search functionality
     */
    public function test_search_functionality()
    {
        $response = $this->getJson('/api/client/products?search=vitamin');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify search results contain the search term
        $products = $response->json('data.data');
        foreach ($products as $product) {
            $searchFound = stripos($product['title_en'], 'vitamin') !== false ||
                          stripos($product['title_ar'], 'vitamin') !== false ||
                          stripos($product['short_name'], 'vitamin') !== false;
            $this->assertTrue($searchFound, 'Product should contain search term');
        }
    }

    /**
     * Test sorting by price ascending
     */
    public function test_sorting_price_ascending()
    {
        $response = $this->getJson('/api/client/products?sort_by=regular_price&sort_order=asc&per_page=5');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify products are sorted by price ascending
        $products = $response->json('data.data');
        $prices = array_map(function($product) {
            return floatval($product['regular_price']);
        }, $products);

        $sortedPrices = $prices;
        sort($sortedPrices);
        $this->assertEquals($sortedPrices, $prices);
    }

    /**
     * Test sorting by price descending
     */
    public function test_sorting_price_descending()
    {
        $response = $this->getJson('/api/client/products?sort_by=regular_price&sort_order=desc&per_page=5');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        // Verify products are sorted by price descending
        $products = $response->json('data.data');
        $prices = array_map(function($product) {
            return floatval($product['regular_price']);
        }, $products);

        $sortedPrices = $prices;
        rsort($sortedPrices);
        $this->assertEquals($sortedPrices, $prices);
    }

    /**
     * Test pagination
     */
    public function test_pagination()
    {
        // Test first page
        $response1 = $this->getJson('/api/client/products?page=1&per_page=2');
        $response1->assertStatus(200)
                 ->assertJson(['status' => true]);

        $page1Data = $response1->json('data');
        $this->assertEquals(1, $page1Data['current_page']);
        $this->assertEquals(2, $page1Data['per_page']);

        // Test second page
        $response2 = $this->getJson('/api/client/products?page=2&per_page=2');
        $response2->assertStatus(200)
                 ->assertJson(['status' => true]);

        $page2Data = $response2->json('data');
        $this->assertEquals(2, $page2Data['current_page']);

        // Verify different products on different pages
        $page1Products = $page1Data['data'];
        $page2Products = $page2Data['data'];
        
        if (!empty($page1Products) && !empty($page2Products)) {
            $this->assertNotEquals($page1Products[0]['id'], $page2Products[0]['id']);
        }
    }

    /**
     * Test boolean filters
     */
    public function test_boolean_filters()
    {
        // Test vegan filter
        $response = $this->getJson('/api/client/products?is_vegan=true');
        $response->assertStatus(200)->assertJson(['status' => true]);

        // Test vegetarian filter
        $response = $this->getJson('/api/client/products?is_vegetarian=true');
        $response->assertStatus(200)->assertJson(['status' => true]);

        // Test halal filter
        $response = $this->getJson('/api/client/products?is_halal=true');
        $response->assertStatus(200)->assertJson(['status' => true]);
    }

    /**
     * Test combined filters
     */
    public function test_combined_filters()
    {
        $response = $this->getJson('/api/client/products?category_id=1&brand_id[]=1&min_price=90&max_price=150&sort_by=regular_price&sort_order=asc&per_page=3');

        $response->assertStatus(200)
                ->assertJson(['status' => true]);

        $products = $response->json('data.data');
        
        // Verify combined filtering works
        foreach ($products as $product) {
            // Check price range
            $price = floatval($product['regular_price']);
            $this->assertGreaterThanOrEqual(90, $price);
            $this->assertLessThanOrEqual(150, $price);
            
            // Check brand if present
            if ($product['brand']) {
                $this->assertEquals(1, $product['brand']['id']);
            }
        }
    }

    /**
     * Test per_page limits
     */
    public function test_per_page_limits()
    {
        // Test normal per_page
        $response = $this->getJson('/api/client/products?per_page=5');
        $response->assertStatus(200);
        $this->assertLessThanOrEqual(5, count($response->json('data.data')));

        // Test max per_page limit (should be capped at 50)
        $response = $this->getJson('/api/client/products?per_page=100');
        $response->assertStatus(200);
        $this->assertLessThanOrEqual(50, $response->json('data.per_page'));
    }

    /**
     * Test invalid parameters
     */
    public function test_invalid_parameters()
    {
        // Test invalid category_id
        $response = $this->getJson('/api/client/products?category_id=99999');
        $response->assertStatus(200); // Should still return 200 but with empty results

        // Test invalid sort field (should fallback to default)
        $response = $this->getJson('/api/client/products?sort_by=invalid_field');
        $response->assertStatus(200);
    }

    /**
     * Test response structure consistency
     */
    public function test_response_structure_consistency()
    {
        $response = $this->getJson('/api/client/products?per_page=1');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title_en',
                                'title_ar',
                                'short_name',
                                'regular_price',
                                'offer_price'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total_items',
                        'total_pages',
                        'has_more_pages'
                    ],
                    'message'
                ]);

        // Verify data types
        $data = $response->json('data');
        $this->assertIsInt($data['current_page']);
        $this->assertIsInt($data['per_page']);
        $this->assertIsInt($data['total_items']);
        $this->assertIsInt($data['total_pages']);
        $this->assertIsBool($data['has_more_pages']);
    }
}
