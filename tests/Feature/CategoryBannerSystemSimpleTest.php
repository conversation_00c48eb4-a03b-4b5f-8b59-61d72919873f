<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Category;
use App\Models\Banner;
use App\Models\BannerItem;
use App\Models\User;
use App\Services\CategoryService;
use App\Services\CategoryInformationService;

class CategoryBannerSystemSimpleTest extends TestCase
{
    use RefreshDatabase;

    private $user;
    private $banner;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        
        // Create a test banner with items
        $this->banner = Banner::create([
            'user_id' => $this->user->id,
            'title' => 'Test Category Banner',
            'description' => 'Test banner for categories',
            'type' => 'category',
            'is_active' => true,
        ]);
        
        // Create banner items
        BannerItem::create([
            'banner_id' => $this->banner->id,
            'user_id' => $this->user->id,
            'title_en' => 'Banner Item 1',
            'title_ar' => 'عنصر البانر 1',
            'media_path' => 'banners/test-image-1.jpg',
            'link_url' => 'https://example.com/1',
            'target' => '_blank',
            'alt_text' => 'Test banner image 1',
            'position' => 1,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function category_model_has_banner_relationship()
    {
        $category = new Category();
        $this->assertTrue(method_exists($category, 'banner'));
    }

    /** @test */
    public function banner_model_has_items_relationship()
    {
        $banner = new Banner();
        $this->assertTrue(method_exists($banner, 'items'));
    }

    /** @test */
    public function category_can_be_created_with_banner_id()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Test Category',
            'name_ar' => 'فئة الاختبار',
            'type' => 'main',
            'slug' => 'test-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $this->assertInstanceOf(Category::class, $category);
        $this->assertEquals($this->banner->id, $category->banner_id);
        $this->assertDatabaseHas('categories', [
            'name_en' => 'Test Category',
            'banner_id' => $this->banner->id,
        ]);
    }

    /** @test */
    public function category_can_be_created_without_banner_id()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Test Category No Banner',
            'name_ar' => 'فئة بدون بانر',
            'type' => 'main',
            'slug' => 'test-category-no-banner',
            'banner_id' => null,
            'status' => 'active',
        ]);
        
        $this->assertInstanceOf(Category::class, $category);
        $this->assertNull($category->banner_id);
        $this->assertDatabaseHas('categories', [
            'name_en' => 'Test Category No Banner',
            'banner_id' => null,
        ]);
    }

    /** @test */
    public function category_banner_relationship_works()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Category with Banner',
            'name_ar' => 'فئة مع البانر',
            'type' => 'main',
            'slug' => 'category-with-banner',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        // Test the relationship
        $categoryWithBanner = Category::with('banner')->find($category->id);
        $this->assertInstanceOf(Banner::class, $categoryWithBanner->banner);
        $this->assertEquals('Test Category Banner', $categoryWithBanner->banner->title);
    }

    /** @test */
    public function category_banner_items_relationship_works()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Category with Banner Items',
            'name_ar' => 'فئة مع عناصر البانر',
            'type' => 'main',
            'slug' => 'category-with-banner-items',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        // Test the nested relationship
        $categoryWithBanner = Category::with(['banner.items'])->find($category->id);
        $this->assertInstanceOf(Banner::class, $categoryWithBanner->banner);
        $this->assertGreaterThan(0, $categoryWithBanner->banner->items->count());
        $this->assertEquals('Banner Item 1', $categoryWithBanner->banner->items->first()->title_en);
    }

    /** @test */
    public function category_service_loads_banner_relationship()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Service Test Category',
            'name_ar' => 'فئة اختبار الخدمة',
            'type' => 'main',
            'slug' => 'service-test-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $categoryService = new CategoryService();
        $result = $categoryService->show($category->id);
        
        $this->assertInstanceOf(Category::class, $result);
        $this->assertTrue($result->relationLoaded('banner'));
        if ($result->banner) {
            $this->assertTrue($result->banner->relationLoaded('items'));
        }
    }

    /** @test */
    public function category_information_service_includes_banner_data()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Info Service Category',
            'name_ar' => 'فئة خدمة المعلومات',
            'type' => 'main',
            'slug' => 'info-service-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $categoryInfoService = new CategoryInformationService();
        $result = $categoryInfoService->getCategoryBySlug('info-service-category');
        
        $this->assertArrayHasKey('category', $result);
        $this->assertArrayHasKey('banner', $result['category']);
        $this->assertNotNull($result['category']['banner']);
        $this->assertEquals('Test Category Banner', $result['category']['banner']['title']);
        $this->assertArrayHasKey('items', $result['category']['banner']);
        $this->assertGreaterThan(0, count($result['category']['banner']['items']));
    }

    /** @test */
    public function category_information_service_handles_null_banner()
    {
        $category = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'No Banner Category',
            'name_ar' => 'فئة بدون بانر',
            'type' => 'main',
            'slug' => 'no-banner-category',
            'banner_id' => null,
            'status' => 'active',
        ]);
        
        $categoryInfoService = new CategoryInformationService();
        $result = $categoryInfoService->getCategoryBySlug('no-banner-category');
        
        $this->assertArrayHasKey('category', $result);
        $this->assertArrayHasKey('banner', $result['category']);
        $this->assertNull($result['category']['banner']);
    }

    /** @test */
    public function subcategory_information_service_includes_banner_data()
    {
        // Create parent category
        $parentCategory = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Parent Category',
            'name_ar' => 'الفئة الأب',
            'type' => 'main',
            'slug' => 'parent-category',
            'status' => 'active',
        ]);
        
        // Create subcategory with banner
        $subcategory = Category::create([
            'user_id' => $this->user->id,
            'name_en' => 'Sub Category',
            'name_ar' => 'الفئة الفرعية',
            'type' => 'sub',
            'parent_id' => $parentCategory->id,
            'slug' => 'sub-category',
            'banner_id' => $this->banner->id,
            'status' => 'active',
        ]);
        
        $categoryInfoService = new CategoryInformationService();
        $result = $categoryInfoService->getSubcategoryBySlug('sub-category');
        
        $this->assertArrayHasKey('category', $result);
        $this->assertArrayHasKey('subcategory', $result['category']);
        $this->assertArrayHasKey('banner', $result['category']['subcategory']);
        $this->assertNotNull($result['category']['subcategory']['banner']);
        $this->assertEquals('Test Category Banner', $result['category']['subcategory']['banner']['title']);
    }

    /** @test */
    public function database_schema_is_correct()
    {
        $columns = \Illuminate\Support\Facades\Schema::getColumnListing('categories');
        
        $this->assertContains('banner_id', $columns);
        $this->assertNotContains('banner', $columns);
        $this->assertNotContains('cover_image', $columns);
    }
}