<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Tpl;
use App\Models\SupportReason;
use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Passport\Passport;

class SupportTicketSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $admin;
    protected $vendor;
    protected $tpl;
    protected $supportReason;
    protected $supportCategory;
    protected $supportTopic;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles for both guards
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'vendor', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'tpl', 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::create(['name' => 'vendor', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::create(['name' => 'tpl', 'guard_name' => 'api']);

        // Create test users
        $this->user = User::factory()->create();
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        $this->vendor = User::factory()->create();
        $this->vendor->assignRole('vendor');

        $this->tpl = User::factory()->create();
        $this->tpl->assignRole('tpl');

        // Create support data
        $this->supportCategory = SupportCategory::create([
            'user_id' => $this->admin->id,
            'name' => 'General Support',
            'status' => 'active'
        ]);

        $this->supportTopic = SupportTopic::create([
            'user_id' => $this->admin->id,
            'category_id' => $this->supportCategory->id,
            'name' => 'Product Issues',
            'status' => 'active'
        ]);

        $this->supportReason = SupportReason::create([
            'label' => 'Refund Request',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_generates_correct_ticket_codes_for_customer_to_admin()
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/support-tickets', [
            'subject' => 'Test Ticket',
            'message' => 'Test message',
            'category_id' => $this->supportCategory->id,
            'topic_id' => $this->supportTopic->id,
        ]);

        $response->assertStatus(201);
        $ticket = SupportTicket::first();
        
        // Should start with CA (Customer → Admin)
        $this->assertStringStartsWith('CA', $ticket->code);
        
        // Should have format: CA + YYMMDD + 6-digit sequence
        $expectedDate = now()->format('ymd');
        $this->assertStringContainsString($expectedDate, $ticket->code);
        $this->assertEquals(14, strlen($ticket->code)); // CA(2) + YYMMDD(6) + sequence(6) = 14 chars
    }

    /** @test */
    public function it_generates_correct_ticket_codes_for_customer_to_vendor()
    {
        Passport::actingAs($this->user);
        
        $vendor = Vendor::factory()->create(['user_id' => $this->vendor->id]);

        $response = $this->postJson('/api/support-tickets', [
            'subject' => 'Vendor Issue',
            'message' => 'Test message',
            'vendor_id' => $vendor->id,
            'reason_id' => $this->supportReason->id,
        ]);

        $response->assertStatus(201);
        $ticket = SupportTicket::first();
        
        // Should start with CV (Customer → Vendor)
        $this->assertStringStartsWith('CV', $ticket->code);
    }

    /** @test */
    public function it_auto_assigns_tickets_based_on_reason()
    {
        Passport::actingAs($this->user);
        
        $vendor = Vendor::factory()->create(['user_id' => $this->vendor->id]);

        $response = $this->postJson('/api/support-tickets', [
            'subject' => 'Vendor Issue',
            'message' => 'Test message',
            'vendor_id' => $vendor->id,
            'reason_id' => $this->supportReason->id,
        ]);

        $response->assertStatus(201);
        $ticket = SupportTicket::first();
        
        // Should be assigned to vendor user
        $this->assertEquals($this->vendor->id, $ticket->assigned_to);
    }

    /** @test */
    public function it_sends_admin_cc_for_vendor_tickets()
    {
        Passport::actingAs($this->user);
        
        $vendor = Vendor::factory()->create(['user_id' => $this->vendor->id]);

        $response = $this->postJson('/api/support-tickets', [
            'subject' => 'Vendor Issue',
            'message' => 'Test message',
            'vendor_id' => $vendor->id,
            'reason_id' => $this->supportReason->id,
        ]);

        $response->assertStatus(201);
        
        // Check if admin received CC notification
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->admin->id,
            'type' => 'support_ticket_cc'
        ]);
    }

    /** @test */
    public function it_creates_threaded_messages()
    {
        Passport::actingAs($this->user);
        
        $ticket = SupportTicket::factory()->create([
            'user_id' => $this->user->id,
            'assigned_to' => $this->admin->id
        ]);

        // Create initial message
        $response = $this->postJson('/api/support-ticket-messages', [
            'ticket_id' => $ticket->id,
            'message' => 'Initial message'
        ]);

        $response->assertStatus(201);

        // Admin replies
        Passport::actingAs($this->admin);
        
        $response = $this->postJson('/api/support-ticket-messages', [
            'ticket_id' => $ticket->id,
            'message' => 'Admin reply'
        ]);

        $response->assertStatus(201);

        // Get threaded messages
        $response = $this->getJson("/api/support-tickets/{$ticket->id}/messages");
        
        $response->assertStatus(200);
        $messages = $response->json('data');
        
        $this->assertCount(2, $messages);
        $this->assertEquals('Initial message', $messages[0]['message']);
        $this->assertEquals('Admin reply', $messages[1]['message']);
    }

    /** @test */
    public function it_ensures_ticket_code_uniqueness()
    {
        Passport::actingAs($this->user);

        // Create multiple tickets on the same day
        for ($i = 0; $i < 5; $i++) {
            $response = $this->postJson('/api/support-tickets', [
                'subject' => "Test Ticket {$i}",
                'message' => 'Test message',
                'category_id' => $this->supportCategory->id,
            ]);
            
            $response->assertStatus(201);
        }

        $tickets = SupportTicket::all();
        $codes = $tickets->pluck('code')->toArray();
        
        // All codes should be unique
        $this->assertEquals(count($codes), count(array_unique($codes)));
        
        // Codes should be sequential
        $sequences = array_map(function($code) {
            return (int)substr($code, -6);
        }, $codes);
        
        sort($sequences);
        $this->assertEquals([1, 2, 3, 4, 5], $sequences);
    }

    /** @test */
    public function it_updates_ticket_status_on_new_message()
    {
        Passport::actingAs($this->user);
        
        $ticket = SupportTicket::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'resolved'
        ]);

        // Add new message to resolved ticket
        $response = $this->postJson('/api/support-ticket-messages', [
            'ticket_id' => $ticket->id,
            'message' => 'Follow up message'
        ]);

        $response->assertStatus(201);
        
        // Ticket status should change to in_progress
        $ticket->refresh();
        $this->assertEquals('in_progress', $ticket->status);
    }

    /** @test */
    public function it_maintains_api_compatibility()
    {
        Passport::actingAs($this->user);

        // Test all existing endpoints still work
        $endpoints = [
            'support-categories',
            'support-topics', 
            'support-reasons',
            'support-tickets',
            'support-ticket-messages'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson("/api/{$endpoint}");
            $this->assertTrue(in_array($response->status(), [200, 201]));
        }
    }
}
