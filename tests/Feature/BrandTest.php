<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BrandTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role for testing
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'api']);

        // Create a user for authentication with admin role
        $this->user = User::factory()->create();
        $this->user->assignRole('admin');
    }

    /** @test */
    public function it_can_list_brands()
    {
        // Create some brands
        Brand::factory()->count(5)->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/admin/brands');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name_en',
                            'name_ar',
                            'slug',
                            'status',
                            'is_active',
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_create_a_brand()
    {
        $brandData = [
            'name_en' => 'Test Brand',
            'name_ar' => 'علامة تجارية تجريبية',
            'slug' => 'test-brand',
            'country_of_origin' => 'UAE',
            'is_trademark_registered' => true,
            'website' => 'https://testbrand.com',
            'instagram' => 'testbrand_official',
            'facebook' => 'testbrand',
            'manufacturer' => true,
            'brand_owner' => true,
            'skus_on_brand_website' => 50,
            'skus_on_amazon' => 25,
            'mohap_registration' => 'yes',
            'brand_usp' => 'Quality products for everyone',
            'top_products' => ['Product A', 'Product B', 'Product C'],
            'is_active' => true,
            'status' => 'pending',
        ];

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/brands', $brandData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'name_en',
                    'name_ar',
                    'slug',
                    'country_of_origin',
                    'is_active',
                ]
            ]);

        $this->assertDatabaseHas('brands', [
            'name_en' => 'Test Brand',
            'slug' => 'test-brand',
            'country_of_origin' => 'UAE',
        ]);
    }

    /** @test */
    public function it_can_show_a_brand()
    {
        $brand = Brand::factory()->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/admin/brands/{$brand->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'name_en',
                    'name_ar',
                    'slug',
                    'country_of_origin',
                    'is_active',
                ]
            ])
            ->assertJson([
                'data' => [
                    'id' => $brand->id,
                    'name_en' => $brand->name_en,
                    'slug' => $brand->slug,
                ]
            ]);
    }

    /** @test */
    public function it_can_update_a_brand()
    {
        $brand = Brand::factory()->create();

        $updateData = [
            'name_en' => 'Updated Brand Name',
            'slug' => $brand->slug, // Keep the original slug
            'country_of_origin' => 'Saudi Arabia',
            'brand_usp' => 'Updated unique selling proposition',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->user, 'api')
            ->putJson("/api/admin/brands/{$brand->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'name_en',
                    'country_of_origin',
                    'brand_usp',
                    'is_active',
                ]
            ]);

        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
            'name_en' => 'Updated Brand Name',
            'country_of_origin' => 'Saudi Arabia',
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_delete_a_brand()
    {
        $brand = Brand::factory()->create();

        $response = $this->actingAs($this->user, 'api')
            ->deleteJson("/api/admin/brands/{$brand->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Brand deleted successfully!',
            ]);

        $this->assertDatabaseMissing('brands', [
            'id' => $brand->id,
        ]);
    }

    /** @test */
    public function it_can_get_active_brands_list()
    {
        // Create active and inactive brands
        Brand::factory()->count(3)->active()->create();
        Brand::factory()->count(2)->rejected()->create();

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/general/brands/active-list');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name_en',
                        'name_ar',
                        'slug',
                        'logo',
                        'country_of_origin',
                    ]
                ]
            ]);

        // Should only return active brands
        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function it_validates_required_fields_when_creating_brand()
    {
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/brands', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name_en', 'slug']);
    }

    /** @test */
    public function it_validates_unique_fields_when_creating_brand()
    {
        $existingBrand = Brand::factory()->create();

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/brands', [
                'name_en' => $existingBrand->name_en,
                'slug' => $existingBrand->slug,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name_en', 'slug']);
    }

    /** @test */
    public function it_validates_enum_fields()
    {
        $brandData = [
            'name_en' => 'Test Brand',
            'slug' => 'test-brand-enum',
            'mohap_registration' => 'invalid_value',
            'status' => 'invalid_status',
        ];

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/admin/brands', $brandData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mohap_registration', 'status']);
    }
}
