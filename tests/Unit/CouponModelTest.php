<?php

namespace Tests\Unit;

use App\Models\Coupon;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CouponModelTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_check_if_coupon_is_expired()
    {
        // Expired coupon
        $expiredCoupon = Coupon::factory()->create([
            'end_date' => now()->subDay(),
        ]);

        // Active coupon
        $activeCoupon = Coupon::factory()->create([
            'end_date' => now()->addDay(),
        ]);

        // Coupon with no end date
        $noEndDateCoupon = Coupon::factory()->create([
            'end_date' => null,
        ]);

        $this->assertTrue($expiredCoupon->is_expired);
        $this->assertFalse($activeCoupon->is_expired);
        $this->assertFalse($noEndDateCoupon->is_expired);
    }

    /** @test */
    public function it_can_check_if_coupon_has_started()
    {
        // Not started coupon
        $notStartedCoupon = Coupon::factory()->create([
            'start_date' => now()->addDay(),
        ]);

        // Started coupon
        $startedCoupon = Coupon::factory()->create([
            'start_date' => now()->subDay(),
        ]);

        // Coupon with no start date
        $noStartDateCoupon = Coupon::factory()->create([
            'start_date' => null,
        ]);

        $this->assertFalse($notStartedCoupon->is_started);
        $this->assertTrue($startedCoupon->is_started);
        $this->assertTrue($noStartDateCoupon->is_started);
    }

    /** @test */
    public function it_can_calculate_remaining_uses()
    {
        $coupon = Coupon::factory()->create([
            'usage_limit' => 100,
        ]);

        // Since usage_count is currently a placeholder returning 0
        $this->assertEquals(100, $coupon->remaining_uses);

        // Coupon with no usage limit
        $unlimitedCoupon = Coupon::factory()->create([
            'usage_limit' => null,
        ]);

        $this->assertNull($unlimitedCoupon->remaining_uses);
    }

    /** @test */
    public function it_can_check_if_coupon_is_valid_for_use()
    {
        // Valid coupon
        $validCoupon = Coupon::factory()->create([
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'usage_limit' => 100,
        ]);

        $this->assertTrue($validCoupon->isValidForUse());

        // Inactive coupon
        $inactiveCoupon = Coupon::factory()->create([
            'is_active' => false,
        ]);

        $this->assertFalse($inactiveCoupon->isValidForUse());

        // Expired coupon
        $expiredCoupon = Coupon::factory()->create([
            'is_active' => true,
            'end_date' => now()->subDay(),
        ]);

        $this->assertFalse($expiredCoupon->isValidForUse());

        // Not started coupon
        $notStartedCoupon = Coupon::factory()->create([
            'is_active' => true,
            'start_date' => now()->addDay(),
        ]);

        $this->assertFalse($notStartedCoupon->isValidForUse());
    }

    /** @test */
    public function it_can_calculate_discount_for_percentage_coupons()
    {
        $percentageCoupon = Coupon::factory()->create([
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 100,
        ]);

        // Order value meets minimum
        $discount = $percentageCoupon->calculateDiscount(150);
        $this->assertEquals(30, $discount); // 20% of 150

        // Order value below minimum
        $discount = $percentageCoupon->calculateDiscount(50);
        $this->assertEquals(0, $discount);
    }

    /** @test */
    public function it_can_calculate_discount_for_fixed_coupons()
    {
        $fixedCoupon = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 50,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 100,
        ]);

        // Order value meets minimum
        $discount = $fixedCoupon->calculateDiscount(150);
        $this->assertEquals(50, $discount);

        // Order value below minimum
        $discount = $fixedCoupon->calculateDiscount(80);
        $this->assertEquals(0, $discount);

        // Fixed discount cannot exceed order value
        $discount = $fixedCoupon->calculateDiscount(30);
        $this->assertEquals(0, $discount); // Below minimum order value
    }

    /** @test */
    public function it_can_get_localized_title()
    {
        $coupon = Coupon::factory()->create([
            'title_en' => 'English Title',
            'title_ar' => 'العنوان العربي',
        ]);

        // English locale
        app()->setLocale('en');
        $this->assertEquals('English Title', $coupon->getLocalizedTitle());

        // Arabic locale
        app()->setLocale('ar');
        $this->assertEquals('العنوان العربي', $coupon->getLocalizedTitle());

        // Explicit locale parameter
        $this->assertEquals('English Title', $coupon->getLocalizedTitle('en'));
        $this->assertEquals('العنوان العربي', $coupon->getLocalizedTitle('ar'));
    }

    /** @test */
    public function it_can_get_localized_description()
    {
        $coupon = Coupon::factory()->create([
            'description_en' => 'English Description',
            'description_ar' => 'الوصف العربي',
        ]);

        // English locale
        app()->setLocale('en');
        $this->assertEquals('English Description', $coupon->getLocalizedDescription());

        // Arabic locale
        app()->setLocale('ar');
        $this->assertEquals('الوصف العربي', $coupon->getLocalizedDescription());

        // Explicit locale parameter
        $this->assertEquals('English Description', $coupon->getLocalizedDescription('en'));
        $this->assertEquals('الوصف العربي', $coupon->getLocalizedDescription('ar'));
    }

    /** @test */
    public function it_can_get_formatted_value()
    {
        $percentageCoupon = Coupon::factory()->create([
            'type' => 'percentage',
            'value' => 25,
        ]);

        $fixedCoupon = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 150.50,
        ]);

        $this->assertEquals('25%', $percentageCoupon->formatted_value);
        $this->assertEquals('AED 150.50', $fixedCoupon->formatted_value);
    }

    /** @test */
    public function it_can_get_formatted_min_order_value()
    {
        $couponWithMinOrder = Coupon::factory()->create([
            'min_order_value' => 100.75,
        ]);

        $couponWithoutMinOrder = Coupon::factory()->create([
            'min_order_value' => null,
        ]);

        $this->assertEquals('AED 100.75', $couponWithMinOrder->formatted_min_order_value);
        $this->assertNull($couponWithoutMinOrder->formatted_min_order_value);
    }

    /** @test */
    public function it_has_proper_relationships()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create();

        $coupon = Coupon::factory()->create([
            'user_id' => $user->id,
            'vendor_id' => $vendor->id,
        ]);

        $this->assertInstanceOf(User::class, $coupon->user);
        $this->assertInstanceOf(Vendor::class, $coupon->vendor);
        $this->assertEquals($user->id, $coupon->user->id);
        $this->assertEquals($vendor->id, $coupon->vendor->id);
    }

    /** @test */
    public function it_has_proper_scopes()
    {
        // Create test data
        $activeCoupon = Coupon::factory()->active()->create();
        $inactiveCoupon = Coupon::factory()->inactive()->create();
        $expiredCoupon = Coupon::factory()->expired()->create();
        $validCoupon = Coupon::factory()->valid()->create();
        $vendor = Vendor::factory()->create();
        $vendorCoupon = Coupon::factory()->create(['vendor_id' => $vendor->id]);
        $platformCoupon = Coupon::factory()->platformWide()->create();

        // Test scopes
        $this->assertTrue(Coupon::active()->get()->contains($activeCoupon));
        $this->assertFalse(Coupon::active()->get()->contains($inactiveCoupon));

        $this->assertTrue(Coupon::valid()->get()->contains($validCoupon));
        $this->assertFalse(Coupon::valid()->get()->contains($expiredCoupon));

        $this->assertTrue(Coupon::forVendor($vendor->id)->get()->contains($vendorCoupon));
        $this->assertFalse(Coupon::forVendor($vendor->id)->get()->contains($platformCoupon));

        $this->assertTrue(Coupon::platformWide()->get()->contains($platformCoupon));
        $this->assertFalse(Coupon::platformWide()->get()->contains($vendorCoupon));
    }
}
