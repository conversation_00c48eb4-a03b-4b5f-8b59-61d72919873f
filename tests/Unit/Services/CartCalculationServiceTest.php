<?php

namespace Tests\Unit\Services;

use App\Models\CartItem;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\Vendor;
use App\Services\CartCalculationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Tests\TestCase;

class CartCalculationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartCalculationService $calculationService;
    protected ShoppingCart $cart;
    protected Vendor $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->calculationService = new CartCalculationService();
        
        $this->vendor = Vendor::factory()->active()->create([
            'free_shipping_threshold' => 500.00,
        ]);

        $this->cart = ShoppingCart::factory()->create([
            'currency' => 'AED',
            'subtotal' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'shipping_amount' => 0,
            'total_amount' => 0,
        ]);
    }

    public function test_calculate_subtotal(): void
    {
        $items = collect([
            (object) ['total_price' => 100.00],
            (object) ['total_price' => 150.00],
            (object) ['total_price' => 75.50],
        ]);

        $subtotal = $this->calculationService->calculateSubtotal($items);

        $this->assertEquals(325.50, $subtotal);
    }

    public function test_calculate_tax(): void
    {
        $product1 = Product::factory()->create(['is_taxable' => true]);
        $product2 = Product::factory()->create(['is_taxable' => false]);
        $product3 = Product::factory()->create(['is_taxable' => true]);

        $item1 = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product1->id,
            'total_price' => 100.00,
            'discount_amount' => 0,
        ]);

        $item2 = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product2->id,
            'total_price' => 200.00,
            'discount_amount' => 0,
        ]);

        $item3 = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product3->id,
            'total_price' => 150.00,
            'discount_amount' => 10.00,
        ]);

        $this->cart->load('items.product');

        $taxAmount = $this->calculationService->calculateTax($this->cart);

        // Tax should be 5% on taxable items only
        // Item 1: 100.00 * 0.05 = 5.00
        // Item 2: Not taxable = 0.00
        // Item 3: (150.00 - 10.00) * 0.05 = 7.00
        // Total: 12.00
        $this->assertEquals(12.00, $taxAmount);
    }

    public function test_calculate_shipping_with_free_shipping_threshold(): void
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);

        // Cart with subtotal above free shipping threshold
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $this->vendor->id,
            'total_price' => 600.00, // Above 500.00 threshold
        ]);

        $this->cart->load('items.vendor');

        $shippingAmount = $this->calculationService->calculateShipping($this->cart);

        $this->assertEquals(0, $shippingAmount); // Free shipping
    }

    public function test_calculate_shipping_below_threshold(): void
    {
        $product = Product::factory()->create(['vendor_id' => $this->vendor->id]);

        // Cart with subtotal below free shipping threshold
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $this->vendor->id,
            'total_price' => 300.00, // Below 500.00 threshold
        ]);

        $this->cart->load('items.vendor');

        $shippingAmount = $this->calculationService->calculateShipping($this->cart);

        $this->assertGreaterThan(0, $shippingAmount); // Should have shipping cost
    }

    public function test_calculate_discounts(): void
    {
        // Create cart items with discounts
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'discount_amount' => 25.00,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'discount_amount' => 15.00,
        ]);

        // Add applied coupons
        $this->cart->update([
            'applied_coupons' => [
                ['code' => 'TEST10', 'discount_amount' => 50.00],
                ['code' => 'SAVE20', 'discount_amount' => 30.00],
            ],
        ]);

        $this->cart->load('items');

        $totalDiscount = $this->calculationService->calculateDiscounts($this->cart);

        // Item discounts: 25.00 + 15.00 = 40.00
        // Coupon discounts: 50.00 + 30.00 = 80.00
        // Total: 120.00
        $this->assertEquals(120.00, $totalDiscount);
    }

    public function test_recalculate_cart(): void
    {
        $product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'is_taxable' => true,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $this->vendor->id,
            'total_price' => 300.00,
            'discount_amount' => 20.00,
        ]);

        $this->cart->update([
            'applied_coupons' => [
                ['code' => 'TEST10', 'discount_amount' => 30.00],
            ],
        ]);

        $recalculatedCart = $this->calculationService->recalculateCart($this->cart);

        $this->assertEquals(300.00, $recalculatedCart->subtotal);
        $this->assertEquals(14.00, $recalculatedCart->tax_amount); // (300-20) * 0.05
        $this->assertEquals(50.00, $recalculatedCart->discount_amount); // 20 + 30
        $this->assertGreaterThan(0, $recalculatedCart->shipping_amount); // Below free shipping
        
        $expectedTotal = 300.00 + 14.00 + $recalculatedCart->shipping_amount - 50.00;
        $this->assertEquals($expectedTotal, $recalculatedCart->total_amount);
    }

    public function test_apply_percentage_coupon(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'PERCENT20',
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 100.00,
        ]);

        $this->cart->update(['subtotal' => 500.00]);

        $discountAmount = $this->calculationService->applyCoupon($this->cart, 'PERCENT20');

        $this->assertEquals(100.00, $discountAmount); // 20% of 500.00

        $appliedCoupons = $this->cart->fresh()->applied_coupons;
        $this->assertEquals(1, count($appliedCoupons));
        $this->assertEquals('PERCENT20', $appliedCoupons[0]['code']);
        $this->assertEquals(100.00, $appliedCoupons[0]['discount_amount']);
    }

    public function test_apply_fixed_coupon(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'FIXED50',
            'type' => 'fixed',
            'value' => 50,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 100.00,
        ]);

        $this->cart->update(['subtotal' => 500.00]);

        $discountAmount = $this->calculationService->applyCoupon($this->cart, 'FIXED50');

        $this->assertEquals(50.00, $discountAmount);

        $appliedCoupons = $this->cart->fresh()->applied_coupons;
        $this->assertEquals(1, count($appliedCoupons));
        $this->assertEquals('FIXED50', $appliedCoupons[0]['code']);
        $this->assertEquals(50.00, $appliedCoupons[0]['discount_amount']);
    }

    public function test_apply_coupon_below_minimum_order_value(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'MIN200',
            'type' => 'fixed',
            'value' => 50,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'min_order_value' => 200.00,
        ]);

        $this->cart->update(['subtotal' => 150.00]); // Below minimum

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Minimum order value of 200 AED required.');

        $this->calculationService->applyCoupon($this->cart, 'MIN200');
    }

    public function test_apply_already_applied_coupon(): void
    {
        $coupon = Coupon::factory()->create([
            'code' => 'DUPLICATE',
            'type' => 'fixed',
            'value' => 50,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
        ]);

        $this->cart->update([
            'subtotal' => 500.00,
            'applied_coupons' => [
                ['code' => 'DUPLICATE', 'discount_amount' => 50.00],
            ],
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Coupon is already applied.');

        $this->calculationService->applyCoupon($this->cart, 'DUPLICATE');
    }

    public function test_get_pricing_breakdown(): void
    {
        $product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'is_taxable' => true,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $this->vendor->id,
            'total_price' => 400.00,
            'discount_amount' => 20.00,
            'base_price' => 120.00,
            'unit_price' => 100.00,
            'quantity' => 4,
        ]);

        $this->cart->update([
            'subtotal' => 400.00,
            'tax_amount' => 19.00,
            'discount_amount' => 20.00,
            'shipping_amount' => 25.00,
            'total_amount' => 424.00,
        ]);

        $breakdown = $this->calculationService->getPricingBreakdown($this->cart);

        $this->assertEquals(400.00, $breakdown['subtotal']);
        $this->assertEquals(424.00, $breakdown['total_amount']);
        $this->assertArrayHasKey('tax_breakdown', $breakdown);
        $this->assertArrayHasKey('shipping_breakdown', $breakdown);
        $this->assertArrayHasKey('discount_breakdown', $breakdown);
        $this->assertArrayHasKey('savings_total', $breakdown);
        
        // Check savings calculation (base_price - unit_price) * quantity
        $expectedSavings = (120.00 - 100.00) * 4 + 20.00; // Price savings + discount
        $this->assertEquals($expectedSavings, $breakdown['savings_total']);
    }
}
