<?php

namespace Tests\Unit\Models;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateCategory;
use App\Models\EmailTemplateHistory;
use App\Models\EmailTemplateVariable;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Tests\TestCase;

class EmailTemplateTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
    }

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'uuid',
            'name',
            'slug',
            'subject',
            'body_html',
            'body_text',
            'category_id',
            'language',
            'is_active',
            'is_default',
            'variables',
            'metadata',
            'created_by',
            'updated_by',
        ];

        $template = new EmailTemplate();
        $this->assertEquals($fillable, $template->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $template = EmailTemplate::factory()->create([
            'is_active' => '1',
            'is_default' => '0',
            'variables' => ['user.name', 'site.name'],
            'metadata' => ['type' => 'test'],
        ]);

        $this->assertIsBool($template->is_active);
        $this->assertIsBool($template->is_default);
        $this->assertIsArray($template->variables);
        $this->assertIsArray($template->metadata);
        $this->assertTrue($template->is_active);
        $this->assertFalse($template->is_default);
    }

    /** @test */
    public function it_automatically_generates_uuid_on_creation()
    {
        $template = EmailTemplate::factory()->create(['uuid' => null]);
        
        $this->assertNotNull($template->uuid);
        $this->assertTrue(Str::isUuid($template->uuid));
    }

    /** @test */
    public function it_automatically_generates_slug_from_name()
    {
        $template = EmailTemplate::factory()->create([
            'name' => 'Test Template Name',
            'slug' => null,
        ]);
        
        $this->assertEquals('test-template-name', $template->slug);
    }

    /** @test */
    public function it_has_category_relationship()
    {
        $template = EmailTemplate::first();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $template->category());
        $this->assertInstanceOf(EmailTemplateCategory::class, $template->category);
    }

    /** @test */
    public function it_has_creator_relationship()
    {
        $user = User::factory()->create();
        $template = EmailTemplate::factory()->create(['created_by' => $user->id]);
        
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $template->creator());
        $this->assertEquals($user->id, $template->creator->id);
    }

    /** @test */
    public function it_has_updater_relationship()
    {
        $user = User::factory()->create();
        $template = EmailTemplate::factory()->create(['updated_by' => $user->id]);
        
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $template->updater());
        $this->assertEquals($user->id, $template->updater->id);
    }

    /** @test */
    public function it_has_histories_relationship()
    {
        $template = EmailTemplate::first();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $template->histories());
    }

    /** @test */
    public function it_has_active_scope()
    {
        EmailTemplate::factory()->create(['is_active' => false]);
        
        $totalTemplates = EmailTemplate::count();
        $activeTemplates = EmailTemplate::active()->count();
        
        $this->assertGreaterThan($activeTemplates, $totalTemplates);
    }

    /** @test */
    public function it_has_default_scope()
    {
        EmailTemplate::factory()->create(['is_default' => true]);
        
        $defaultTemplates = EmailTemplate::default()->count();
        $this->assertGreaterThan(0, $defaultTemplates);
    }

    /** @test */
    public function it_has_language_scope()
    {
        EmailTemplate::factory()->create(['language' => 'ar']);
        
        $englishTemplates = EmailTemplate::language('en')->count();
        $arabicTemplates = EmailTemplate::language('ar')->count();
        
        $this->assertGreaterThan(0, $englishTemplates);
        $this->assertGreaterThan(0, $arabicTemplates);
    }

    /** @test */
    public function it_has_category_scope()
    {
        $category = EmailTemplateCategory::first();
        $templatesInCategory = EmailTemplate::where('category_id', $category->id)->count();

        $this->assertGreaterThanOrEqual(0, $templatesInCategory);
    }

    /** @test */
    public function it_uses_uuid_as_route_key()
    {
        $template = new EmailTemplate();
        $this->assertEquals('uuid', $template->getRouteKeyName());
    }

    /** @test */
    public function it_can_create_history_record_manually()
    {
        $template = EmailTemplate::first();
        $originalHistoryCount = $template->histories()->count();

        // Manually create history record
        $template->createHistoryRecord('Test change');

        $newHistoryCount = $template->histories()->count();
        $this->assertEquals($originalHistoryCount + 1, $newHistoryCount);

        $latestHistory = $template->histories()->first();
        $this->assertEquals('Test change', $latestHistory->change_reason);
    }

    /** @test */
    public function it_gets_next_version_number()
    {
        $template = EmailTemplate::first();
        
        // Create some history records
        EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        EmailTemplateHistory::factory()->forTemplate($template)->version(2)->create();
        
        $nextVersion = $template->getNextVersionNumber();
        $this->assertEquals(3, $nextVersion);
    }

    /** @test */
    public function it_extracts_variables_from_content()
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {{user.name}}',
            'body_html' => '<p>Welcome {{user.name}} to {{site.name}}!</p>',
            'body_text' => 'Welcome {{user.name}} to {{site.name}}!',
        ]);
        
        $variables = $template->extractVariablesFromContent();
        
        $this->assertContains('user.name', $variables);
        $this->assertContains('site.name', $variables);
        $this->assertCount(2, $variables);
    }

    /** @test */
    public function it_validates_template_structure()
    {
        // Create a template with invalid HTML
        $template = EmailTemplate::factory()->create([
            'subject' => '',
            'body_html' => '<div><p>Unclosed div',
        ]);
        
        $errors = $template->validateTemplate();
        
        $this->assertIsArray($errors);
        $this->assertNotEmpty($errors);
    }

    /** @test */
    public function it_checks_required_variables()
    {
        // Create a required variable
        EmailTemplateVariable::factory()->create([
            'key' => 'required.var',
            'is_required' => true,
        ]);
        
        $template = EmailTemplate::factory()->create([
            'body_html' => 'Hello {{required.var}}',
        ]);
        
        // Test with missing required variable
        $hasRequired = $template->hasRequiredVariables([]);
        $this->assertFalse($hasRequired);
        
        // Test with provided required variable
        $hasRequired = $template->hasRequiredVariables(['required' => ['var' => 'value']]);
        $this->assertTrue($hasRequired);
    }

    /** @test */
    public function it_can_be_created_with_valid_data()
    {
        $category = EmailTemplateCategory::first();
        
        $data = [
            'name' => 'Test Template',
            'subject' => 'Test Subject',
            'body_html' => '<p>Test Body</p>',
            'category_id' => $category->id,
        ];

        $template = EmailTemplate::create($data);

        $this->assertInstanceOf(EmailTemplate::class, $template);
        $this->assertEquals('Test Template', $template->name);
        $this->assertEquals('test-template', $template->slug);
        $this->assertNotNull($template->uuid);
    }

    /** @test */
    public function scopes_filter_correctly()
    {
        // Create test templates
        EmailTemplate::factory()->active()->create();
        EmailTemplate::factory()->inactive()->create();
        EmailTemplate::factory()->default()->create();
        
        $activeCount = EmailTemplate::active()->count();
        $defaultCount = EmailTemplate::default()->count();
        
        $this->assertGreaterThan(0, $activeCount);
        $this->assertGreaterThan(0, $defaultCount);
        
        // Verify active scope only returns active templates
        $activeTemplates = EmailTemplate::active()->get();
        foreach ($activeTemplates as $template) {
            $this->assertTrue($template->is_active);
        }
        
        // Verify default scope only returns default templates
        $defaultTemplates = EmailTemplate::default()->get();
        foreach ($defaultTemplates as $template) {
            $this->assertTrue($template->is_default);
        }
    }
}
