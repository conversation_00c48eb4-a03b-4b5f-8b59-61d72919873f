<?php

namespace Tests\Unit\Models;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateHistory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateHistoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
    }

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'template_id',
            'version_number',
            'name',
            'subject',
            'body_html',
            'body_text',
            'variables',
            'metadata',
            'changed_by',
            'change_reason',
            'created_at',
        ];

        $history = new EmailTemplateHistory();
        $this->assertEquals($fillable, $history->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $history = EmailTemplateHistory::factory()->create([
            'variables' => ['user.name', 'site.name'],
            'metadata' => ['type' => 'test'],
        ]);

        $this->assertIsArray($history->variables);
        $this->assertIsArray($history->metadata);
        $this->assertEquals(['user.name', 'site.name'], $history->variables);
        $this->assertEquals(['type' => 'test'], $history->metadata);
    }

    /** @test */
    public function it_has_template_relationship()
    {
        $template = EmailTemplate::first();
        $history = EmailTemplateHistory::factory()->forTemplate($template)->create();
        
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $history->template());
        $this->assertEquals($template->id, $history->template->id);
    }

    /** @test */
    public function it_has_changed_by_relationship()
    {
        $user = User::factory()->create();
        $history = EmailTemplateHistory::factory()->changedBy($user)->create();
        
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class, $history->changedBy());
        $this->assertEquals($user->id, $history->changedBy->id);
    }

    /** @test */
    public function it_has_template_scope()
    {
        $template = EmailTemplate::first();
        EmailTemplateHistory::factory()->forTemplate($template)->create();
        
        $historyCount = EmailTemplateHistory::forTemplate($template->id)->count();
        $this->assertGreaterThan(0, $historyCount);
    }

    /** @test */
    public function it_has_version_scope()
    {
        $template = EmailTemplate::first();
        EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        EmailTemplateHistory::factory()->forTemplate($template)->version(2)->create();
        
        $version1Count = EmailTemplateHistory::version(1)->count();
        $version2Count = EmailTemplateHistory::version(2)->count();
        
        $this->assertGreaterThan(0, $version1Count);
        $this->assertGreaterThan(0, $version2Count);
    }

    /** @test */
    public function it_orders_by_version_descending_by_default()
    {
        $template = EmailTemplate::first();
        EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        EmailTemplateHistory::factory()->forTemplate($template)->version(3)->create();
        EmailTemplateHistory::factory()->forTemplate($template)->version(2)->create();
        
        $histories = EmailTemplateHistory::forTemplate($template->id)->get();
        
        $this->assertEquals(3, $histories->first()->version_number);
        $this->assertEquals(1, $histories->last()->version_number);
    }

    /** @test */
    public function it_gets_previous_version()
    {
        $template = EmailTemplate::first();
        $history1 = EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        $history2 = EmailTemplateHistory::factory()->forTemplate($template)->version(2)->create();
        
        $previousVersion = $history2->getPreviousVersion();
        
        $this->assertInstanceOf(EmailTemplateHistory::class, $previousVersion);
        $this->assertEquals(1, $previousVersion->version_number);
    }

    /** @test */
    public function it_gets_next_version()
    {
        $template = EmailTemplate::first();
        $history1 = EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        $history2 = EmailTemplateHistory::factory()->forTemplate($template)->version(2)->create();
        
        $nextVersion = $history1->getNextVersion();
        
        $this->assertInstanceOf(EmailTemplateHistory::class, $nextVersion);
        $this->assertEquals(2, $nextVersion->version_number);
    }

    /** @test */
    public function it_returns_null_for_non_existent_previous_version()
    {
        $template = EmailTemplate::first();
        $history = EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        
        $previousVersion = $history->getPreviousVersion();
        
        $this->assertNull($previousVersion);
    }

    /** @test */
    public function it_returns_null_for_non_existent_next_version()
    {
        $template = EmailTemplate::first();
        $history = EmailTemplateHistory::factory()->forTemplate($template)->version(1)->create();
        
        $nextVersion = $history->getNextVersion();
        
        $this->assertNull($nextVersion);
    }

    /** @test */
    public function it_compares_with_another_version()
    {
        $template = EmailTemplate::first();
        $history1 = EmailTemplateHistory::factory()->forTemplate($template)->create([
            'version_number' => 1,
            'name' => 'Original Name',
            'subject' => 'Original Subject',
        ]);
        
        $history2 = EmailTemplateHistory::factory()->forTemplate($template)->create([
            'version_number' => 2,
            'name' => 'Updated Name',
            'subject' => 'Original Subject',
        ]);
        
        $changes = $history2->compareWith($history1);
        
        $this->assertIsArray($changes);
        $this->assertArrayHasKey('name', $changes);
        $this->assertEquals('Original Name', $changes['name']['old']);
        $this->assertEquals('Updated Name', $changes['name']['new']);
        $this->assertArrayNotHasKey('subject', $changes); // No change
    }

    /** @test */
    public function it_can_be_created_with_valid_data()
    {
        $template = EmailTemplate::first();
        $user = User::factory()->create();
        
        $data = [
            'template_id' => $template->id,
            'version_number' => 1,
            'name' => 'Test History',
            'subject' => 'Test Subject',
            'body_html' => '<p>Test Body</p>',
            'body_text' => 'Test Body',
            'variables' => ['user.name'],
            'metadata' => ['type' => 'test'],
            'changed_by' => $user->id,
            'change_reason' => 'Test change',
        ];

        $history = EmailTemplateHistory::create($data);

        $this->assertInstanceOf(EmailTemplateHistory::class, $history);
        $this->assertEquals('Test History', $history->name);
        $this->assertEquals(1, $history->version_number);
        $this->assertEquals('Test change', $history->change_reason);
    }

    /** @test */
    public function scopes_filter_correctly()
    {
        $template1 = EmailTemplate::first();
        $template2 = EmailTemplate::skip(1)->first();
        
        // Create histories for different templates and versions
        EmailTemplateHistory::factory()->forTemplate($template1)->version(1)->create();
        EmailTemplateHistory::factory()->forTemplate($template1)->version(2)->create();
        EmailTemplateHistory::factory()->forTemplate($template2)->version(1)->create();
        
        // Test template scope
        $template1Histories = EmailTemplateHistory::forTemplate($template1->id)->get();
        foreach ($template1Histories as $history) {
            $this->assertEquals($template1->id, $history->template_id);
        }
        
        // Test version scope
        $version1Histories = EmailTemplateHistory::version(1)->get();
        foreach ($version1Histories as $history) {
            $this->assertEquals(1, $history->version_number);
        }
    }

    /** @test */
    public function it_generates_change_summary()
    {
        $template = EmailTemplate::first();
        $history1 = EmailTemplateHistory::factory()->create([
            'template_id' => $template->id,
            'version_number' => 1,
            'name' => 'Original Name',
            'subject' => 'Original Subject',
            'body_html' => '<p>Original</p>',
        ]);

        $history2 = EmailTemplateHistory::factory()->create([
            'template_id' => $template->id,
            'version_number' => 2,
            'name' => 'Updated Name',
            'subject' => 'Updated Subject',
            'body_html' => '<p>Updated</p>',
        ]);

        $summary = $history2->getChangeSummary();

        $this->assertIsString($summary);
        $this->assertStringContainsString('Name changed', $summary);
        $this->assertStringContainsString('Subject changed', $summary);
        $this->assertStringContainsString('Body html changed', $summary);
    }

    /** @test */
    public function it_handles_empty_change_comparison()
    {
        $template = EmailTemplate::first();
        $history1 = EmailTemplateHistory::factory()->create([
            'template_id' => $template->id,
            'version_number' => 1,
            'name' => 'Same Name',
            'subject' => 'Same Subject',
            'body_html' => '<p>Same content</p>',
            'body_text' => 'Same content',
            'variables' => ['user.name'],
            'metadata' => ['type' => 'test'],
        ]);

        $history2 = EmailTemplateHistory::factory()->create([
            'template_id' => $template->id,
            'version_number' => 2,
            'name' => 'Same Name',
            'subject' => 'Same Subject',
            'body_html' => '<p>Same content</p>',
            'body_text' => 'Same content',
            'variables' => ['user.name'],
            'metadata' => ['type' => 'test'],
        ]);

        $changes = $history2->compareWith($history1);

        $this->assertIsArray($changes);
        $this->assertEmpty($changes);
    }
}
