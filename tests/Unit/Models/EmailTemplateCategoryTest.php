<?php

namespace Tests\Unit\Models;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateCategoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\EmailTemplateSystemSeeder::class);
    }

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'name',
            'slug',
            'description',
            'icon',
            'sort_order',
            'is_active',
        ];

        $category = new EmailTemplateCategory();
        $this->assertEquals($fillable, $category->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $category = EmailTemplateCategory::factory()->create([
            'is_active' => '1',
            'sort_order' => '5',
        ]);

        $this->assertIsBool($category->is_active);
        $this->assertIsInt($category->sort_order);
        $this->assertTrue($category->is_active);
        $this->assertEquals(5, $category->sort_order);
    }

    /** @test */
    public function it_has_templates_relationship()
    {
        $category = EmailTemplateCategory::first();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $category->templates());
    }

    /** @test */
    public function it_has_active_templates_relationship()
    {
        $category = EmailTemplateCategory::first();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $category->activeTemplates());
    }

    /** @test */
    public function active_templates_only_returns_active_templates()
    {
        $category = EmailTemplateCategory::first();
        
        // Create an inactive template
        EmailTemplate::factory()->create([
            'category_id' => $category->id,
            'is_active' => false,
        ]);

        $activeCount = $category->templates()->where('is_active', true)->count();
        $activeTemplatesCount = $category->activeTemplates()->count();

        $this->assertEquals($activeCount, $activeTemplatesCount);
    }

    /** @test */
    public function it_has_active_scope()
    {
        EmailTemplateCategory::factory()->create(['is_active' => false]);
        
        $totalCategories = EmailTemplateCategory::count();
        $activeCategories = EmailTemplateCategory::active()->count();
        
        $this->assertGreaterThan($activeCategories, $totalCategories);
    }

    /** @test */
    public function it_has_ordered_scope()
    {
        // Create categories with different sort orders
        EmailTemplateCategory::factory()->create(['sort_order' => 10, 'name' => 'Z Category']);
        EmailTemplateCategory::factory()->create(['sort_order' => 1, 'name' => 'A Category']);
        
        $orderedCategories = EmailTemplateCategory::ordered()->get();
        
        // Should be ordered by sort_order first, then by name
        $this->assertEquals(1, $orderedCategories->first()->sort_order);
    }

    /** @test */
    public function it_uses_slug_as_route_key()
    {
        $category = new EmailTemplateCategory();
        $this->assertEquals('slug', $category->getRouteKeyName());
    }

    /** @test */
    public function it_can_be_created_with_valid_data()
    {
        $data = [
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'A test category',
            'icon' => 'fas fa-test',
            'sort_order' => 1,
            'is_active' => true,
        ];

        $category = EmailTemplateCategory::create($data);

        $this->assertInstanceOf(EmailTemplateCategory::class, $category);
        $this->assertEquals('Test Category', $category->name);
        $this->assertEquals('test-category', $category->slug);
        $this->assertTrue($category->is_active);
    }

    /** @test */
    public function it_can_count_templates()
    {
        $category = EmailTemplateCategory::first();
        $templateCount = $category->templates()->count();
        
        $this->assertIsInt($templateCount);
        $this->assertGreaterThanOrEqual(0, $templateCount);
    }

    /** @test */
    public function active_scope_filters_correctly()
    {
        // Create inactive category
        EmailTemplateCategory::factory()->create(['is_active' => false]);
        
        $allCategories = EmailTemplateCategory::all();
        $activeCategories = EmailTemplateCategory::active()->get();
        
        $this->assertGreaterThan($activeCategories->count(), $allCategories->count());
        
        foreach ($activeCategories as $category) {
            $this->assertTrue($category->is_active);
        }
    }

    /** @test */
    public function ordered_scope_sorts_correctly()
    {
        // Clear existing data and create test data
        EmailTemplateCategory::query()->delete();
        
        EmailTemplateCategory::factory()->create(['sort_order' => 3, 'name' => 'C']);
        EmailTemplateCategory::factory()->create(['sort_order' => 1, 'name' => 'A']);
        EmailTemplateCategory::factory()->create(['sort_order' => 2, 'name' => 'B']);
        EmailTemplateCategory::factory()->create(['sort_order' => 1, 'name' => 'AA']); // Same sort_order, different name
        
        $ordered = EmailTemplateCategory::ordered()->get();
        
        // Should be ordered by sort_order ASC, then name ASC
        $this->assertEquals(1, $ordered[0]->sort_order);
        $this->assertEquals('A', $ordered[0]->name);
        $this->assertEquals(1, $ordered[1]->sort_order);
        $this->assertEquals('AA', $ordered[1]->name);
        $this->assertEquals(2, $ordered[2]->sort_order);
        $this->assertEquals(3, $ordered[3]->sort_order);
    }
}
