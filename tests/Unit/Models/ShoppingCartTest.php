<?php

namespace Tests\Unit\Models;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShoppingCartTest extends TestCase
{
    use RefreshDatabase;

    protected ShoppingCart $cart;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'currency' => 'AED',
            'status' => 'active',
            'subtotal' => 500.00,
            'tax_amount' => 25.00,
            'discount_amount' => 50.00,
            'shipping_amount' => 30.00,
            'total_amount' => 505.00,
        ]);
    }

    public function test_cart_has_uuid_on_creation(): void
    {
        $newCart = ShoppingCart::factory()->create();

        $this->assertNotNull($newCart->uuid);
        $this->assertIsString($newCart->uuid);
    }

    public function test_cart_updates_last_activity_on_creation(): void
    {
        $cart = ShoppingCart::factory()->create();

        $this->assertNotNull($cart->last_activity_at);
        $this->assertEqualsWithDelta(
            now()->timestamp,
            $cart->last_activity_at->timestamp,
            5 // 5 seconds tolerance
        );
    }

    public function test_cart_updates_last_activity_on_update(): void
    {
        $originalActivity = $this->cart->last_activity_at;
        
        sleep(1); // Ensure time difference
        
        $this->cart->update(['notes' => 'Updated cart']);

        $this->assertNotEquals(
            $originalActivity->timestamp,
            $this->cart->fresh()->last_activity_at->timestamp
        );
    }

    public function test_cart_belongs_to_user(): void
    {
        $this->assertInstanceOf(User::class, $this->cart->user);
        $this->assertEquals($this->user->id, $this->cart->user->id);
    }

    public function test_cart_has_many_items(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        CartItem::factory()->count(3)->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
        ]);

        $this->assertCount(3, $this->cart->items);
        $this->assertInstanceOf(CartItem::class, $this->cart->items->first());
    }

    public function test_items_count_accessor(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        CartItem::factory()->count(5)->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
        ]);

        $this->assertEquals(5, $this->cart->items_count);
    }

    public function test_total_quantity_accessor(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'quantity' => 3,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'quantity' => 7,
        ]);

        $this->assertEquals(10, $this->cart->total_quantity);
    }

    public function test_is_expired_accessor_with_expired_cart(): void
    {
        $this->cart->update(['expires_at' => now()->subDay()]);

        $this->assertTrue($this->cart->is_expired);
    }

    public function test_is_expired_accessor_with_active_cart(): void
    {
        $this->cart->update(['expires_at' => now()->addDay()]);

        $this->assertFalse($this->cart->is_expired);
    }

    public function test_is_expired_accessor_with_null_expiry(): void
    {
        $this->cart->update(['expires_at' => null]);

        $this->assertFalse($this->cart->is_expired);
    }

    public function test_vendor_groups_accessor(): void
    {
        $vendor1 = Vendor::factory()->create(['vendor_display_name_en' => 'Vendor One']);
        $vendor2 = Vendor::factory()->create(['vendor_display_name_en' => 'Vendor Two']);
        
        $product1 = Product::factory()->create(['vendor_id' => $vendor1->id]);
        $product2 = Product::factory()->create(['vendor_id' => $vendor2->id]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product1->id,
            'vendor_id' => $vendor1->id,
            'quantity' => 2,
            'unit_price' => 100.00,
            'total_price' => 200.00,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product2->id,
            'vendor_id' => $vendor2->id,
            'quantity' => 1,
            'unit_price' => 150.00,
            'total_price' => 150.00,
        ]);

        $vendorGroups = $this->cart->vendor_groups;

        $this->assertCount(2, $vendorGroups);
        
        $group1 = collect($vendorGroups)->firstWhere('vendor_id', $vendor1->id);
        $this->assertEquals(200.00, $group1['subtotal']);
        $this->assertEquals(1, $group1['items_count']);
        $this->assertEquals(2, $group1['total_quantity']);
    }

    public function test_active_scope(): void
    {
        ShoppingCart::factory()->create(['status' => 'active']);
        ShoppingCart::factory()->create(['status' => 'abandoned']);
        ShoppingCart::factory()->create(['status' => 'converted']);

        $activeCarts = ShoppingCart::active()->get();

        $this->assertCount(2, $activeCarts); // Including the one from setUp
        $activeCarts->each(function ($cart) {
            $this->assertEquals('active', $cart->status);
        });
    }

    public function test_for_user_scope(): void
    {
        $otherUser = User::factory()->create();
        ShoppingCart::factory()->create(['user_id' => $otherUser->id]);

        $userCarts = ShoppingCart::forUser($this->user->id)->get();

        $this->assertCount(1, $userCarts);
        $this->assertEquals($this->user->id, $userCarts->first()->user_id);
    }

    public function test_for_session_scope(): void
    {
        $sessionId = 'test-session-123';
        ShoppingCart::factory()->create(['session_id' => $sessionId, 'user_id' => null]);
        ShoppingCart::factory()->create(['session_id' => 'other-session', 'user_id' => null]);

        $sessionCarts = ShoppingCart::forSession($sessionId)->get();

        $this->assertCount(1, $sessionCarts);
        $this->assertEquals($sessionId, $sessionCarts->first()->session_id);
    }

    public function test_expired_scope(): void
    {
        ShoppingCart::factory()->create(['expires_at' => now()->subDay()]);
        ShoppingCart::factory()->create(['expires_at' => now()->addDay()]);
        ShoppingCart::factory()->create(['expires_at' => null]);

        $expiredCarts = ShoppingCart::expired()->get();

        $this->assertCount(1, $expiredCarts);
        $this->assertTrue($expiredCarts->first()->expires_at->isPast());
    }

    public function test_not_expired_scope(): void
    {
        ShoppingCart::factory()->create(['expires_at' => now()->subDay()]);
        ShoppingCart::factory()->create(['expires_at' => now()->addDay()]);
        ShoppingCart::factory()->create(['expires_at' => null]);

        $notExpiredCarts = ShoppingCart::notExpired()->get();

        $this->assertCount(3, $notExpiredCarts); // Including the one from setUp
    }

    public function test_is_empty_method(): void
    {
        $emptyCart = ShoppingCart::factory()->create();
        $this->assertTrue($emptyCart->isEmpty());

        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);
        
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
        ]);

        $this->assertFalse($this->cart->isEmpty());
    }

    public function test_has_expired_method(): void
    {
        $this->cart->update(['expires_at' => now()->subDay()]);
        $this->assertTrue($this->cart->hasExpired());

        $this->cart->update(['expires_at' => now()->addDay()]);
        $this->assertFalse($this->cart->hasExpired());

        $this->cart->update(['expires_at' => null]);
        $this->assertFalse($this->cart->hasExpired());
    }

    public function test_mark_as_abandoned(): void
    {
        $this->cart->markAsAbandoned();

        $this->assertEquals('abandoned', $this->cart->fresh()->status);
    }

    public function test_mark_as_converted(): void
    {
        $this->cart->markAsConverted();

        $this->assertEquals('converted', $this->cart->fresh()->status);
    }

    public function test_calculate_totals_method(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
        ]);

        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'quantity' => 1,
            'unit_price' => 150.00,
            'total_price' => 150.00,
        ]);

        $this->cart->calculateTotals();

        $this->assertEquals(250.00, $this->cart->fresh()->subtotal);
        $this->assertGreaterThanOrEqual(0, $this->cart->fresh()->total_amount);
    }

    public function test_casts_are_applied(): void
    {
        $this->assertIsString($this->cart->subtotal);
        $this->assertIsString($this->cart->tax_amount);
        $this->assertIsString($this->cart->discount_amount);
        $this->assertIsString($this->cart->shipping_amount);
        $this->assertIsString($this->cart->total_amount);
        
        if ($this->cart->expires_at) {
            $this->assertInstanceOf(\Carbon\Carbon::class, $this->cart->expires_at);
        }
        
        if ($this->cart->last_activity_at) {
            $this->assertInstanceOf(\Carbon\Carbon::class, $this->cart->last_activity_at);
        }
        
        if ($this->cart->applied_coupons) {
            $this->assertIsArray($this->cart->applied_coupons);
        }
        
        if ($this->cart->applied_discounts) {
            $this->assertIsArray($this->cart->applied_discounts);
        }
        
        if ($this->cart->metadata) {
            $this->assertIsArray($this->cart->metadata);
        }
    }
}
