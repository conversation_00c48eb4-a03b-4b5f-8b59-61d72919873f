<?php

namespace Tests\Unit;

use App\Models\SupportTicket;
use App\Models\SupportReason;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SupportTicketCodeGenerationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_generates_correct_code_format()
    {
        // Create a mock ticket object
        $ticket = new SupportTicket([
            'user_id' => 1,
            'subject' => 'Test Ticket',
            'message' => 'Test message'
        ]);

        $code = SupportTicket::generateCode($ticket);

        // Should be 14 characters: XX(2) + YYMMDD(6) + XXXXXX(6)
        $this->assertEquals(14, strlen($code));
        
        // Should start with CA (default prefix)
        $this->assertStringStartsWith('CA', $code);
        
        // Should contain today's date in YYMMDD format
        $expectedDate = now()->format('ymd');
        $this->assertStringContainsString($expectedDate, $code);
        
        // Should end with 6-digit sequence
        $sequence = substr($code, -6);
        $this->assertMatchesRegularExpression('/^\d{6}$/', $sequence);
    }

    /** @test */
    public function it_generates_different_prefixes_based_on_reason()
    {
        // Create support reasons
        $vendorReason = SupportReason::create([
            'label' => 'Product Issue',
            'route_to' => 'vendor',
            'code_prefix' => 'CV',
            'status' => 'active'
        ]);

        $tplReason = SupportReason::create([
            'label' => 'Delivery Issue',
            'route_to' => 'tpl',
            'code_prefix' => 'CT',
            'status' => 'active'
        ]);

        // Test vendor ticket
        $vendorTicket = new SupportTicket([
            'user_id' => 1,
            'reason_id' => $vendorReason->id,
            'subject' => 'Vendor Issue'
        ]);
        $vendorCode = SupportTicket::generateCode($vendorTicket);
        $this->assertStringStartsWith('CV', $vendorCode);

        // Test TPL ticket
        $tplTicket = new SupportTicket([
            'user_id' => 1,
            'reason_id' => $tplReason->id,
            'subject' => 'TPL Issue'
        ]);
        $tplCode = SupportTicket::generateCode($tplTicket);
        $this->assertStringStartsWith('CT', $tplCode);
    }

    /** @test */
    public function it_generates_sequential_codes_for_same_day()
    {
        // Create a user first
        $user = User::factory()->create();

        $codes = [];

        // Create 5 actual tickets to test sequence
        for ($i = 0; $i < 5; $i++) {
            $ticket = SupportTicket::create([
                'user_id' => $user->id,
                'subject' => "Test Ticket {$i}",
                'message' => 'Test message',
                'priority' => 'medium',
                'status' => 'open'
            ]);
            $codes[] = $ticket->code;
        }

        // All codes should be unique
        $this->assertEquals(count($codes), count(array_unique($codes)));

        // Extract sequences and verify they're sequential
        $sequences = array_map(function($code) {
            return (int)substr($code, -6);
        }, $codes);

        // Should be sequential starting from 1
        $this->assertEquals([1, 2, 3, 4, 5], $sequences);
    }

    /** @test */
    public function support_reason_auto_generates_prefix()
    {
        $reason = new SupportReason([
            'label' => 'Test Reason',
            'route_to' => 'vendor',
            'status' => 'active'
        ]);

        // Should auto-generate CV prefix for vendor routing
        $this->assertEquals('CV', $reason->code_prefix);

        $reason->route_to = 'tpl';
        $this->assertEquals('CT', $reason->code_prefix);

        $reason->route_to = 'admin';
        $this->assertEquals('CA', $reason->code_prefix);
    }

    /** @test */
    public function it_uses_custom_prefix_when_provided()
    {
        $reason = SupportReason::create([
            'label' => 'Custom Reason',
            'route_to' => 'vendor',
            'code_prefix' => 'XY',
            'status' => 'active'
        ]);

        $ticket = new SupportTicket([
            'user_id' => 1,
            'reason_id' => $reason->id,
            'subject' => 'Custom Prefix Test'
        ]);

        $code = SupportTicket::generateCode($ticket);
        $this->assertStringStartsWith('XY', $code);
    }
}
