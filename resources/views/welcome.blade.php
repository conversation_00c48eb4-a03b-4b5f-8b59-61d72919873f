<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vitamins.ae - Coming Soon</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            min-height: 100vh;
        }
        .animate-fade-in {
            animation: fade-in 1.2s ease-out forwards;
        }
        .animate-fade-up {
            animation: fade-up 0.8s ease-out forwards;
            opacity: 0;
        }
        @keyframes fade-in {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }
        @keyframes fade-up {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="flex flex-col min-h-screen py-12 px-4">
<div class="container mx-auto flex-grow flex flex-col">
    <!-- Header -->
    <header class="flex flex-col items-center mb-8 sm:mb-12">
        <div class="flex items-center text-emerald-700">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>
            <h1 class="text-3xl sm:text-4xl font-bold">Vitamins.ae</h1>
        </div>
        <p class="text-sm sm:text-base text-emerald-800 mt-2">
            A Health and Beauty Marketplace
        </p>
    </header>

    <!-- Coming Soon -->
    <div class="text-center mb-16 sm:mb-24">
        <h2 class="text-5xl sm:text-7xl md:text-8xl font-bold text-emerald-800 tracking-wide animate-fade-in">
            Coming Soon
        </h2>
    </div>

    <!-- Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto px-4">
        <!-- Card 1 -->
        <div class="flex justify-center animate-fade-up" style="animation-delay: 0ms">
            <div class="bg-white rounded-lg p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-shadow duration-300 max-w-sm w-full">
                <h3 class="font-semibold text-center text-emerald-800 mb-2">
                    Suppliers & Vendors
                </h3>
                <p class="text-center text-emerald-700 text-sm mb-4">
                    Listing of your health & Beauty Products
                </p>
                <a href="#suppliers" class="mt-auto w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded text-center transition-colors duration-300">
                    Sell on Vitamins.ae
                </a>
            </div>
        </div>

        <!-- Card 2 -->
        <div class="flex justify-center animate-fade-up" style="animation-delay: 150ms">
            <div class="bg-white rounded-lg p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-shadow duration-300 max-w-sm w-full">
                <h3 class="font-semibold text-center text-emerald-800 mb-2">
                    Customers connect for prelaunch
                </h3>
                <p class="text-center text-emerald-700 text-sm mb-4">
                    deals & offers
                </p>
                <a href="#customers" class="mt-auto w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded text-center transition-colors duration-300">
                    Click For Deals and Offers
                </a>
            </div>
        </div>

        <!-- Card 3 -->
        <div class="flex justify-center animate-fade-up" style="animation-delay: 300ms">
            <div class="bg-white rounded-lg p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-shadow duration-300 max-w-sm w-full">
                <h3 class="font-semibold text-center text-emerald-800 mb-2">
                    Trade Partners
                </h3>
                <p class="text-center text-emerald-700 text-sm mb-4">
                    Delivery,logistic fulfillment service providers
                </p>
                <a href="#partners" class="mt-auto w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded text-center transition-colors duration-300">
                    Click For Deals and Offers
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-16 sm:mt-24 pb-8">
        <div class="flex justify-center space-x-6">
            <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" class="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors duration-300" aria-label="Facebook">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
            </a>
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" class="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors duration-300" aria-label="Instagram">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-pink-600"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
            </a>
            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors duration-300" aria-label="Twitter">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-black"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
            </a>
        </div>
        <div class="mt-8 text-center">
            <form class="mx-auto max-w-md px-4">
                <div class="flex">
                    <input type="email" placeholder="Enter your email for updates" class="flex-grow px-4 py-2 rounded-l-lg border-t border-b border-l border-emerald-300 focus:outline-none focus:ring-2 focus:ring-emerald-500">
                    <button type="submit" class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-r-lg transition-colors duration-300">
                        Subscribe
                    </button>
                </div>
            </form>
        </div>
    </footer>
</div>
</body>
</html>
