<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Development Database Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .warning {
            background: #e74c3c;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }

        .content {
            padding: 30px;
        }

        .auth-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        input,
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .response-section,
        .output-section {
            margin-top: 30px;
        }

        .response {
            background: #f8f9fa;
            color: #2c3e50;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
            font-size: 14px;
            line-height: 1.5;
        }

        .output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #3498db;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .checkbox-group {
            margin: 15px 0;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s;
        }

        .modal-header {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }

        .modal-body {
            padding: 30px;
            text-align: center;
        }

        .modal-footer {
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .modal-footer .btn {
            margin: 0 10px;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ Development Database Manager</h1>
            <p>Secure interface for database operations</p>
        </div>

        <div class="warning">
            ⚠️ DEVELOPMENT ONLY - This interface is disabled in production
        </div>

        <div class="content">
            <div class="auth-section">
                <h3>🔐 Authentication Required</h3>
                <p>Enter your development credentials to access database commands.</p>

                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" placeholder="Enter username">
                </div>

                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" placeholder="Enter password">
                </div>
            </div>

            <div class="alert alert-success" id="success-alert"></div>
            <div class="alert alert-error" id="error-alert"></div>

            <div class="quick-actions">
                <button class="btn btn-danger" onclick="executeQuickRefresh()">
                    🔄 Quick Refresh (migrate:fresh --seed)
                </button>
            </div>

            <div class="form-group">
                <label for="command">Select Command:</label>
                <select id="command">
                
                    <optgroup label="🧹 Cache Commands">
                        <option value="cache:clear">cache:clear</option>
                        <option value="config:clear">config:clear</option>
                        <option value="route:clear">route:clear</option>
                        <option value="view:clear">view:clear</option>
                        <option value="optimize:clear">optimize:clear</option>
                    </optgroup>
                    <optgroup label="🗃️ Database Commands">
                        <option value="migrate:fresh --seed">migrate:fresh --seed (Reset & Seed)</option>
                        <option value="migrate:fresh">migrate:fresh (Reset Only)</option>
                        <option value="migrate">migrate (Run Migrations)</option>
                        <option value="migrate:rollback">migrate:rollback</option>
                        <option value="migrate:reset">migrate:reset</option>
                        <option value="db:seed">db:seed</option>
                    </optgroup>
                    <optgroup label="🔗 System Commands">
                        <option value="storage:link">storage:link</option>
                    </optgroup>
                    {{-- <optgroup label="📦 Composer Commands">
                        <option value="composer:install">composer install</option>
                        <option value="composer:update">composer update</option>
                        <option value="composer:dump-autoload">composer dump-autoload</option>
                        <option value="composer:require">composer require</option>
                        <option value="composer:remove">composer remove</option>
                    </optgroup> --}}
                </select>
            </div>

            <div class="alert alert-warning"
                style="display: block; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; margin-bottom: 20px;">
                ⚠️ <strong>Note:</strong> Some commands (migrate:fresh, migrate:reset) will destroy existing data. Use
                with caution in development.
            </div>

            <button class="btn" onclick="executeCommand()">Execute Command</button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Executing command...</p>
            </div>

            <div class="response-section" id="response-section" style="display: none;">
                <h3>Response:</h3>
                <div class="response" id="response"></div>
            </div>

            <div class="output-section" id="output-section" style="display: none;">
                <h3>Command Output:</h3>
                <div class="output" id="output"></div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚠️ Destructive Command Warning</h2>
            </div>
            <div class="modal-body">
                <p><strong>You are about to execute a destructive command:</strong></p>
                <p id="commandToExecute"
                    style="font-family: 'Courier New', monospace; background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 15px 0;">
                </p>
                <p>This will <strong>permanently delete</strong> all existing data in your database and cannot be
                    undone.</p>
                <p>Are you sure you want to continue?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" onclick="confirmDestructiveCommand()">
                    🗑️ Yes, Delete Data
                </button>
                <button class="btn" onclick="closeModal()">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <script>
        // Set CSRF token for all AJAX requests
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Set up CSRF token for all requests
        window.Laravel = {
            csrfToken: token
        };

        function showAlert(message, type) {
            const alertId = type === 'success' ? 'success-alert' : 'error-alert';
            const alert = document.getElementById(alertId);
            alert.textContent = message;
            alert.style.display = 'block';

            // Hide other alert
            const otherAlertId = type === 'success' ? 'error-alert' : 'success-alert';
            document.getElementById(otherAlertId).style.display = 'none';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showResponse(message, success) {
            const responseElement = document.getElementById('response');
            const responseSection = document.getElementById('response-section');

            responseElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 18px; margin-right: 8px;">${success ? '✅' : '❌'}</span>
                    <strong>${success ? 'Success' : 'Error'}</strong>
                </div>
                <div>${message}</div>
            `;
            responseSection.style.display = 'block';
        }

        function showOutput(output) {
            if (output && output.trim()) {
                const outputElement = document.getElementById('output');
                const outputSection = document.getElementById('output-section');
                outputElement.textContent = output;
                outputSection.style.display = 'block';
            }
        }

        function hideResponseSections() {
            document.getElementById('response-section').style.display = 'none';
            document.getElementById('output-section').style.display = 'none';
        }

        function getCredentials() {
            return {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
        }

        // Destructive commands that require confirmation
        const destructiveCommands = [
            'migrate:fresh --seed',
            'migrate:fresh',
            'migrate:reset'
        ];

        function isDestructiveCommand(command) {
            return destructiveCommands.includes(command);
        }

        function showModal(command) {
            document.getElementById('commandToExecute').textContent = command;
            document.getElementById('confirmModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }

        // Store the pending command and action type
        let pendingCommand = null;
        let pendingActionType = null;

        function confirmDestructiveCommand() {
            closeModal();

            if (pendingActionType === 'quick-refresh') {
                executeQuickRefreshConfirmed();
            } else if (pendingActionType === 'command') {
                executeCommandConfirmed();
            }
        }

        // Close modal when clicking outside of it
        window.onclick = function (event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        function executeCommand() {
            const credentials = getCredentials();
            const command = document.getElementById('command').value;

            if (!credentials.username || !credentials.password) {
                showAlert('Please enter both username and password', 'error');
                return;
            }

            // Check if command is destructive and requires confirmation
            if (isDestructiveCommand(command)) {
                pendingCommand = command;
                pendingActionType = 'command';
                showModal(command);
                return;
            }

            // Execute non-destructive command directly
            executeCommandConfirmed();
        }

        function executeCommandConfirmed() {
            const credentials = getCredentials();
            const command = pendingCommand || document.getElementById('command').value;

            hideResponseSections();
            showLoading(true);

            // Create form data for API request (no CSRF token needed for API routes)
            const formData = new FormData();
            formData.append('username', credentials.username);
            formData.append('password', credentials.password);
            formData.append('command', command);

            fetch('/api/dev/execute-command', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    // Check if response is ok before trying to parse JSON
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text(); // Get as text first to debug
                })
                .then(text => {
                    showLoading(false);

                    // Try to parse JSON
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${text.substring(0, 100)}...`);
                    }

                    // Show response section
                    showResponse(data.message, data.success);

                    // Show command output if available
                    if (data.output) {
                        showOutput(data.output);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    showResponse('Network error: ' + error.message, false);
                })
                .finally(() => {
                    // Clear pending command
                    pendingCommand = null;
                    pendingActionType = null;
                });
        }

        function executeQuickRefresh() {
            const credentials = getCredentials();

            if (!credentials.username || !credentials.password) {
                showAlert('Please enter both username and password', 'error');
                return;
            }

            // Quick refresh is always destructive, show confirmation
            pendingCommand = 'migrate:fresh --seed';
            pendingActionType = 'quick-refresh';
            showModal('migrate:fresh --seed');
        }

        function executeQuickRefreshConfirmed() {
            const credentials = getCredentials();

            hideResponseSections();
            showLoading(true);

            // Create form data for API request (no CSRF token needed for API routes)
            const formData = new FormData();
            formData.append('username', credentials.username);
            formData.append('password', credentials.password);

            fetch('/api/dev/quick-refresh', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    // Check if response is ok before trying to parse JSON
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text(); // Get as text first to debug
                })
                .then(text => {
                    showLoading(false);

                    // Try to parse JSON
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Invalid JSON response: ${text.substring(0, 100)}...`);
                    }

                    // Show response section
                    showResponse(data.message, data.success);

                    // Show command output if available
                    if (data.output) {
                        showOutput(data.output);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    showResponse('Network error: ' + error.message, false);
                })
                .finally(() => {
                    // Clear pending command
                    pendingCommand = null;
                    pendingActionType = null;
                });
        }

        // Allow Enter key to execute command
        document.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                executeCommand();
            }
        });
    </script>
</body>

</html>