<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\VendorEoi;

class VendorEOISubmission extends Mailable
{
    use Queueable, SerializesModels;

    public $vendor;

    /**
     * Create a new message instance.
     */
    public function __construct(VendorEoi $vendor)
    {
        $this->vendor = $vendor;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('New Vendor EOI Submission')
            ->view('emails.vendor_eoi_submitted')
            ->with([
                'vendor' => $this->vendor
            ]);
    }
}
