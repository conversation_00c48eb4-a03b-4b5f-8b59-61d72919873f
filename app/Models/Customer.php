<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'gender',
        'date_of_birth',
        'loyalty_points',
        'customer_type',
        'preferred_language',
        'preferred_currency',
        'kyc_document_type',
        'kyc_file',
        'kyc_verified',
        'referral_code',
        'referred_by',
        'loyalty_points_awarded',
        'occupation',
        'designation',
        'company_name',
        'newsletter_consent',
        'is_vrps',
        'pricing_tier_id',
        'total_annual_spend',
        'total_orders_count',
        'tier_assigned_at',
        'last_tier_evaluation_at',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'loyalty_points' => 'integer',
        'kyc_verified' => 'boolean',
        'newsletter_consent' => 'boolean',
        'is_vrps' => 'boolean',
        'total_annual_spend' => 'decimal:2',
        'total_orders_count' => 'integer',
        'tier_assigned_at' => 'datetime',
        'last_tier_evaluation_at' => 'datetime',
    ];

    protected $appends = [
        'tier_name',
        'tier_discount_percentage',
        'is_vip_customer',
        'next_tier_requirements',
        'annual_spend_progress',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function pricingTier(): BelongsTo
    {
        return $this->belongsTo(CustomerPricingTier::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id', 'user_id');
    }

    // Accessors
    public function getTierNameAttribute(): ?string
    {
        return $this->pricingTier?->name;
    }

    public function getTierDiscountPercentageAttribute(): float
    {
        return $this->pricingTier?->discount_percentage ?? 0;
    }

    public function getIsVipCustomerAttribute(): bool
    {
        return $this->pricingTier && $this->pricingTier->code === 'VIP';
    }

    public function getNextTierRequirementsAttribute(): ?array
    {
        $nextTier = CustomerPricingTier::where('is_active', true)
            ->where('priority', '>', $this->pricingTier?->priority ?? 0)
            ->orderBy('priority', 'asc')
            ->first();

        if (!$nextTier) {
            return null;
        }

        return [
            'tier_name' => $nextTier->name,
            'required_annual_spend' => $nextTier->minimum_annual_spend,
            'required_orders_count' => $nextTier->minimum_orders_count,
            'current_annual_spend' => $this->total_annual_spend,
            'current_orders_count' => $this->total_orders_count,
            'spend_remaining' => max(0, ($nextTier->minimum_annual_spend ?? 0) - $this->total_annual_spend),
            'orders_remaining' => max(0, ($nextTier->minimum_orders_count ?? 0) - $this->total_orders_count),
        ];
    }

    public function getAnnualSpendProgressAttribute(): array
    {
        $currentTier = $this->pricingTier;
        $nextTier = CustomerPricingTier::where('is_active', true)
            ->where('priority', '>', $currentTier?->priority ?? 0)
            ->orderBy('priority', 'asc')
            ->first();

        if (!$nextTier || !$nextTier->minimum_annual_spend) {
            return [
                'current' => $this->total_annual_spend,
                'target' => null,
                'percentage' => 100,
                'is_qualified' => true,
            ];
        }

        $percentage = ($this->total_annual_spend / $nextTier->minimum_annual_spend) * 100;

        return [
            'current' => $this->total_annual_spend,
            'target' => $nextTier->minimum_annual_spend,
            'percentage' => min(100, $percentage),
            'is_qualified' => $this->total_annual_spend >= $nextTier->minimum_annual_spend,
        ];
    }

    // Helper methods
    public function updateSpendingStats(): void
    {
        $currentYear = now()->year;

        $annualSpend = $this->orders()
            ->whereYear('created_at', $currentYear)
            ->where('payment_status', 'paid')
            ->sum('total');

        $totalOrders = $this->orders()
            ->where('payment_status', 'paid')
            ->count();

        $this->update([
            'total_annual_spend' => $annualSpend,
            'total_orders_count' => $totalOrders,
            'last_tier_evaluation_at' => now(),
        ]);
    }

    public function evaluateTierEligibility(): ?CustomerPricingTier
    {
        $this->updateSpendingStats();

        return CustomerPricingTier::evaluateCustomerTier($this);
    }

    public function calculateTierDiscount(float $amount): float
    {
        if (!$this->pricingTier) {
            return 0;
        }

        return $this->pricingTier->calculateDiscount($amount);
    }

    public function hasActiveTier(): bool
    {
        return $this->pricingTier && $this->pricingTier->is_active;
    }

    // Scopes
    public function scopeWithTier($query, string $tierCode)
    {
        return $query->whereHas('pricingTier', function ($q) use ($tierCode) {
            $q->where('code', $tierCode);
        });
    }

    public function scopeVipCustomers($query)
    {
        return $query->withTier('VIP');
    }

    public function scopeWholesaleCustomers($query)
    {
        return $query->withTier('WHOLESALE');
    }

    public function scopeWithMinimumSpend($query, float $amount)
    {
        return $query->where('total_annual_spend', '>=', $amount);
    }

    public function scopeWithMinimumOrders($query, int $count)
    {
        return $query->where('total_orders_count', '>=', $count);
    }
}
