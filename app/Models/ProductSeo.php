<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductSeo extends Model
{
    protected $table = 'product_seo';

    protected $fillable = [
        'product_id',
        'meta_title_en',
        'meta_description_en',
        'keywords_en',
        'meta_title_ar',
        'meta_description_ar',
        'keywords_ar'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
