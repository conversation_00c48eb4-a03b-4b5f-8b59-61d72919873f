<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportTicketMessage extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'ticket_id', 'sender_id', 'sender_type', 'message',
        'is_read', 'status'
    ];

    public function ticket()
    {
        return $this->belongsTo(SupportTicket::class);
    }

    public function sender()
    {
        return $this->morphTo();
    }

    public function attachments()
    {
        return $this->hasMany(SupportTicketAttachment::class, 'message_id');
    }
}
