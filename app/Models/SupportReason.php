<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportReason extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'label',
        'route_to',
        'code_prefix',
        'status'
    ];

    protected $casts = [
        'status' => 'string',
        'route_to' => 'string'
    ];

    // Relationships
    public function tickets()
    {
        return $this->hasMany(SupportTicket::class, 'reason_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Helper methods
    public function getCodePrefixAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // Auto-generate prefix based on route_to if not set
        return match($this->route_to) {
            'vendor' => 'CV',
            'tpl' => 'CT',
            'admin' => 'CA',
            default => 'CA'
        };
    }
}
