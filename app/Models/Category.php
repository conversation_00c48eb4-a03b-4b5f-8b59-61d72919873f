<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Category extends Model
{
    use HasFactory;
    protected $fillable = [
        'name_en',
        'name_ar',
        'type',
        'code',
        'fee_text',
        'parent_id',
        'ordering_number',
        'banner_id',
        'icon',
        'meta_title',
        'meta_description',
        'slug',
        'status',
    ];

    protected $casts = [
        'filtering_attributes' => 'array',
    ];

    protected $appends = [
        'icon_url',
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function banner(): BelongsTo
    {
        return $this->belongsTo(Banner::class);
    }

    public function getIconAttribute($value)
    {
        return $value;
    }

    public function getIconUrlAttribute()
    {
        if ($this->icon) {
            return config('filesystems.disks.s3.url') . '/' . $this->icon;
        }
        return null;
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function subCategory()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id');
    }

    public function classes()
    {
        return $this->hasMany(ProductClass::class, 'sub_category_id', 'id');
    }
}
