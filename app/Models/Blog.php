<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Blog extends Model
{
    protected $fillable = [
        'blog_category_id',
        'user_id',
        'title_en',
        'title_ar',
        'slug',
        'summary_en',
        'summary_ar',
        'content_en',
        'content_ar',
        'meta_title',
        'meta_description',
        'keywords',
        'featured_image',
        'status',
        'published_at',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];

    protected $appends = [
        'featured_image_url',
    ];

    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return config('filesystems.disks.s3.url') . '/' . $this->featured_image;
        }
        return null;
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(BlogCategory::class, 'blog_category_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
