<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class SupportTicket extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'vendor_id',
        'tpl_id',
        'order_id',
        'reason_id',
        'category_id',
        'topic_id',
        'subject',
        'message',
        'priority',
        'status',
        'record_status',
        'assigned_to',
        'resolved_at',
        'code',
    ];

    protected static function booted()
    {
        static::creating(function ($ticket) {
            $ticket->code = self::generateCode($ticket);
        });
    }


    public static function generateCode($ticket)
    {
        // Determine prefix based on sender/receiver relationship
        $prefix = self::determineCodePrefix($ticket);

        // Format: YYMMDD
        $date = now()->format('ymd');

        // Get daily sequence number (6 digits)
        $sequence = self::getDailySequence($date);

        return $prefix . $date . $sequence;
    }

    private static function determineCodePrefix($ticket)
    {
        // If reason has specific prefix, use it
        if ($ticket->reason_id) {
            $reason = SupportReason::find($ticket->reason_id);
            if ($reason && $reason->code_prefix) {
                return $reason->code_prefix;
            }
        }

        // Determine prefix based on sender/receiver relationship
        $user = auth()->user();

        // If ticket has vendor_id, it's directed to vendor
        if ($ticket->vendor_id) {
            return 'CV'; // Customer → Vendor
        }

        // If ticket has tpl_id, it's directed to TPL
        if ($ticket->tpl_id) {
            return 'CT'; // Customer → TPL
        }

        // Check if sender is vendor
        if ($user && $user->hasRole('vendor')) {
            return 'VA'; // Vendor → Admin
        }

        // Check if sender is TPL
        if ($user && $user->hasRole('tpl')) {
            return 'TA'; // TPL → Admin
        }

        // Default: Customer → Admin
        return 'CA';
    }

    private static function getDailySequence($date)
    {
        // Get count of tickets created today
        $todayCount = self::withTrashed()
            ->whereDate('created_at', now()->toDateString())
            ->count();

        // For testing purposes, if no tickets exist, start from 1
        return str_pad($todayCount + 1, 6, '0', STR_PAD_LEFT);
    }

    // Relationships

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function tpl()
    {
        return $this->belongsTo(Tpl::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function reason()
    {
        return $this->belongsTo(SupportReason::class);
    }

    public function category()
    {
        return $this->belongsTo(SupportCategory::class);
    }

    public function topic()
    {
        return $this->belongsTo(SupportTopic::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function messages()
    {
        return $this->hasMany(SupportTicketMessage::class);
    }
}
