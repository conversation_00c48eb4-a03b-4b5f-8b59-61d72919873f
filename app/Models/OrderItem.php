<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_variant_id',
        'vendor_id',
        'product_title',
        'sku',
        'barcode',
        'quantity',
        'price',
        'total',
        'discount',
        'tax',
        'shipping_fee',
        'base_price',
        'promotional_price',
        'member_price',
        'product_snapshot',
        'applied_discounts',
        'customizations',
        'special_instructions',
        'metadata',
        'fulfillment_status',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'discount' => 'decimal:2',
        'tax' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'base_price' => 'decimal:2',
        'promotional_price' => 'decimal:2',
        'member_price' => 'decimal:2',
        'product_snapshot' => 'array',
        'applied_discounts' => 'array',
        'customizations' => 'array',
        'metadata' => 'array',
    ];

    protected $appends = [
        'final_unit_price',
        'savings_amount',
        'product_name',
        'product_image',
        'status_display',
        'total_with_tax',
        'unit_price_with_tax',
    ];

    protected $touches = ['order'];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($orderItem) {
            $orderItem->calculatePrices();
        });

        static::updating(function ($orderItem) {
            if ($orderItem->isDirty(['quantity', 'price', 'discount', 'tax'])) {
                $orderItem->calculatePrices();
            }
        });

        static::saved(function ($orderItem) {
            // Recalculate order totals when item is saved
            $orderItem->order->calculateTotals();
        });

        static::deleted(function ($orderItem) {
            // Recalculate order totals when item is deleted
            $orderItem->order->calculateTotals();
        });
    }

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    // Accessors
    public function getFinalUnitPriceAttribute(): float
    {
        return $this->price - ($this->discount / $this->quantity);
    }

    public function getSavingsAmountAttribute(): float
    {
        $originalPrice = $this->base_price ?? $this->price;
        return ($originalPrice - $this->final_unit_price) * $this->quantity;
    }

    public function getProductNameAttribute(): string
    {
        return $this->product_snapshot['name'] ?? $this->product_title ?? 'Unknown Product';
    }

    public function getProductImageAttribute(): ?string
    {
        $images = $this->product_snapshot['images'] ?? [];
        return !empty($images) ? $images[0] : null;
    }

    public function getStatusDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->fulfillment_status));
    }

    public function getTotalWithTaxAttribute(): float
    {
        return $this->total + $this->tax;
    }

    public function getUnitPriceWithTaxAttribute(): float
    {
        return $this->price + ($this->tax / $this->quantity);
    }

    // Helper methods
    public function calculatePrices(): void
    {
        // Calculate total before tax
        $this->total = $this->price * $this->quantity - $this->discount;
        
        // Ensure total is not negative
        if ($this->total < 0) {
            $this->total = 0;
        }
    }

    public function updateStatus(string $status, ?string $reason = null): void
    {
        $oldStatus = $this->fulfillment_status;
        
        $this->update(['fulfillment_status' => $status]);
        
        // Create status history record for the order
        $this->order->statusHistories()->create([
            'from_status' => $oldStatus,
            'to_status' => $status,
            'status_type' => 'item_fulfillment',
            'reason' => $reason,
            'metadata' => [
                'item_id' => $this->id,
                'product_title' => $this->product_title,
                'sku' => $this->sku,
            ],
            'user_id' => auth()->id(),
            'changed_by_type' => auth()->check() ? 
                (auth()->user()->hasRole('admin') ? 'admin' : 'vendor') : 'system',
            'changed_at' => now(),
        ]);
    }

    public function getAppliedPricingType(): string
    {
        if ($this->member_price && $this->price == $this->member_price) {
            return 'member';
        }
        
        if ($this->promotional_price && $this->price == $this->promotional_price) {
            return 'promotional';
        }
        
        if ($this->price == $this->base_price) {
            return 'regular';
        }
        
        return 'custom';
    }

    public function getPricingBreakdown(): array
    {
        return [
            'base_price' => $this->base_price,
            'promotional_price' => $this->promotional_price,
            'member_price' => $this->member_price,
            'final_price' => $this->price,
            'applied_pricing_type' => $this->getAppliedPricingType(),
            'discount_amount' => $this->discount,
            'tax_amount' => $this->tax,
            'total_amount' => $this->total,
            'savings_amount' => $this->savings_amount,
        ];
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('fulfillment_status', $status);
    }

    public function scopeByVendor($query, int $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    public function scopeByProduct($query, int $productId)
    {
        return $query->where('product_id', $productId);
    }
}
