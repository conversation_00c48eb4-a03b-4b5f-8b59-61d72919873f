<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'type',
        'first_name',
        'last_name',
        'company',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'phone',
        'email',
        'special_instructions',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    protected $appends = [
        'full_name',
        'formatted_address',
        'is_shipping_address',
        'is_billing_address',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getFormattedAddressAttribute(): string
    {
        $address = $this->address_line_1;
        
        if ($this->address_line_2) {
            $address .= ', ' . $this->address_line_2;
        }
        
        $address .= ', ' . $this->city;
        
        if ($this->state) {
            $address .= ', ' . $this->state;
        }
        
        if ($this->postal_code) {
            $address .= ' ' . $this->postal_code;
        }
        
        $address .= ', ' . $this->country;
        
        return $address;
    }

    public function getIsShippingAddressAttribute(): bool
    {
        return $this->type === 'shipping';
    }

    public function getIsBillingAddressAttribute(): bool
    {
        return $this->type === 'billing';
    }

    // Helper methods
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'full_name' => $this->full_name,
            'company' => $this->company,
            'address_line_1' => $this->address_line_1,
            'address_line_2' => $this->address_line_2,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'phone' => $this->phone,
            'email' => $this->email,
            'formatted_address' => $this->formatted_address,
            'special_instructions' => $this->special_instructions,
            'metadata' => $this->metadata,
        ];
    }

    public function isSameAs(OrderAddress $address): bool
    {
        return $this->first_name === $address->first_name &&
               $this->last_name === $address->last_name &&
               $this->address_line_1 === $address->address_line_1 &&
               $this->address_line_2 === $address->address_line_2 &&
               $this->city === $address->city &&
               $this->state === $address->state &&
               $this->postal_code === $address->postal_code &&
               $this->country === $address->country;
    }

    public static function createFromUserAddress(int $orderId, string $type, UserAddress $userAddress): self
    {
        return static::create([
            'order_id' => $orderId,
            'type' => $type,
            'first_name' => $userAddress->first_name,
            'last_name' => $userAddress->last_name,
            'company' => $userAddress->company,
            'address_line_1' => $userAddress->address_line_1,
            'address_line_2' => $userAddress->address_line_2,
            'city' => $userAddress->city,
            'state' => $userAddress->state,
            'postal_code' => $userAddress->postal_code,
            'country' => $userAddress->country,
            'phone' => $userAddress->phone,
            'email' => $userAddress->email,
            'metadata' => [
                'source' => 'user_address',
                'user_address_id' => $userAddress->id,
            ],
        ]);
    }

    // Validation helper
    public function isComplete(): bool
    {
        return !empty($this->first_name) &&
               !empty($this->last_name) &&
               !empty($this->address_line_1) &&
               !empty($this->city) &&
               !empty($this->country);
    }

    // Scopes
    public function scopeShipping($query)
    {
        return $query->where('type', 'shipping');
    }

    public function scopeBilling($query)
    {
        return $query->where('type', 'billing');
    }

    public function scopeByOrder($query, int $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    public function scopeByCountry($query, string $country)
    {
        return $query->where('country', $country);
    }
}
