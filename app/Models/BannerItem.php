<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BannerItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_id',
        'title_en',
        'title_ar',
        'media_path',
        'link_url',
        'target',
        'alt_text',
        'position',
        'is_active',
        'user_id',
    ];

    protected $appends = ['media_path_url'];

    protected $casts = [
        'banner_id' => 'integer',
        'position' => 'integer',
        'is_active' => 'boolean',
    ];

    public function banner()
    {
        return $this->belongsTo(Banner::class);
    }

    public function getMediaPathUrlAttribute($value)
    {
        if (!empty($this->media_path)) {
            return config('filesystems.disks.s3.url') . '/' . $this->media_path;
        }
        return null;
    }
}
