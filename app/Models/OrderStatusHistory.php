<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'user_id',
        'from_status',
        'to_status',
        'status_type',
        'reason',
        'notes',
        'metadata',
        'changed_by_type',
        'changed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'changed_at' => 'datetime',
    ];

    protected $appends = [
        'status_change_display',
        'changed_by_display',
        'time_since_change',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getStatusChangeDisplayAttribute(): string
    {
        $from = $this->from_status ? ucfirst(str_replace('_', ' ', $this->from_status)) : 'New';
        $to = ucfirst(str_replace('_', ' ', $this->to_status));
        
        return "{$from} → {$to}";
    }

    public function getChangedByDisplayAttribute(): string
    {
        if ($this->user) {
            return $this->user->name;
        }
        
        return ucfirst($this->changed_by_type ?? 'System');
    }

    public function getTimeSinceChangeAttribute(): string
    {
        return $this->changed_at->diffForHumans();
    }

    // Helper methods
    public function isStatusUpgrade(): bool
    {
        $statusHierarchy = [
            'pending' => 1,
            'confirmed' => 2,
            'processing' => 3,
            'shipped' => 4,
            'delivered' => 5,
            'cancelled' => 0,
            'returned' => 0,
        ];

        $fromLevel = $statusHierarchy[$this->from_status] ?? 0;
        $toLevel = $statusHierarchy[$this->to_status] ?? 0;

        return $toLevel > $fromLevel;
    }

    public function isStatusDowngrade(): bool
    {
        return !$this->isStatusUpgrade() && $this->from_status !== $this->to_status;
    }

    public function getStatusIcon(): string
    {
        return match($this->to_status) {
            'pending' => '⏳',
            'confirmed' => '✅',
            'processing' => '⚙️',
            'shipped' => '🚚',
            'delivered' => '📦',
            'cancelled' => '❌',
            'returned' => '↩️',
            default => '📋'
        };
    }

    public function getStatusColor(): string
    {
        return match($this->to_status) {
            'pending' => 'warning',
            'confirmed' => 'info',
            'processing' => 'primary',
            'shipped' => 'info',
            'delivered' => 'success',
            'cancelled' => 'danger',
            'returned' => 'secondary',
            default => 'secondary'
        };
    }

    // Scopes
    public function scopeByOrder($query, int $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    public function scopeByStatusType($query, string $statusType)
    {
        return $query->where('status_type', $statusType);
    }

    public function scopeByChangedBy($query, string $changedByType)
    {
        return $query->where('changed_by_type', $changedByType);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('changed_at', '>=', now()->subDays($days));
    }

    public function scopeOrderedByDate($query, string $direction = 'desc')
    {
        return $query->orderBy('changed_at', $direction);
    }
}
