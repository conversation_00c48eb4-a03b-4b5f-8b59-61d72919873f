<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Coupon extends Model
{
    use HasFactory;
    protected $fillable = [
        'code',
        'title_en',
        'title_ar',
        'description_en',
        'description_ar',
        'type',
        'value',
        'min_order_value',
        'usage_limit',
        'per_user_limit',
        'vendor_id',
        'user_id',
        'start_date',
        'end_date',
        'is_active',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'min_order_value' => 'decimal:2',
        'usage_limit' => 'integer',
        'per_user_limit' => 'integer',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'is_expired',
        'is_started',
        'usage_count',
        'remaining_uses',
        'formatted_value',
        'formatted_min_order_value',
    ];

    /**
     * Check if the coupon is expired
     */
    public function getIsExpiredAttribute(): bool
    {
        if (!$this->end_date) {
            return false;
        }
        return now()->isAfter($this->end_date);
    }

    /**
     * Check if the coupon has started
     */
    public function getIsStartedAttribute(): bool
    {
        if (!$this->start_date) {
            return true;
        }
        return now()->isAfter($this->start_date);
    }

    /**
     * Get the current usage count
     */
    public function getUsageCountAttribute(): int
    {
        // This would be calculated from orders/coupon_usages table
        // For now, returning 0 as placeholder
        return 0;
    }

    /**
     * Get remaining uses
     */
    public function getRemainingUsesAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }
        return max(0, $this->usage_limit - $this->usage_count);
    }

    /**
     * Get the vendor that owns this coupon
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the user who created this coupon
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get non-expired coupons
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('end_date')
              ->orWhere('end_date', '>', now());
        });
    }

    /**
     * Scope to get started coupons
     */
    public function scopeStarted($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('start_date')
              ->orWhere('start_date', '<=', now());
        });
    }

    /**
     * Scope to get valid coupons (active, started, not expired)
     */
    public function scopeValid($query)
    {
        return $query->active()->started()->notExpired();
    }

    /**
     * Scope to filter by vendor
     */
    public function scopeForVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * Scope to get platform-wide coupons (no vendor)
     */
    public function scopePlatformWide($query)
    {
        return $query->whereNull('vendor_id');
    }

    /**
     * Check if coupon is valid for use
     */
    public function isValidForUse(): bool
    {
        // Check if active
        if (!$this->is_active) {
            return false;
        }

        // Check if started
        if (!$this->is_started) {
            return false;
        }

        // Check if expired
        if ($this->is_expired) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->usage_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Get formatted discount value (Middle East currency format)
     */
    public function getFormattedValueAttribute(): string
    {
        if ($this->type === 'percentage') {
            // Format percentage without decimals if it's a whole number
            $value = $this->value;
            if ($value == intval($value)) {
                return intval($value) . '%';
            }
            return number_format($value, 2) . '%';
        }

        return 'AED ' . number_format($this->value, 2);
    }

    /**
     * Get formatted minimum order value (Middle East currency format)
     */
    public function getFormattedMinOrderValueAttribute(): ?string
    {
        if (!$this->min_order_value) {
            return null;
        }

        return 'AED ' . number_format($this->min_order_value, 2);
    }

    /**
     * Get localized title based on current locale
     */
    public function getLocalizedTitle(?string $locale = null): string
    {
        $locale = $locale ?? app()->getLocale();

        if ($locale === 'ar' && $this->title_ar) {
            return $this->title_ar;
        }

        return $this->title_en ?? '';
    }

    /**
     * Get localized description based on current locale
     */
    public function getLocalizedDescription(?string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();

        if ($locale === 'ar' && $this->description_ar) {
            return $this->description_ar;
        }

        return $this->description_en;
    }

    /**
     * Calculate discount amount for given order value
     */
    public function calculateDiscount(float $orderValue): float
    {
        if (!$this->isValidForUse()) {
            return 0;
        }

        // Check minimum order value
        if ($this->min_order_value && $orderValue < $this->min_order_value) {
            return 0;
        }

        if ($this->type === 'percentage') {
            return ($orderValue * $this->value) / 100;
        }

        if ($this->type === 'fixed') {
            return min($this->value, $orderValue);
        }

        return 0;
    }
}
