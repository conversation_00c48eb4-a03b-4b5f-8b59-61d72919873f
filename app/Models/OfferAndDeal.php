<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class OfferAndDeal extends Model
{
    protected $fillable = [
        'title_en',
        'title_ar',
        'description_en',
        'description_ar',
        'image',
        'link',
        'type',
        'regular_price',
        'offer_price',
        'discount_percentage',
        'start_time',
        'end_time',
        'is_active',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'image_url',
    ];

    /**
     * Format start_time for frontend display
     */
    public function getStartTimeAttribute($value)
    {
        return $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null;
    }

    /**
     * Format end_time for frontend display
     */
    public function getEndTimeAttribute($value)
    {
        return $value ? Carbon::parse($value)->format('Y-m-d H:i:s') : null;
    }

    /**
     * Get full URL of image
     */
    public function getImageUrlAttribute()
    {
        return $this->image
            ? config('filesystems.disks.s3.url') . '/' . ltrim($this->image, '/')
            : null;
    }
}
