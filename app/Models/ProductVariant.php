<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductVariant extends Model
{
    use HasFactory;
    protected $fillable = [
        'product_id',
        'regular_price',
        'offer_price',
        'vat_tax',
        'discount_start_date',
        'discount_end_date',
        'stock',
        'sku',
        'system_sku',
        'barcode',
        'weight',
        'length',
        'width',
        'height',
        'is_active'
    ];

    protected $casts = [
        'regular_price' => 'decimal:2',
        'offer_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'is_active' => 'boolean',
        'discount_start_date' => 'datetime',
        'discount_end_date' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->system_sku)) {
                $product->system_sku = 'SKU-' . uniqid();
            }
        });
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function media()
    {
        return $this->hasOne(ProductMedia::class);
    }

    public function inventory()
    {
        return $this->hasOne(Inventory::class);
    }

    public function productVariantAttribute()
    {
        return $this->hasOne(ProductVariantAttribute::class);
    }
}
