<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplateVariable extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'key',
        'description',
        'data_type',
        'default_value',
        'is_required',
        'category',
        'example_value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_required' => 'boolean',
    ];

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include required variables.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope a query to only include optional variables.
     */
    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    /**
     * Scope a query to filter by data type.
     */
    public function scopeDataType($query, $dataType)
    {
        return $query->where('data_type', $dataType);
    }

    /**
     * Get variables grouped by category.
     */
    public static function getGroupedByCategory(): array
    {
        return static::orderBy('category')->orderBy('name')->get()->groupBy('category')->toArray();
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'key';
    }

    /**
     * Format the variable for template usage.
     */
    public function getFormattedKey(): string
    {
        return '{{' . $this->key . '}}';
    }

    /**
     * Get the default value with proper type casting.
     */
    public function getTypedDefaultValue()
    {
        if (is_null($this->default_value)) {
            return null;
        }

        return match ($this->data_type) {
            'number' => (float) $this->default_value,
            'boolean' => (bool) $this->default_value,
            'date' => $this->default_value,
            'array' => json_decode($this->default_value, true),
            'object' => json_decode($this->default_value, true),
            default => (string) $this->default_value,
        };
    }
}
