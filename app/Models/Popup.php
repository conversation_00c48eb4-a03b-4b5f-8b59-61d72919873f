<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Popup extends Model
{
    protected $fillable = [
        'title_en',
        'title_ar',
        'content_en',
        'content_ar',
        'image',
        'button_text',
        'button_link',
        'type',
        'status',
    ];

    protected $appends = [
        'image_url',

    ];

    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return config('filesystems.disks.s3.url') . '/' . $this->image;
        }
        return null;
    }
}
