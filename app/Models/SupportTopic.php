<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportTopic extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'category_id', 'name_en', 'name_ar', 'status'
    ];

    /**
     * Boot method to handle auto-population of name_ar
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->name_ar) && !empty($model->name_en)) {
                $model->name_ar = $model->name_en;
            }
        });

        static::updating(function ($model) {
            if (empty($model->name_ar) && !empty($model->name_en)) {
                $model->name_ar = $model->name_en;
            }
        });
    }

    public function category()
    {
        return $this->belongsTo(SupportCategory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
