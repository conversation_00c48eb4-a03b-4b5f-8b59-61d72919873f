<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Warehouse extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'user_id',
        'name_en',
        'name_ar',
        'code',
        'address',
        'location',
        'contact_person',
        'contact_number',
        'email',
        'status',
        'is_active',
        'is_global'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }


    public function warehouseVendor()
    {
        return $this->belongsToMany(Vendor::class, 'warehouse_vendors', 'warehouse_id', 'vendor_id');
    }

}
