<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'fingerprint',
        'cart_data',
        'user_preferences',
        'expires_at',
        'last_accessed_at',
        'ip_address',
        'user_agent',
        'is_migrated',
        'migrated_to_user_id',
        'migrated_at',
    ];

    protected $casts = [
        'cart_data' => 'array',
        'user_preferences' => 'array',
        'expires_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'migrated_at' => 'datetime',
        'is_migrated' => 'boolean',
    ];

    protected $appends = [
        'is_expired',
        'items_count',
        'total_value',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($session) {
            $session->last_accessed_at = now();
            if (!$session->expires_at) {
                $session->expires_at = now()->addDays(7); // Default 7 days expiry
            }
        });

        static::updating(function ($session) {
            if (!$session->is_migrated) {
                $session->last_accessed_at = now();
            }
        });
    }

    // Relationships
    public function migratedToUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'migrated_to_user_id');
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getItemsCountAttribute(): int
    {
        return count($this->cart_data['items'] ?? []);
    }

    public function getTotalValueAttribute(): float
    {
        $items = $this->cart_data['items'] ?? [];
        return array_sum(array_column($items, 'total_price'));
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_migrated', false)
                    ->where('expires_at', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    public function scopeNotMigrated($query)
    {
        return $query->where('is_migrated', false);
    }

    public function scopeForSession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    // Business Logic Methods
    public function addItem(array $itemData): void
    {
        $cartData = $this->cart_data ?? ['items' => []];
        $items = $cartData['items'];
        
        $existingItemKey = $this->findExistingItem($itemData);
        
        if ($existingItemKey !== null) {
            // Update existing item quantity
            $items[$existingItemKey]['quantity'] += $itemData['quantity'];
            $items[$existingItemKey]['total_price'] = 
                $items[$existingItemKey]['unit_price'] * $items[$existingItemKey]['quantity'];
        } else {
            // Add new item
            $items[] = array_merge($itemData, [
                'added_at' => now()->toISOString(),
            ]);
        }
        
        $cartData['items'] = $items;
        $cartData['updated_at'] = now()->toISOString();
        
        $this->cart_data = $cartData;
        $this->save();
    }

    public function updateItem(string $itemId, array $updateData): bool
    {
        $cartData = $this->cart_data ?? ['items' => []];
        $items = $cartData['items'];
        
        foreach ($items as $key => $item) {
            if ($item['id'] === $itemId) {
                $items[$key] = array_merge($item, $updateData);
                if (isset($updateData['quantity'])) {
                    $items[$key]['total_price'] = $item['unit_price'] * $updateData['quantity'];
                }
                
                $cartData['items'] = $items;
                $cartData['updated_at'] = now()->toISOString();
                
                $this->cart_data = $cartData;
                return $this->save();
            }
        }
        
        return false;
    }

    public function removeItem(string $itemId): bool
    {
        $cartData = $this->cart_data ?? ['items' => []];
        $items = $cartData['items'];
        
        $items = array_filter($items, function ($item) use ($itemId) {
            return $item['id'] !== $itemId;
        });
        
        $cartData['items'] = array_values($items);
        $cartData['updated_at'] = now()->toISOString();
        
        $this->cart_data = $cartData;
        return $this->save();
    }

    public function clearCart(): void
    {
        $this->cart_data = [
            'items' => [],
            'cleared_at' => now()->toISOString(),
        ];
        $this->save();
    }

    public function migrateToUser(User $user): ?ShoppingCart
    {
        if ($this->is_migrated) {
            return null;
        }

        // Create or get user's cart
        $cart = ShoppingCart::firstOrCreate([
            'user_id' => $user->id,
            'status' => 'active',
        ], [
            'currency' => 'AED',
            'expires_at' => now()->addDays(30),
        ]);

        // Migrate items from session to cart
        $items = $this->cart_data['items'] ?? [];
        foreach ($items as $itemData) {
            $cart->items()->updateOrCreate([
                'product_id' => $itemData['product_id'],
                'variant_id' => $itemData['variant_id'] ?? null,
            ], [
                'vendor_id' => $itemData['vendor_id'],
                'quantity' => $itemData['quantity'],
                'unit_price' => $itemData['unit_price'],
                'total_price' => $itemData['total_price'],
                'product_snapshot' => $itemData['product_snapshot'] ?? [],
            ]);
        }

        // Mark session as migrated
        $this->update([
            'is_migrated' => true,
            'migrated_to_user_id' => $user->id,
            'migrated_at' => now(),
        ]);

        $cart->calculateTotals();
        return $cart;
    }

    public function extendExpiry(int $days = 7): void
    {
        $this->expires_at = now()->addDays($days);
        $this->save();
    }

    protected function findExistingItem(array $itemData): ?int
    {
        $items = $this->cart_data['items'] ?? [];
        
        foreach ($items as $key => $item) {
            if ($item['product_id'] === $itemData['product_id'] && 
                ($item['variant_id'] ?? null) === ($itemData['variant_id'] ?? null)) {
                return $key;
            }
        }
        
        return null;
    }
}
