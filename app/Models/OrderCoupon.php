<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderCoupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'coupon_id',
        'vendor_id',
        'coupon_code',
        'coupon_title',
        'coupon_type',
        'coupon_value',
        'min_order_value',
        'discount_amount',
        'order_subtotal_at_application',
        'applied_at',
        'metadata',
    ];

    protected $casts = [
        'coupon_value' => 'decimal:2',
        'min_order_value' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'order_subtotal_at_application' => 'decimal:2',
        'applied_at' => 'datetime',
        'metadata' => 'array',
    ];

    protected $appends = [
        'discount_percentage',
        'savings_display',
        'coupon_display',
        'is_vendor_specific',
        'time_since_applied',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    // Accessors
    public function getDiscountPercentageAttribute(): float
    {
        if ($this->order_subtotal_at_application > 0) {
            return ($this->discount_amount / $this->order_subtotal_at_application) * 100;
        }
        
        return 0;
    }

    public function getSavingsDisplayAttribute(): string
    {
        if ($this->coupon_type === 'percentage') {
            return number_format($this->coupon_value, 1) . '% off';
        }
        
        return 'AED ' . number_format($this->discount_amount, 2) . ' off';
    }

    public function getCouponDisplayAttribute(): string
    {
        return $this->coupon_title . ' (' . $this->coupon_code . ')';
    }

    public function getIsVendorSpecificAttribute(): bool
    {
        return !is_null($this->vendor_id);
    }

    public function getTimeSinceAppliedAttribute(): string
    {
        return $this->applied_at->diffForHumans();
    }

    // Helper methods
    public function calculateExpectedDiscount(float $orderSubtotal): float
    {
        // Check minimum order value
        if ($this->min_order_value && $orderSubtotal < $this->min_order_value) {
            return 0;
        }

        if ($this->coupon_type === 'percentage') {
            return ($orderSubtotal * $this->coupon_value) / 100;
        }

        if ($this->coupon_type === 'fixed') {
            return min($this->coupon_value, $orderSubtotal);
        }

        return 0;
    }

    public function isValidForOrder(Order $order): bool
    {
        // Check if vendor-specific coupon matches order vendor
        if ($this->vendor_id && $order->vendor_id !== $this->vendor_id) {
            return false;
        }

        // Check minimum order value
        if ($this->min_order_value && $order->subtotal < $this->min_order_value) {
            return false;
        }

        return true;
    }

    public function getDiscountBreakdown(): array
    {
        return [
            'coupon_code' => $this->coupon_code,
            'coupon_title' => $this->coupon_title,
            'coupon_type' => $this->coupon_type,
            'coupon_value' => $this->coupon_value,
            'min_order_value' => $this->min_order_value,
            'order_subtotal' => $this->order_subtotal_at_application,
            'discount_amount' => $this->discount_amount,
            'discount_percentage' => $this->discount_percentage,
            'is_vendor_specific' => $this->is_vendor_specific,
            'vendor_name' => $this->vendor?->name,
            'applied_at' => $this->applied_at,
        ];
    }

    public static function createFromCoupon(int $orderId, Coupon $coupon, float $discountAmount, float $orderSubtotal): self
    {
        return static::create([
            'order_id' => $orderId,
            'coupon_id' => $coupon->id,
            'vendor_id' => $coupon->vendor_id,
            'coupon_code' => $coupon->code,
            'coupon_title' => $coupon->title_en,
            'coupon_type' => $coupon->type,
            'coupon_value' => $coupon->value,
            'min_order_value' => $coupon->min_order_value,
            'discount_amount' => $discountAmount,
            'order_subtotal_at_application' => $orderSubtotal,
            'applied_at' => now(),
            'metadata' => [
                'original_coupon_data' => $coupon->toArray(),
            ],
        ]);
    }

    // Scopes
    public function scopeByOrder($query, int $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    public function scopeByVendor($query, int $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('coupon_type', $type);
    }

    public function scopeVendorSpecific($query)
    {
        return $query->whereNotNull('vendor_id');
    }

    public function scopeGlobal($query)
    {
        return $query->whereNull('vendor_id');
    }

    public function scopeOrderedByDiscount($query, string $direction = 'desc')
    {
        return $query->orderBy('discount_amount', $direction);
    }
}
