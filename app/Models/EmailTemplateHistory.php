<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailTemplateHistory extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'template_id',
        'version_number',
        'name',
        'subject',
        'body_html',
        'body_text',
        'variables',
        'metadata',
        'changed_by',
        'change_reason',
        'created_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'variables' => 'array',
        'metadata' => 'array',
        'version_number' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * Get the template that owns this history record.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(EmailTemplate::class, 'template_id');
    }

    /**
     * Get the user who made this change.
     */
    public function changedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    /**
     * Scope a query to order by version number descending.
     */
    public function scopeLatestFirst($query)
    {
        return $query->orderBy('version_number', 'desc');
    }

    /**
     * Scope a query to order by version number ascending.
     */
    public function scopeOldestFirst($query)
    {
        return $query->orderBy('version_number', 'asc');
    }

    /**
     * Get the previous version of this template.
     */
    public function getPreviousVersion(): ?self
    {
        return static::where('template_id', $this->template_id)
            ->where('version_number', '<', $this->version_number)
            ->orderBy('version_number', 'desc')
            ->first();
    }

    /**
     * Get the next version of this template.
     */
    public function getNextVersion(): ?self
    {
        return static::where('template_id', $this->template_id)
            ->where('version_number', '>', $this->version_number)
            ->orderBy('version_number', 'asc')
            ->first();
    }

    /**
     * Check if this is the latest version.
     */
    public function isLatestVersion(): bool
    {
        $latestVersion = static::where('template_id', $this->template_id)
            ->max('version_number');

        return $this->version_number === $latestVersion;
    }

    /**
     * Get a summary of changes from the previous version.
     */
    public function getChangesSummary(): array
    {
        $previous = $this->getPreviousVersion();

        if (!$previous) {
            return ['type' => 'created', 'changes' => []];
        }

        $changes = [];

        if ($previous->name !== $this->name) {
            $changes['name'] = ['from' => $previous->name, 'to' => $this->name];
        }

        if ($previous->subject !== $this->subject) {
            $changes['subject'] = ['from' => $previous->subject, 'to' => $this->subject];
        }

        if ($previous->body_html !== $this->body_html) {
            $changes['body_html'] = ['changed' => true];
        }

        if ($previous->body_text !== $this->body_text) {
            $changes['body_text'] = ['changed' => true];
        }

        return ['type' => 'updated', 'changes' => $changes];
    }
}
