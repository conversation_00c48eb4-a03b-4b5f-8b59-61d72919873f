<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserAddress extends Model
{
    protected $fillable = [
        'user_id',
        'address_type',
        'flat_or_villa_number',
        'building_name',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'is_default',
        'is_shipping',
        'is_billing',
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
