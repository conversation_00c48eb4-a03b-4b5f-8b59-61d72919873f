<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductMedia extends Model
{
    protected $fillable = [
        'product_id',
        'product_variant_id',
        'type',
        'path',
        'title',
        'alt_text',
        'lang_code',
        'position',
        'is_primary'
    ];
    protected $appends = ['path_url'];

    protected $casts = [
        'is_primary' => 'boolean',
        'position' => 'integer'
    ];


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getPathUrlAttribute()
    {
        if ($this->path) {
            return config('filesystems.disks.s3.url') . '/' . $this->path;
        }
        return null;
    }
}
