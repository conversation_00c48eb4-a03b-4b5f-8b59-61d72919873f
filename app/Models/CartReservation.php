<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class CartReservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'cart_item_id',
        'product_id',
        'variant_id',
        'inventory_id',
        'quantity_reserved',
        'reserved_until',
        'status',
        'reservation_token',
        'notes',
    ];

    protected $casts = [
        'quantity_reserved' => 'integer',
        'reserved_until' => 'datetime',
    ];

    protected $appends = [
        'is_expired',
        'time_remaining',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($reservation) {
            if (empty($reservation->reservation_token)) {
                $reservation->reservation_token = Str::random(32);
            }
            
            if (!$reservation->reserved_until) {
                $reservation->reserved_until = now()->addMinutes(15); // Default 15 minutes
            }
        });
    }

    // Relationships
    public function cartItem(): BelongsTo
    {
        return $this->belongsTo(CartItem::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function inventory(): BelongsTo
    {
        return $this->belongsTo(Inventory::class);
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->reserved_until && $this->reserved_until->isPast();
    }

    public function getTimeRemainingAttribute(): ?int
    {
        if ($this->is_expired) {
            return 0;
        }
        
        return $this->reserved_until ? 
            now()->diffInSeconds($this->reserved_until, false) : 
            null;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('reserved_until', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('reserved_until', '<', now())
                    ->where('status', 'active');
    }

    public function scopeForProduct($query, int $productId, ?int $variantId = null)
    {
        $query = $query->where('product_id', $productId);
        
        if ($variantId) {
            $query->where('variant_id', $variantId);
        }
        
        return $query;
    }

    public function scopeByToken($query, string $token)
    {
        return $query->where('reservation_token', $token);
    }

    // Business Logic Methods
    public function extend(int $minutes = 15): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $this->reserved_until = now()->addMinutes($minutes);
        return $this->save();
    }

    public function release(): bool
    {
        $this->status = 'released';
        return $this->save();
    }

    public function convert(): bool
    {
        $this->status = 'converted';
        return $this->save();
    }

    public function expire(): bool
    {
        $this->status = 'expired';
        return $this->save();
    }

    public function isValid(): bool
    {
        return $this->status === 'active' && !$this->is_expired;
    }

    public function canBeExtended(): bool
    {
        return $this->status === 'active' && 
               $this->reserved_until->diffInHours(now()) < 2; // Can extend within 2 hours
    }

    public static function createForCartItem(CartItem $cartItem, int $minutes = 15): self
    {
        return static::create([
            'cart_item_id' => $cartItem->id,
            'product_id' => $cartItem->product_id,
            'variant_id' => $cartItem->variant_id,
            'quantity_reserved' => $cartItem->quantity,
            'reserved_until' => now()->addMinutes($minutes),
            'status' => 'active',
        ]);
    }

    public static function getTotalReservedQuantity(int $productId, ?int $variantId = null): int
    {
        return static::active()
            ->forProduct($productId, $variantId)
            ->sum('quantity_reserved');
    }

    public static function releaseExpiredReservations(): int
    {
        return static::expired()->update(['status' => 'expired']);
    }

    public static function getAvailableStock(int $productId, ?int $variantId = null): int
    {
        // Get the actual stock from product/variant
        if ($variantId) {
            $variant = ProductVariant::find($variantId);
            $totalStock = $variant ? $variant->stock_quantity : 0;
        } else {
            $product = Product::find($productId);
            $totalStock = $product ? $product->stock_quantity : 0;
        }

        // Subtract reserved quantities
        $reservedQuantity = static::getTotalReservedQuantity($productId, $variantId);
        
        return max(0, $totalStock - $reservedQuantity);
    }
}
