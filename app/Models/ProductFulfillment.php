<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductFulfillment extends Model
{
    protected $fillable = [
        'product_id',
        'mode_id', // Fulfillment By Amazon / vendor / self
        'collection_point',
        'shipping_time',
        'shipping_fee',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    //mood
    public function mode()
    {
        return $this->belongsTo(DropdownOption::class, 'mode_id','id');
    }
}
