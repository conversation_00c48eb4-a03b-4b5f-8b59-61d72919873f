<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorBank extends Model
{
protected $fillable = [
    'vendor_id',
    'bank_name',
    'branch_name',
    'account_holder_name',
    'iban_number',
    'original_cheque_number',
    'bank_certificate_copy',
    'is_primary',
    'is_active',
];

protected $casts = [
    'is_active' => 'boolean',
    'is_primary' => 'boolean',
];


}
