<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class EmailTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'subject',
        'body_html',
        'body_text',
        'category_id',
        'language',
        'is_active',
        'is_default',
        'variables',
        'metadata',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'variables' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($template) {
            if (empty($template->uuid)) {
                $template->uuid = (string) Str::uuid();
            }
            if (empty($template->slug)) {
                $template->slug = Str::slug($template->name);
            }
        });

        static::updating(function ($template) {
            // Create history record before updating
            $template->createHistoryRecord();
        });
    }

    /**
     * Get the category that owns the template.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(EmailTemplateCategory::class, 'category_id');
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the template.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the history records for this template.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(EmailTemplateHistory::class, 'template_id')->orderBy('version_number', 'desc');
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include default templates.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope a query to filter by language.
     */
    public function scopeLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Create a history record for the current template state.
     */
    public function createHistoryRecord(string $changeReason = null): void
    {
        $latestVersion = $this->histories()->max('version_number') ?? 0;

        EmailTemplateHistory::create([
            'template_id' => $this->id,
            'version_number' => $latestVersion + 1,
            'name' => $this->getOriginal('name'),
            'subject' => $this->getOriginal('subject'),
            'body_html' => $this->getOriginal('body_html'),
            'body_text' => $this->getOriginal('body_text'),
            'variables' => $this->getOriginal('variables'),
            'metadata' => $this->getOriginal('metadata'),
            'changed_by' => auth()->id(),
            'change_reason' => $changeReason,
            'created_at' => now(),
        ]);
    }

    /**
     * Get the next version number for this template.
     */
    public function getNextVersionNumber(): int
    {
        return ($this->histories()->max('version_number') ?? 0) + 1;
    }

    /**
     * Check if template has required variables.
     */
    public function hasRequiredVariables(array $providedVariables = []): bool
    {
        $templateVariables = $this->variables ?? [];
        $requiredVariables = collect($templateVariables)->filter(function ($var) {
            return isset($var['required']) && $var['required'];
        });

        foreach ($requiredVariables as $variable) {
            if (!isset($providedVariables[$variable['key']])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all variables used in the template content.
     */
    public function extractVariablesFromContent(): array
    {
        $content = $this->body_html . ' ' . $this->subject . ' ' . ($this->body_text ?? '');
        preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);

        return array_unique($matches[1] ?? []);
    }

    /**
     * Validate template syntax and variables.
     */
    public function validateTemplate(): array
    {
        $errors = [];

        // Check for required fields
        if (empty($this->subject)) {
            $errors[] = 'Subject is required';
        }

        if (empty($this->body_html)) {
            $errors[] = 'HTML body is required';
        }

        // Check for valid HTML (basic validation)
        if (!empty($this->body_html)) {
            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);
            if (!$dom->loadHTML($this->body_html)) {
                $errors[] = 'Invalid HTML structure in body';
            }
            libxml_clear_errors();
        }

        // Check for unknown variables
        $usedVariables = $this->extractVariablesFromContent();
        $knownVariables = EmailTemplateVariable::pluck('key')->toArray();

        foreach ($usedVariables as $variable) {
            if (!in_array(trim($variable), $knownVariables)) {
                $errors[] = "Unknown variable: {{" . trim($variable) . "}}";
            }
        }

        return $errors;
    }
}
