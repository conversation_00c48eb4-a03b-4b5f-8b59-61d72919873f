<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CustomerPricingTier extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'discount_percentage',
        'minimum_order_value',
        'priority',
        'minimum_annual_spend',
        'minimum_orders_count',
        'is_active',
        'auto_assign',
        'benefits',
        'metadata',
    ];

    protected $casts = [
        'discount_percentage' => 'decimal:2',
        'minimum_order_value' => 'decimal:2',
        'priority' => 'integer',
        'minimum_annual_spend' => 'decimal:2',
        'minimum_orders_count' => 'integer',
        'is_active' => 'boolean',
        'auto_assign' => 'boolean',
        'benefits' => 'array',
        'metadata' => 'array',
    ];

    protected $appends = [
        'customers_count',
        'qualification_display',
        'benefits_display',
        'discount_display',
    ];

    // Relationships
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'pricing_tier_id');
    }

    // Accessors
    public function getCustomersCountAttribute(): int
    {
        return $this->customers()->count();
    }

    public function getQualificationDisplayAttribute(): string
    {
        $requirements = [];
        
        if ($this->minimum_annual_spend) {
            $requirements[] = 'Annual spend: AED ' . number_format($this->minimum_annual_spend, 2);
        }
        
        if ($this->minimum_orders_count) {
            $requirements[] = 'Minimum orders: ' . $this->minimum_orders_count;
        }
        
        if ($this->minimum_order_value) {
            $requirements[] = 'Minimum order: AED ' . number_format($this->minimum_order_value, 2);
        }
        
        return implode(' | ', $requirements) ?: 'No specific requirements';
    }

    public function getBenefitsDisplayAttribute(): array
    {
        $benefits = $this->benefits ?? [];
        
        if ($this->discount_percentage) {
            array_unshift($benefits, $this->discount_percentage . '% discount on all products');
        }
        
        return $benefits;
    }

    public function getDiscountDisplayAttribute(): string
    {
        if ($this->discount_percentage) {
            return number_format($this->discount_percentage, 1) . '% off';
        }
        
        return 'No discount';
    }

    // Helper methods
    public function calculateDiscount(float $amount): float
    {
        if (!$this->discount_percentage) {
            return 0;
        }
        
        return ($amount * $this->discount_percentage) / 100;
    }

    public function isCustomerQualified(Customer $customer): bool
    {
        // Check annual spend requirement
        if ($this->minimum_annual_spend && $customer->total_annual_spend < $this->minimum_annual_spend) {
            return false;
        }
        
        // Check orders count requirement
        if ($this->minimum_orders_count && $customer->total_orders_count < $this->minimum_orders_count) {
            return false;
        }
        
        return true;
    }

    public function assignToCustomer(Customer $customer, ?string $reason = null): bool
    {
        if (!$this->isCustomerQualified($customer)) {
            return false;
        }
        
        $customer->update([
            'pricing_tier_id' => $this->id,
            'tier_assigned_at' => now(),
            'last_tier_evaluation_at' => now(),
        ]);
        
        return true;
    }

    public static function findBestTierForCustomer(Customer $customer): ?self
    {
        return static::where('is_active', true)
            ->where('auto_assign', true)
            ->orderBy('priority', 'desc')
            ->get()
            ->first(function ($tier) use ($customer) {
                return $tier->isCustomerQualified($customer);
            });
    }

    public static function evaluateCustomerTier(Customer $customer): ?self
    {
        $bestTier = static::findBestTierForCustomer($customer);
        
        if ($bestTier && $customer->pricing_tier_id !== $bestTier->id) {
            $bestTier->assignToCustomer($customer, 'Auto-assigned based on qualification');
        }
        
        return $bestTier;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAutoAssign($query)
    {
        return $query->where('auto_assign', true);
    }

    public function scopeOrderedByPriority($query, string $direction = 'desc')
    {
        return $query->orderBy('priority', $direction);
    }

    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    public function scopeWithMinimumSpend($query, float $amount)
    {
        return $query->where('minimum_annual_spend', '<=', $amount);
    }

    public function scopeWithMinimumOrders($query, int $count)
    {
        return $query->where('minimum_orders_count', '<=', $count);
    }
}
