<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'cart_id',
        'product_id',
        'vendor_id',
        'variant_id',
        'quantity',
        'unit_price',
        'total_price',
        'discount_amount',
        'tax_amount',
        'product_snapshot',
        'customizations',
        'special_instructions',
        'metadata',
        'base_price',
        'promotional_price',
        'applied_discounts',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'base_price' => 'decimal:2',
        'promotional_price' => 'decimal:2',
        'product_snapshot' => 'array',
        'customizations' => 'array',
        'metadata' => 'array',
        'applied_discounts' => 'array',
    ];

    protected $appends = [
        'final_unit_price',
        'savings_amount',
        'product_name',
        'product_image',
    ];

    protected $touches = ['cart'];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($cartItem) {
            $cartItem->calculatePrices();
        });

        static::updating(function ($cartItem) {
            if ($cartItem->isDirty(['quantity', 'unit_price', 'discount_amount'])) {
                $cartItem->calculatePrices();
            }
        });

        static::saved(function ($cartItem) {
            // Recalculate cart totals when item is saved
            $cartItem->cart->calculateTotals();
            $cartItem->cart->touch(); // Explicitly touch the cart
        });

        static::deleted(function ($cartItem) {
            // Recalculate cart totals when item is deleted
            $cartItem->cart->calculateTotals();
        });
    }

    // Relationships
    public function cart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, 'cart_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(CartReservation::class);
    }

    // Accessors
    public function getFinalUnitPriceAttribute(): float
    {
        return $this->unit_price - ($this->discount_amount / $this->quantity);
    }

    public function getSavingsAmountAttribute(): float
    {
        if ($this->promotional_price && $this->base_price > $this->promotional_price) {
            return ($this->base_price - $this->promotional_price) * $this->quantity;
        }
        return $this->discount_amount;
    }

    public function getProductNameAttribute(): ?string
    {
        return $this->product_snapshot['name'] ?? $this->product?->name_en;
    }

    public function getProductImageAttribute(): ?string
    {
        return $this->product_snapshot['image'] ?? $this->product?->featured_image;
    }

    // Business Logic Methods
    public function calculatePrices(): void
    {
        $this->total_price = $this->unit_price * $this->quantity;
        $this->tax_amount = $this->calculateTax();
    }

    public function updateQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return $this->delete();
        }

        $this->quantity = $quantity;
        $this->calculatePrices();
        return $this->save();
    }

    public function increaseQuantity(int $amount = 1): bool
    {
        return $this->updateQuantity($this->quantity + $amount);
    }

    public function decreaseQuantity(int $amount = 1): bool
    {
        return $this->updateQuantity($this->quantity - $amount);
    }

    public function applyDiscount(float $discountAmount): void
    {
        $this->discount_amount = min($discountAmount, $this->total_price);
        $this->save();
    }

    public function createProductSnapshot(): array
    {
        $product = $this->product;
        $variant = $this->variant;

        return [
            'id' => $product->id,
            'name' => $product->name_en,
            'name_ar' => $product->name_ar,
            'sku' => $product->sku,
            'image' => $product->featured_image,
            'description' => $product->description_en,
            'variant' => $variant ? [
                'id' => $variant->id,
                'sku' => $variant->sku,
                'name' => $variant->name,
                'attributes' => $variant->attributes,
            ] : null,
            'vendor' => [
                'id' => $this->vendor->id,
                'name' => $this->vendor->name_tl_en,
                'display_name' => $this->vendor->vendor_display_name_en,
            ],
            'captured_at' => now()->toISOString(),
        ];
    }

    public function isAvailable(): bool
    {
        return $this->product &&
               $this->product->is_active &&
               $this->product->is_approved &&
               $this->product->status === 'submitted' &&
               (!$this->variant || $this->variant->is_active);
    }

    public function hasInsufficientStock(): bool
    {
        if ($this->variant) {
            // For variants, check the variant's inventory or direct stock field
            $availableStock = $this->variant->inventory
                ? $this->variant->inventory->available_stock
                : $this->variant->stock;
        } else {
            // For products, check the product's inventory
            $availableStock = $this->product->inventory
                ? $this->product->inventory->available_stock
                : 0;
        }

        return $this->quantity > $availableStock;
    }

    protected function calculateTax(): float
    {
        // Tax calculation logic based on product and location
        // This is a placeholder - implement based on business rules
        return 0.00;
    }
}
