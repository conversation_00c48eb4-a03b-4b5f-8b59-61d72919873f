<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductClass extends Model
{
    protected $table = 'product_classes';

    protected $fillable = [
        'name_en',
        'name_ar',
        'code',
        'category_id',
        'sub_category_id',
        'parent_id',
        'is_popular',
        'status',
        'slug',
    ];

    protected $casts = [
        'category_id' => 'integer',
        'sub_category_id' => 'integer',
        'parent_id' => 'integer',
        'is_popular' => 'boolean',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(ProductClass::class, 'parent_id');
    }

    public function subClasses(): HasMany
    {
        return $this->hasMany(ProductClass::class, 'parent_id');
    }
}
