<?php

namespace App\Http\Controllers;

use App\Http\Requests\IsActiveUpdateRequest;
use App\Http\Requests\StoreVendorInformationRequest;
use App\Http\Requests\UpdateVendorInformationRequest;
use App\Services\VendorInformationService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VendorController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(VendorInformationService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'VendorInformation data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVendorInformationRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'VendorInformation created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create VendorInformation', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    public function storeVendor(StoreVendorInformationRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->storeVendor($request);

            return $this->successResponse($resource, 'VendorInformation created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create VendorInformation', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'VendorInformation retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve VendorInformation', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVendorInformationRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'VendorInformation updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create VendorInformation', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'VendorInformation deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete VendorInformation', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function approveVendor(Request $request, int $id): JsonResponse
    {
        try {
            $this->service->approveVendor($request, $id);

            return $this->successResponse(null, 'Vendor approved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to approve Vendor', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function activeDeactivateVendor(IsActiveUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $user = $this->service->activeDeactivateVendor($request, $id);

            if ($user->is_active == 1) {
                return $this->successResponse($user, 'Vendor active successfully!', Response::HTTP_OK);
            } else {
                return $this->successResponse($user, 'Vendor deactivated successfully!', Response::HTTP_OK);
            }
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to active/deactivate Vendor', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function profile(): JsonResponse
    {
        try {
            $resource = $this->service->profile();
            return $this->successResponse($resource, 'Vendor profile retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Vendor profile', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updateProfile(UpdateVendorInformationRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->updateProfile($request);
            return $this->successResponse($resource, 'Vendor profile updated successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update Vendor profile', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function vendorListByActive(Request $request): JsonResponse
    {
        try {
            $data = $this->service->vendorListByActive($request);

            return $this->successResponse($data, 'Vendor list retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Vendor list', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
