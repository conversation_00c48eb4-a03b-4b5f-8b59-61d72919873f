<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\UpdateOrderStatusRequest;
use App\Http\Resources\Order\OrderResource;
use App\Http\Resources\Order\OrderCollectionResource;
use App\Http\Resources\Order\OrderSummaryResource;
use App\Models\Order;
use App\Services\OrderService;
use App\Services\OrderStatusService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrderController extends Controller
{
    use HelperTrait;

    protected OrderService $orderService;
    protected OrderStatusService $statusService;

    public function __construct(OrderService $orderService, OrderStatusService $statusService)
    {
        $this->orderService = $orderService;
        $this->statusService = $statusService;
    }

    /**
     * Display a listing of vendor's orders.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $vendorId = auth()->user()->vendor_id;
            
            if (!$vendorId) {
                return $this->errorResponse(
                    'Vendor not found for user',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $filters = [
                'vendor_id' => $vendorId,
                'status' => $request->get('status'),
                'payment_status' => $request->get('payment_status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
            ];

            $perPage = $request->get('per_page', 15);
            $orders = $this->orderService->getVendorOrders($vendorId, $filters, $perPage);

            return $this->successResponse(
                new OrderCollectionResource($orders),
                'Orders retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve orders',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $order = $this->orderService->getOrderByUuid($uuid);

            if (!$order) {
                return $this->errorResponse(
                    'Order not found',
                    'Not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Verify vendor ownership
            if ($order->vendor_id !== auth()->user()->vendor_id) {
                return $this->errorResponse(
                    'Unauthorized access to order',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            return $this->successResponse(
                new OrderResource($order),
                'Order retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update order status (vendor can only update fulfillment status).
     */
    public function updateStatus(UpdateOrderStatusRequest $request, string $uuid): JsonResponse
    {
        try {
            $order = Order::where('uuid', $uuid)->firstOrFail();

            // Verify vendor ownership
            if ($order->vendor_id !== auth()->user()->vendor_id) {
                return $this->errorResponse(
                    'Unauthorized access to order',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $validatedData = $request->validatedWithComputed();

            // Vendors can only update fulfillment status
            if (isset($validatedData['fulfillment_status'])) {
                $this->statusService->updateOrderStatus(
                    $order,
                    $validatedData['fulfillment_status'],
                    $validatedData['reason'] ?? null,
                    $validatedData['metadata'] ?? null
                );
            }

            // Update tracking number if provided
            if (isset($validatedData['tracking_number'])) {
                $order->update(['tracking_number' => $validatedData['tracking_number']]);
            }

            // Update vendor notes
            if (isset($validatedData['notes'])) {
                $order->update(['admin_note' => $validatedData['notes']]); // Using admin_note for vendor notes
            }

            return $this->successResponse(
                new OrderResource($order->fresh(['items', 'addresses', 'statusHistories'])),
                'Order status updated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to update order status',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get vendor order analytics.
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $vendorId = auth()->user()->vendor_id;
            
            if (!$vendorId) {
                return $this->errorResponse(
                    'Vendor not found for user',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $filters = [
                'vendor_id' => $vendorId,
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            $analytics = $this->orderService->getOrderAnalytics($filters);

            return $this->successResponse(
                $analytics,
                'Order analytics retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve analytics',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get vendor dashboard data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        try {
            $vendorId = auth()->user()->vendor_id;
            
            if (!$vendorId) {
                return $this->errorResponse(
                    'Vendor not found for user',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            // Recent orders
            $recentOrders = $this->orderService->getVendorOrders(
                $vendorId,
                ['sort_by' => 'created_at', 'sort_direction' => 'desc'],
                10
            );

            // Analytics
            $analytics = $this->orderService->getOrderAnalytics(['vendor_id' => $vendorId]);

            // Pending actions (orders that need vendor attention)
            $pendingOrders = $this->orderService->getVendorOrders(
                $vendorId,
                ['status' => 'confirmed'],
                5
            );

            return $this->successResponse([
                'recent_orders' => OrderSummaryResource::collection($recentOrders->items()),
                'pending_orders' => OrderSummaryResource::collection($pendingOrders->items()),
                'analytics' => $analytics,
            ], 'Dashboard data retrieved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve dashboard data',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get orders requiring vendor action.
     */
    public function pendingActions(Request $request): JsonResponse
    {
        try {
            $vendorId = auth()->user()->vendor_id;
            
            if (!$vendorId) {
                return $this->errorResponse(
                    'Vendor not found for user',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            // Orders that need vendor attention (confirmed orders ready for processing)
            $pendingOrders = $this->orderService->getVendorOrders(
                $vendorId,
                ['status' => 'confirmed'],
                $request->get('per_page', 15)
            );

            return $this->successResponse(
                new OrderCollectionResource($pendingOrders),
                'Pending orders retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve pending orders',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
