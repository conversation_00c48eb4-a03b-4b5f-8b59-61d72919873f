<?php

namespace App\Http\Controllers;

use App\Http\Requests\BrandIndexRequest;
use App\Http\Requests\IsActiveUpdateRequest;
use App\Http\Requests\StatusUpdateRequest;
use App\Http\Requests\StoreBrandRequest;
use App\Http\Requests\UpdateBrandRequest;
use App\Models\Brand;
use App\Services\BrandService;
use App\Services\StatusService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BrandController extends Controller
{
    use HelperTrait;

    private $service;
    private $statusService;

    public function __construct(BrandService $service, StatusService $statusService)
    {
        $this->service = $service;
        $this->statusService = $statusService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(BrandIndexRequest $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Brand data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBrandRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Brand created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Brand', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'Brand retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Brand', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBrandRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'Brand updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Brand', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'Brand deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete Brand', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function brandListByActive(Request $request): JsonResponse
    {
        try {
            $data = $this->service->brandListByActive($request);

            return $this->successResponse($data, 'Brand list retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Brand list', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function statusUpdate(IsActiveUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $model = Brand::class;
            $resource = $this->statusService->IsActiveUpdate($request, $id, $model);

            return $this->successResponse($resource, 'Brand approved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to approve Brand', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
