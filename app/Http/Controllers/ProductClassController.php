<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProductClassListRequest;
use App\Http\Requests\StoreProductClassRequest;
use App\Http\Requests\UpdateProductClassRequest;
use App\Models\ProductClass;
use App\Services\ProductClassService;
use App\Services\StatusService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProductClassController extends Controller
{
    use HelperTrait;

    private $service;
    private $statusService;

    public function __construct(ProductClassService $service, StatusService $statusService)
    {
        $this->service = $service;
        $this->statusService = $statusService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'ProductClass data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProductClassRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'ProductClass created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create ProductClass', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function statusUpdate(Request $request, int $id): JsonResponse
    {
        try {
            $model = ProductClass::class;
            $resource = $this->statusService->statusUpdate($request, $id, $model);

            return $this->successResponse($resource, 'ProductClass status updated successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update ProductClass status', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'ProductClass retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve ProductClass', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductClassRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'ProductClass updated successfully!', Response::HTTP_OK);
        }catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create ProductClass', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'ProductClass deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete ProductClass', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getClassByCategory(int $categoryId)
    {
        try {
            $data = $this->service->getClassByCategory($categoryId);
            return $this->successResponse($data, 'ProductClass by category retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve ProductClass by category', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getSubClasses(int $classId)
    {
        try {
            $data = $this->service->getSubClasses($classId);
            return $this->successResponse($data, 'ProductClass sub-classes retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve ProductClass sub-classes', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

    }

    public function classListByActive(ProductClassListRequest $request): JsonResponse
    {
        try {
            $data = $this->service->classListByActive($request);

            return $this->successResponse($data, 'ProductClass list retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve ProductClass list', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function productListByVendor($id)
    {
        try {
            $data = $this->service->productListByVendor($id);

            return $this->successResponse($data, 'Product list by vendor retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve product list by vendor', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
