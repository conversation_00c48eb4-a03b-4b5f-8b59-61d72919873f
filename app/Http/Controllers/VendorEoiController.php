<?php

namespace App\Http\Controllers;

use App\Http\Requests\VendorEoiRequest;
use App\Http\Requests\UpdateVendorEoiRequest;
use App\Http\Requests\ApproveVendorEoiRequest;
use Illuminate\Http\Request;
use App\Services\VendorEoiService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

use App\Traits\HelperTrait;

class VendorEoiController extends Controller
{
    use HelperTrait;
    protected $vendorEoiService;

    public function __construct(VendorEoiService $vendorEoiService)
    {
        $this->vendorEoiService = $vendorEoiService;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->vendorEoiService->index($request);

            return $this->successResponse($data, 'Vendor EOI data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(VendorEoiRequest $request): JsonResponse
    {
        try {
            $resource = $this->vendorEoiService->store($request);

            return $this->successResponse($resource, 'Vendor EOI created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Vendor EOI', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->vendorEoiService->show($id);

            return $this->successResponse($resource, 'Vendor EOI retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Vendor EOI', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }


    public function update(UpdateVendorEoiRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->vendorEoiService->update($request, $id);

            return $this->successResponse($resource, 'Vendor EOI updated successfully!', Response::HTTP_OK);
        }catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Vendor EOI', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $this->vendorEoiService->destroy($id);

            return $this->successResponse(null, 'Vendor EOI deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete Vendor EOI', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    // eoi_details

    public function eoi_details(Request $request, $eoi_id): JsonResponse
    {
        try {
            $resource = $this->vendorEoiService->eoiDetails($eoi_id);

            return $this->successResponse($resource, 'Vendor EOI retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), $th->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Vendor EOI', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    


    public function approveEOI(ApproveVendorEoiRequest $request): JsonResponse
    {
        try {
            $data = $this->vendorEoiService->approveVendorEoi($request);

            return $this->successResponse($data, 'Vendor EOI has been approved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
