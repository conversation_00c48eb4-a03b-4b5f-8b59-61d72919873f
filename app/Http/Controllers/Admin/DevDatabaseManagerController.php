<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class DevDatabaseManagerController extends Controller
{
    /**
     * Static credentials for development access
     */
    private const DEV_USERNAME = 'aDminDev';
    private const DEV_PASSWORD = 'O&sT2E*O7pvp7ZPe';

    /**
     * Allowed artisan commands for security
     */
    private const ALLOWED_COMMANDS = [
        'optimize:clear',
        'migrate:fresh --seed',
        'migrate:fresh',
        'migrate',
        'migrate:rollback',
        'migrate:reset',
        'db:seed',
        'cache:clear',
        'config:clear',
        'route:clear',
        'view:clear',
        'queue:work --stop-when-empty',
        'storage:link'
        // Composer commands
        // 'composer:install',
        // 'composer:update',
        // 'composer:dump-autoload',
        // 'composer:require',
        // 'composer:remove',

    ];

    /**
     * Commands that are potentially destructive (for documentation purposes)
     */
    private const DESTRUCTIVE_COMMANDS = [
        'migrate:fresh',
        'migrate:fresh --seed',
        'migrate:reset'
    ];

    /**
     * Test endpoint to verify system status
     */
    public function test(): JsonResponse
    {
        $this->checkEnvironment();

        return response()->json([
            'status' => 'working',
            'env' => config('app.env'),
            'username_config' => self::DEV_USERNAME,
            'timestamp' => now()->toISOString(),
            'allowed_commands' => self::ALLOWED_COMMANDS
        ]);
    }

    /**
     * Display the database manager interface
     */
    public function index()
    {
        $this->checkEnvironment();
        return view('dev.db-manager');
    }

    /**
     * Execute artisan commands with authentication
     */
    public function executeCommand(Request $request): JsonResponse
    {
        $this->checkEnvironment();

        // Authenticate user
        if (!$this->authenticate($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials',
                'output' => ''
            ], 401);
        }

        // Validate command
        $command = $request->input('command');
        if (!$this->isCommandAllowed($command)) {
            return response()->json([
                'success' => false,
                'message' => 'Command not allowed. Only specific database and cache commands are permitted.',
                'output' => 'Allowed commands: ' . implode(', ', self::ALLOWED_COMMANDS)
            ], 400);
        }

        try {
            return $this->runCommand($command);
        } catch (ProcessFailedException $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Process failed to execute',
                'output' => $exception->getMessage()
            ], 500);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred',
                'output' => $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Quick refresh: migrate:fresh --seed
     */
    public function quickRefresh(Request $request): JsonResponse
    {
        $this->checkEnvironment();

        // Authenticate user
        if (!$this->authenticate($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        try {
            // Use output buffering to capture all output including direct echo/print statements
            ob_start();

            // Capture Artisan output
            Artisan::call('migrate:fresh', ['--seed' => true]);
            $artisanOutput = Artisan::output();

            // Capture any direct output from seeders
            $directOutput = ob_get_clean();

            // Combine both outputs
            $combinedOutput = trim($directOutput . "\n" . $artisanOutput);

            return response()->json([
                'success' => true,
                'message' => 'Database refreshed and seeded successfully',
                'output' => $combinedOutput
            ]);
        } catch (\Exception $exception) {
            // Clean output buffer in case of exception
            if (ob_get_level()) {
                ob_end_clean();
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh database',
                'output' => $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Check if application is in development environment
     */
    private function checkEnvironment(): void
    {
        if (!in_array(config('app.env'), ['local', 'development'])) {
            abort(404, 'Not Found');
        }
    }

    /**
     * Authenticate user with static credentials
     */
    private function authenticate(Request $request): bool
    {
        $username = $request->input('username');
        $password = $request->input('password');

        // Debug logging for development
        Log::info('Auth attempt', [
            'received_username' => $username,
            'received_password' => $password,
            'expected_username' => self::DEV_USERNAME,
            'expected_password' => self::DEV_PASSWORD,
            'username_match' => $username === self::DEV_USERNAME,
            'password_match' => $password === self::DEV_PASSWORD
        ]);

        return $username === self::DEV_USERNAME && $password === self::DEV_PASSWORD;
    }

    /**
     * Check if command is in allowed list
     */
    private function isCommandAllowed(string $command): bool
    {
        return in_array($command, self::ALLOWED_COMMANDS);
    }

    /**
     * Check if command is destructive and requires confirmation
     */
    private function isDestructiveCommand(string $command): bool
    {
        return in_array($command, self::DESTRUCTIVE_COMMANDS);
    }

    /**
     * Execute command using Symfony Process
     */
    private function runCommand(string $command): JsonResponse
    {
        // Use output buffering to prevent any direct output from interfering with JSON response
        ob_start();

        try {
            $commandParts = explode(' ', $command);

            // Check if it's a composer command
            if (str_starts_with($command, 'composer:')) {
                // Convert composer:install to composer install
                $composerCommand = str_replace('composer:', '', $command);
                $process = new Process(array_merge(['composer'], explode(' ', $composerCommand)));
            } else {
                // Regular Artisan command
                $process = new Process(array_merge(['php', 'artisan'], $commandParts));
            }

            $process->setWorkingDirectory(base_path());
            $process->setTimeout(600); // 10 minutes timeout for composer commands

            $output = '';
            $process->run(function ($type, $buffer) use (&$output) {
                // $type indicates whether output is from stdout or stderr
                $output .= $buffer;
            });

            // Capture any direct output that might have leaked
            $directOutput = ob_get_clean();

            // Combine outputs if there's any direct output
            if (!empty(trim($directOutput))) {
                $output = trim($directOutput . "\n" . $output);
            }

            $success = $process->isSuccessful();

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Command executed successfully' : 'Command failed',
                'output' => $output,
                'exit_code' => $process->getExitCode()
            ]);
        } catch (\Exception $e) {
            // Clean output buffer in case of exception
            if (ob_get_level()) {
                ob_end_clean();
            }
            throw $e;
        }
    }

    /**
     * Get system information for debugging
     */
    public function systemInfo(): JsonResponse
    {
        $this->checkEnvironment();

        return response()->json([
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'environment' => config('app.env'),
            'debug_mode' => config('app.debug'),
            'database_connection' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'queue_driver' => config('queue.default'),
            'allowed_commands' => self::ALLOWED_COMMANDS,
            'destructive_commands' => self::DESTRUCTIVE_COMMANDS,
            'timestamp' => now()->toISOString()
        ]);
    }
}
