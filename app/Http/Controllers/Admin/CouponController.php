<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCouponRequest;
use App\Http\Requests\UpdateCouponRequest;
use App\Http\Requests\ValidateCouponRequest;
use App\Http\Requests\CouponStatsRequest;
use App\Models\Coupon;
use App\Services\CouponService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CouponController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(CouponService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Coupon data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCouponRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Coupon created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Coupon', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            return $this->successResponse($resource, 'Coupon retrieved successfully!', Response::HTTP_OK);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Coupon not found', 'Coupon not found', Response::HTTP_NOT_FOUND);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Coupon', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCouponRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'Coupon updated successfully!', Response::HTTP_OK);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Coupon not found', 'Coupon not found', Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update Coupon', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'Coupon deleted successfully!', Response::HTTP_OK);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Coupon not found', 'Coupon not found', Response::HTTP_NOT_FOUND);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete Coupon', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get coupons for a specific vendor
     */
    public function vendorCoupons(Request $request, int $vendorId): JsonResponse
    {
        try {
            $data = $this->service->getVendorCoupons($vendorId, $request);

            return $this->successResponse($data, 'Vendor coupons retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve vendor coupons', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get platform-wide coupons
     */
    public function platformCoupons(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getPlatformCoupons($request);

            return $this->successResponse($data, 'Platform coupons retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve platform coupons', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validate a coupon code
     */
    public function validateCode(ValidateCouponRequest $request): JsonResponse
    {
        try {
            $coupon = $this->service->validateCouponCode(
                $request->input('code'),
                $request->input('vendor_id')
            );

            if (!$coupon) {
                return $this->errorResponse('Invalid coupon code', 'Coupon not found or expired', Response::HTTP_NOT_FOUND);
            }

            return $this->successResponse($coupon, 'Coupon is valid!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to validate coupon', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get coupon statistics
     */
    public function stats(CouponStatsRequest $request): JsonResponse
    {
        try {
            $stats = $this->service->getCouponStats($request->input('vendor_id'));

            return $this->successResponse($stats, 'Coupon statistics retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve coupon statistics', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Toggle coupon status
     */
    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $resource = $this->service->toggleStatus($id);

            $status = $resource->is_active ? 'activated' : 'deactivated';
            return $this->successResponse($resource, "Coupon {$status} successfully!", Response::HTTP_OK);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Coupon not found', 'Coupon not found', Response::HTTP_NOT_FOUND);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to toggle coupon status', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
