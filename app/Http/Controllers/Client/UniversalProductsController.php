<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\UniversalProductsRequest;
use App\Services\UniversalProductService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class UniversalProductsController extends Controller
{
    use HelperTrait;

    private $universalProductService;

    public function __construct(UniversalProductService $universalProductService)
    {
        $this->universalProductService = $universalProductService;
    }

    /**
     * Get products with comprehensive filtering and pagination
     * Works for category pages, subcategory pages, brand pages, search pages
     * 
     * @param UniversalProductsRequest $request
     * @return JsonResponse
     */
    public function getProducts(UniversalProductsRequest $request): JsonResponse
    {
        try {
            $data = $this->universalProductService->getProducts($request);

            return $this->successResponse(
                $data,
                'Products retrieved successfully',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve products',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
