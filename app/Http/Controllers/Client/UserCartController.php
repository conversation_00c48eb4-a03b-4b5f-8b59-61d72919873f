<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cart\MigrateGuestCartRequest;
use App\Http\Resources\Cart\CartResource;
use App\Models\ShoppingCart;
use App\Services\CartService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UserCartController extends Controller
{
    use HelperTrait;

    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->middleware('auth:api');
        $this->cartService = $cartService;
    }

    /**
     * Get current user's cart.
     */
    public function getCurrentCart(): JsonResponse
    {
        try {
            $cart = $this->cartService->getOrCreateCart();

            return $this->successResponse(
                new CartResource($cart->load(['items.product', 'items.vendor', 'items.variant'])),
                'User cart retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Migrate guest cart to user account.
     */
    public function migrateGuestCart(MigrateGuestCartRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $migratedCart = $this->cartService->migrateGuestCart(
                $user,
                $request->guest_session_id,
                $request->getEffectiveMergeStrategy()
            );

            if (!$migratedCart) {
                return $this->errorResponse(
                    'No guest cart found to migrate',
                    'Migration failed',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->successResponse(
                new CartResource($migratedCart->load(['items.product', 'items.vendor', 'items.variant'])),
                'Guest cart migrated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to migrate guest cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user's cart history.
     */
    public function getCartHistory(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'limit' => 'nullable|integer|min:1|max:50',
                'status' => 'nullable|string|in:active,abandoned,converted,expired',
            ]);

            $query = ShoppingCart::where('user_id', auth()->id())
                ->with(['items.product', 'items.vendor'])
                ->orderBy('updated_at', 'desc');

            if ($request->status) {
                $query->where('status', $request->status);
            } else {
                $query->whereIn('status', ['abandoned', 'converted']);
            }

            $limit = $request->limit ?? 10;
            $carts = $query->limit($limit)->get();

            return $this->successResponse(
                CartResource::collection($carts),
                'Cart history retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve cart history',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Save items for later (move to wishlist).
     */
    public function saveForLater(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'cart_item_ids' => 'required|array|min:1',
                'cart_item_ids.*' => 'required|integer|exists:cart_items,id',
            ]);

            $user = auth()->user();
            $userCart = ShoppingCart::where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if (!$userCart) {
                return $this->errorResponse(
                    'No active cart found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $savedItems = [];
            foreach ($request->cart_item_ids as $itemId) {
                $cartItem = $userCart->items()->find($itemId);
                
                if ($cartItem) {
                    // Add to wishlist (assuming wishlist functionality exists)
                    $wishlistItem = [
                        'user_id' => $user->id,
                        'product_id' => $cartItem->product_id,
                        'variant_id' => $cartItem->variant_id,
                        'quantity' => $cartItem->quantity,
                        'notes' => 'Saved from cart',
                    ];

                    // Create wishlist entry (this would need the Wishlist model)
                    // Wishlist::create($wishlistItem);

                    // Remove from cart
                    $this->cartService->removeItem($cartItem);
                    $savedItems[] = $cartItem->product->name_en;
                }
            }

            return $this->successResponse(
                [
                    'saved_items_count' => count($savedItems),
                    'saved_items' => $savedItems,
                    'cart' => new CartResource($userCart->fresh()->load(['items.product', 'items.vendor', 'items.variant'])),
                ],
                'Items saved for later successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to save items for later',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get saved items (from wishlist).
     */
    public function getSavedItems(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'limit' => 'nullable|integer|min:1|max:50',
            ]);

            $user = auth()->user();
            $limit = $request->limit ?? 20;

            // This would fetch from wishlist table
            // For now, returning empty array as placeholder
            $savedItems = [];

            return $this->successResponse(
                $savedItems,
                'Saved items retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve saved items',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get cart statistics for user.
     */
    public function getCartStatistics(): JsonResponse
    {
        try {
            $user = auth()->user();
            
            $stats = [
                'total_carts_created' => ShoppingCart::where('user_id', $user->id)->count(),
                'abandoned_carts_count' => ShoppingCart::where('user_id', $user->id)
                    ->where('status', 'abandoned')->count(),
                'converted_carts_count' => ShoppingCart::where('user_id', $user->id)
                    ->where('status', 'converted')->count(),
                'current_cart_value' => 0,
                'average_cart_value' => 0,
                'total_items_in_current_cart' => 0,
            ];

            $currentCart = ShoppingCart::where('user_id', $user->id)
                ->where('status', 'active')
                ->first();

            if ($currentCart) {
                $stats['current_cart_value'] = $currentCart->total_amount;
                $stats['total_items_in_current_cart'] = $currentCart->items()->sum('quantity');
            }

            $avgCartValue = ShoppingCart::where('user_id', $user->id)
                ->where('status', 'converted')
                ->avg('total_amount');
            
            $stats['average_cart_value'] = round($avgCartValue ?? 0, 2);

            return $this->successResponse(
                $stats,
                'Cart statistics retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Clear user's current cart.
     */
    public function clearCurrentCart(): JsonResponse
    {
        try {
            $cart = ShoppingCart::where('user_id', auth()->id())
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                return $this->errorResponse(
                    'No active cart found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $this->cartService->clearCart($cart);

            return $this->successResponse(
                new CartResource($cart->fresh()),
                'Cart cleared successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to clear cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
