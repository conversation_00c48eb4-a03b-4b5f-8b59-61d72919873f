<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\FiltersRequest;
use App\Services\FiltersService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class FiltersController extends Controller
{
    use HelperTrait;

    private $filtersService;

    public function __construct(FiltersService $filtersService)
    {
        $this->filtersService = $filtersService;
    }

    /**
     * Get dynamic filters based on context parameters
     * 
     * @param FiltersRequest $request
     * @return JsonResponse
     */
    public function getFilters(FiltersRequest $request): JsonResponse
    {
        try {
            $data = $this->filtersService->getFilters($request);

            return $this->successResponse(
                $data,
                'Filters retrieved successfully',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve filters',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
