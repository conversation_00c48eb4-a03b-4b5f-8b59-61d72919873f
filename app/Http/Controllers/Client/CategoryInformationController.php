<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryInformationRequest;
use App\Services\CategoryInformationService;
use App\Traits\HelperTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class CategoryInformationController extends Controller
{
    use HelperTrait;

    private $categoryInformationService;

    public function __construct(CategoryInformationService $categoryInformationService)
    {
        $this->categoryInformationService = $categoryInformationService;
    }

    /**
     * Get category information by slug
     * 
     * @param string $categorySlug
     * @param CategoryInformationRequest $request
     * @return JsonResponse
     */
    public function getCategoryInformation(string $categorySlug, CategoryInformationRequest $request): JsonResponse
    {
        try {
            $data = $this->categoryInformationService->getCategoryBySlug($categorySlug);

            return $this->successResponse(
                $data,
                'Category information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Category not found',
                'The requested category could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve category information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get subcategory information by slug
     * 
     * @param string $subcategorySlug
     * @param CategoryInformationRequest $request
     * @return JsonResponse
     */
    public function getSubcategoryInformation(string $subcategorySlug, CategoryInformationRequest $request): JsonResponse
    {
        try {
            $data = $this->categoryInformationService->getSubcategoryBySlug($subcategorySlug);

            return $this->successResponse(
                $data,
                'Subcategory information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Subcategory not found',
                'The requested subcategory could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve subcategory information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get all active categories for navigation
     * 
     * @param CategoryInformationRequest $request
     * @return JsonResponse
     */
    public function getAllCategories(CategoryInformationRequest $request): JsonResponse
    {
        try {
            $data = $this->categoryInformationService->getAllActiveCategories();

            return $this->successResponse(
                ['categories' => $data],
                'Categories retrieved successfully',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve categories',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get subcategories by parent category ID
     * 
     * @param int $parentId
     * @param CategoryInformationRequest $request
     * @return JsonResponse
     */
    public function getSubcategoriesByParent(int $parentId, CategoryInformationRequest $request): JsonResponse
    {
        try {
            $data = $this->categoryInformationService->getSubcategoriesByParentId($parentId);

            return $this->successResponse(
                ['subcategories' => $data],
                'Subcategories retrieved successfully',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve subcategories',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
