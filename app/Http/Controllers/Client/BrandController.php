<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Services\BrandService;
use App\Traits\HelperTrait;
use Aws\Api\Service;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;

class BrandController extends Controller
{
    use HelperTrait;

    private  $service;
    public function __construct(BrandService $service)
    {
        $this->service = $service;
    }

    public function getGroupedBrands(Request $request)
    {

        try {
            $data = $this->service->getGroupedBrands($request);

            return $this->successResponse(
                $data,
                'Brand information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Brand not found',
                'The requested brand could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve brand information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function brandInformation(Request $request, $brand_slug)
    {
        try {
            $data = $this->service->getBrandBySlug($brand_slug);

            return $this->successResponse(
                $data,
                'Brand information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Brand not found',
                'The requested brand could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve brand information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
