<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\ProductClassService;
use App\Traits\HelperTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ClassInformationController extends Controller
{
    use HelperTrait;

    public function __construct(
        private  ProductClassService $classInformationService,

    ) {
        $this->classInformationService = $classInformationService;
    }

    public function getClassInformation(string $categorySlug): JsonResponse
    {
      
        try {
            $data = $this->classInformationService->getClassBySlug($categorySlug);
           

            return $this->successResponse(
                $data,
                'Class information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Class not found',
                'The requested class could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve class information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function getSubclassesInformation(string $categorySlug): JsonResponse
    {
        try {
            $data = $this->classInformationService->getSubclassesBySlug($categorySlug);

            return $this->successResponse(
                $data,
                'Subclasses information retrieved successfully',
                Response::HTTP_OK
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Subclasses not found',
                'The requested subclasses could not be found',
                Response::HTTP_NOT_FOUND
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Failed to retrieve subclasses information',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
