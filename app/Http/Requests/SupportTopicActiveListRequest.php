<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SupportTopicActiveListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'category_id' => 'nullable|exists:support_categories,id',
        ];
    }

    public function messages(): array
    {
        return [
            'category_id.exists' => 'The selected support category is invalid.',
        ];
    }
}
