<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CouponStatsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'vendor_id' => 'nullable|exists:vendors,id',
        ];
    }

    public function messages(): array
    {
        return [
            'vendor_id.exists' => 'Selected vendor does not exist.',
        ];
    }
}
