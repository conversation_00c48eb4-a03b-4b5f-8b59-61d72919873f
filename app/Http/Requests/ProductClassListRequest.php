<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductClassListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust as needed (e.g., check for user roles/permissions)
    }

    public function rules(): array
    {
        return [
            'category_id' => 'nullable|exists:categories,id', // Adjust based on your category table and validation rules
            'sub_category_id' => 'nullable|exists:categories,id', // Adjust based on your sub-category table and validation rules
        ];
    }

    public function messages(): array
    {
        return [
            'category_id.exists' => 'The selected category is invalid.',
            'sub_category_id.exists' => 'The selected sub-category is invalid.',
        ];
    }
}
