<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSupportTopicRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'category_id' => 'required|exists:support_categories,id',
            'name_en' => 'required|string|max:255|unique:support_topics,name_en,' . $this->route('support_topic'),
            'name_ar' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
        ];
    }
}
