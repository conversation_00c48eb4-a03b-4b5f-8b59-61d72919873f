<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    
    public function authorize(): bool
    {
        return true;
    }
            

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'stock' => 'nullable|integer|min:0',
            'reserved' => 'nullable|integer|min:0',
            'threshold' => 'nullable|integer|min:0',
            'location' => 'nullable|string|max:255',
            'note' => 'nullable|string',
            'is_active' => 'nullable|in:0,1',
            'stock_status' => 'nullable|in:in_stock,out_of_stock,low_stock',
        ];
    }
}
