<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBlogCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title_en' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:blog_categories,id',
            'slug' => 'required|string|unique:blog_categories,slug|max:255',
            'status' => 'required|in:active,inactive',
        ];
    }
}
