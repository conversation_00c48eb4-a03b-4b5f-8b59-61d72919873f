<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCouponRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                'max:50',
                'regex:/^[A-Z0-9_-]+$/',
                Rule::unique('coupons', 'code')
            ],
            'title_en' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'description_en' => 'nullable|string|max:1000',
            'description_ar' => 'nullable|string|max:1000',
            'type' => 'required|in:percentage,fixed',
            'value' => [
                'required',
                'numeric',
                'min:0',
                function ($attribute, $value, $fail) {
                    if ($this->input('type') === 'percentage' && $value > 100) {
                        $fail('The percentage value cannot exceed 100%.');
                    }
                    if ($this->input('type') === 'fixed' && $value > 10000) {
                        $fail('The fixed discount value cannot exceed 10,000.');
                    }
                },
            ],
            'min_order_value' => 'nullable|numeric|min:0|max:100000',
            'usage_limit' => 'nullable|integer|min:1|max:1000000',
            'per_user_limit' => 'nullable|integer|min:1|max:1000',
            'vendor_id' => 'nullable|exists:vendors,id',
            'start_date' => 'nullable|date|after_or_equal:today',
            'end_date' => [
                'nullable',
                'date',
                'after:start_date',
                function ($attribute, $value, $fail) {
                    if ($value && $this->input('start_date')) {
                        $startDate = \Carbon\Carbon::parse($this->input('start_date'));
                        $endDate = \Carbon\Carbon::parse($value);
                        
                        if ($endDate->diffInDays($startDate) > 365) {
                            $fail('The coupon validity period cannot exceed 365 days.');
                        }
                    }
                },
            ],
            'is_active' => 'boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Coupon code is required.',
            'code.unique' => 'This coupon code already exists.',
            'code.regex' => 'Coupon code can only contain uppercase letters, numbers, hyphens, and underscores.',
            'title_en.required' => 'Coupon title is required.',
            'type.required' => 'Discount type is required.',
            'type.in' => 'Discount type must be either percentage or fixed amount.',
            'value.required' => 'Discount value is required.',
            'value.min' => 'Discount value must be greater than 0.',
            'min_order_value.min' => 'Minimum order value cannot be negative.',
            'usage_limit.min' => 'Usage limit must be at least 1.',
            'per_user_limit.min' => 'Per user limit must be at least 1.',
            'start_date.after_or_equal' => 'Start date cannot be in the past.',
            'end_date.after' => 'End date must be after start date.',
            'vendor_id.exists' => 'Selected vendor does not exist.',
        ];
    }

    protected function prepareForValidation()
    {
        // Convert code to uppercase
        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper($this->input('code'))
            ]);
        }

        // Convert string boolean values to actual booleans (for multipart/form-data)
        if ($this->has('is_active')) {
            $isActive = $this->input('is_active');
            if (is_string($isActive)) {
                $this->merge([
                    'is_active' => filter_var($isActive, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)
                ]);
            }
        } else {
            // Set default value if not provided
            $this->merge(['is_active' => true]);
        }
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional business logic validation
            
            // Check if per_user_limit is not greater than usage_limit
            if ($this->filled('usage_limit') && $this->filled('per_user_limit')) {
                if ($this->input('per_user_limit') > $this->input('usage_limit')) {
                    $validator->errors()->add('per_user_limit', 'Per user limit cannot exceed total usage limit.');
                }
            }

            // Validate minimum order value for percentage discounts
            if ($this->input('type') === 'percentage' && $this->filled('min_order_value')) {
                $minOrder = $this->input('min_order_value');
                $discountValue = $this->input('value');
                
                if ($minOrder > 0 && $discountValue > 0) {
                    $maxDiscount = ($minOrder * $discountValue) / 100;
                    if ($maxDiscount > $minOrder) {
                        $validator->errors()->add('value', 'Percentage discount would exceed minimum order value.');
                    }
                }
            }

            // Validate fixed discount against minimum order value
            if ($this->input('type') === 'fixed' && $this->filled('min_order_value')) {
                if ($this->input('value') > $this->input('min_order_value')) {
                    $validator->errors()->add('value', 'Fixed discount cannot exceed minimum order value.');
                }
            }
        });
    }
}
