<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBlogCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title_en' => 'sometimes|required|string|max:255',
            'title_ar' => 'sometimes|nullable|string|max:255',
            'parent_id' => 'sometimes|nullable|exists:blog_categories,id',
            'slug' => 'sometimes|required|string|max:255|unique:blog_categories,slug,' . $this->route('blog_category'),
            'status' => 'sometimes|required|in:active,inactive',
        ];
    }
}
