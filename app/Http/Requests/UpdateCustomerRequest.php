<?php

namespace App\Http\Requests;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = Customer::findOrFail($this->route('customer'))->user;
        if (!$user) {
            abort(404, 'User not found');
        }

        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:15|unique:users,phone,' . $user->id,
            'avatar' => 'nullable|string',
            'status' => 'required|in:pending,active,inactive,banned',
            'is_active' => 'nullable|in:0,1',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date',
            'loyalty_points' => 'nullable|integer|min:0',
            'customer_type' => 'nullable|in:retail,wholesale',
            'preferred_language' => 'nullable|in:en,ar,fr,de,es',
            'preferred_currency' => 'nullable|in:AED,USD,EUR,GBP',
            'password' => 'nullable|string|min:8|confirmed',
            'kyc_document_type' => 'nullable|in:passport,emirates_id,driving_license,national_id,student_id',
            'kyc_file' => 'nullable|string',
            'kyc_verified' => 'nullable|boolean',
            'referral_code' => 'nullable|string|max:255',
            'referred_by' => 'nullable|string|max:255',
            'loyalty_points_awarded' => 'nullable|boolean',
            'occupation' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'newsletter_consent' => 'nullable|boolean',
            'is_vrps' => 'nullable|boolean',
        ];
    }
}
