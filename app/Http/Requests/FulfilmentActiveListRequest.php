<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FulfilmentActiveListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // No specific parameters required for basic active list
            // Can be extended later if filtering parameters are needed
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // No custom messages needed for basic active list
        ];
    }
}
