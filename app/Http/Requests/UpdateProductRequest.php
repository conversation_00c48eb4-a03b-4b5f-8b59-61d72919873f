<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Ownership & Categorization
            'vendor_id'         => 'nullable|exists:vendors,id',
            'category_id'       => 'sometimes|required|exists:categories,id',
            'sub_category_id'   => 'nullable|exists:categories,id',
            'class_id'          => 'nullable|exists:product_classes,id',
            'sub_class_id'      => 'nullable|exists:product_classes,id',
            'brand_id'          => 'nullable|exists:brands,id',

            // Product Identity & Basic Info
            'vendor_sku'        => 'nullable|string|max:255|unique:products,vendor_sku,' . request()->route('product'),
            'system_sku'        => 'nullable|string|max:255|unique:products,system_sku,' . request()->route('product'),
            'barcode'           => 'nullable|string|max:255',
            'model_number'      => 'nullable|string|max:255',

            // Multi-language Titles & Descriptions
            'title_en'              => 'sometimes|required|string|max:255',
            'title_ar'              => 'nullable|string|max:255',
            'short_name'            => 'nullable|string|max:255',
            'short_description_en'  => 'nullable|string',
            'short_description_ar'  => 'nullable|string',
            'description_en'        => 'nullable|string',
            'description_ar'        => 'nullable|string',

            // Product Details
            'key_ingredients'       => 'nullable|string',
            'supplement_image'      => 'nullable|string|max:255',
            'usage_instruction_en'    => 'nullable|string',
            'usage_instruction_ar'    => 'nullable|string',
            'user_group_id'         => 'nullable|integer',
            'net_weight'         => 'nullable|integer',
            'net_weight_unit_id'    => 'nullable|integer',
            'formulation_id'        => 'nullable|integer',
            'servings'              => 'nullable|integer|min:0',
            'flavour_id'            => 'nullable|integer',
            'is_variant'            => 'nullable|boolean',

            // Pricing & Promotion
            'regular_price'         => 'sometimes|required|numeric|min:0|max:999999.99',
            'offer_price'           => 'nullable|numeric|min:0|max:999999.99',
            'vat_tax'               => 'nullable|string|max:255',
            'discount_start_date'   => 'nullable|date',
            'discount_end_date'     => 'nullable|date|after_or_equal:discount_start_date',
            'approx_commission'     => 'nullable|numeric|min:0|max:999999.99',

            // Compliance & Legal
            'dietary_need_ids'              => 'nullable|array',
            'is_vegan'                      => 'nullable|boolean',
            'is_vegetarian'                 => 'nullable|boolean',
            'is_halal'                      => 'nullable|boolean',
            'allergen_info_ids'            => 'nullable|array',
            'storage_conditions'           => 'nullable|integer|exists:dropdown_options,id',
            'vat_tax_utl'                  => 'nullable|string|max:255',
            'regulatory_product_registration' => 'nullable|string|max:255',
            'country_of_origin'            => 'nullable|integer|exists:dropdown_options,id',
            'is_returnable'                => 'nullable|integer|exists:dropdown_options,id',
            'warranty'                     => 'nullable|integer|exists:dropdown_options,id',
            'bbe_date'                     => 'nullable|date',
            'fulfillment_id'       => 'nullable|integer',
            // Packaging
            'package_length'      => 'nullable|numeric|min:0|max:9999.99',
            'package_width'       => 'nullable|numeric|min:0|max:9999.99',
            'package_height'      => 'nullable|numeric|min:0|max:9999.99',
            'package_weight'      => 'nullable|numeric|min:0|max:9999.99',

            // Status & Control
            'is_active'    => 'nullable|boolean',
            'is_approved'  => 'nullable|boolean',
            'status'       => 'nullable|in:draft,pending,submitted',
            'variant_setup' => 'nullable|array',

            // product seo
            'product_seo' => 'sometimes|nullable|array',
            'product_seo.meta_title_en' => 'nullable|string|max:255',
            'product_seo.meta_description_en' => 'nullable|string|max:255',
            'product_seo.keywords_en' => 'nullable|string|max:255',
            'product_seo.meta_title_ar' => 'nullable|string|max:255',
            'product_seo.meta_description_ar' => 'nullable|string|max:255',
            'product_seo.keywords_ar' => 'nullable|string|max:255',

            // product faqs
            'product_faqs' => 'sometimes|nullable|array',
            'product_faqs.*.question' => 'required|string|max:255',
            'product_faqs.*.answer' => 'required|string',

            //product variants
            'product_variants' => 'sometimes|nullable|array',
            'product_variants.*.regular_price' => 'required|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.offer_price' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.vat_tax' => 'nullable|string',
            'product_variants.*.discount_start_date' => 'nullable|date',
            'product_variants.*.discount_end_date' => 'nullable|date|after:discount_start_date',
            'product_variants.*.stock' => 'nullable|integer|min:0',
            'product_variants.*.reserved' => 'nullable|integer|min:0',
            'product_variants.*.threshold' => 'nullable|integer|min:0',
            'product_variants.*.location' => 'nullable|string|max:255',
            'product_variants.*.note' => 'nullable|string',
            'product_variants.*.sku' => 'nullable|string|max:255',
            'product_variants.*.barcode' => 'nullable|string|max:255',
            'product_variants.*.weight' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.length' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.width' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.height' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.path' => 'nullable|string|max:255',
            'product_variants.*.is_active' => 'nullable|in:0,1',
            'product_variants.*.stock_status' => 'nullable|in:in_stock,out_of_stock,low_stock',
            'product_variants.*.attribute_id' => 'nullable|integer|exists:product_attributes,id',
            'product_variants.*.attribute_value_id' => 'nullable|integer|exists:product_attribute_values,id',

        ];
    }

    /**
     * Prepare the data for validation.
     */
    public function withValidator($validator): void
    {
        $validator->after(function (Validator $validator) {
            $isVariant = $this->boolean('is_variant');

            if ($isVariant) {
                $productVariants = $this->input('product_variants');

                if (empty($productVariants) || !is_array($productVariants) || count($productVariants) === 0) {
                    $validator->errors()->add('product_variants', 'The product_variants field is required when is_variant is true and must contain at least one item.');
                }
            }
        });
    }
}
