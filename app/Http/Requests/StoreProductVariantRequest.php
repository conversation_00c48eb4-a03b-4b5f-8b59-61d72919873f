<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    
    public function authorize(): bool
    {
        return true;
    }
            

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|exists:products,id',
            'attribute_id'=> 'required|exists:product_attributes,id',
            'attribute_value_id' => 'required|exists:product_attribute_values,id',
            'path' => 'nullable|string|max:255',
            'regular_price' => 'required|decimal:0,2|min:0|max:999999.99',
            'offer_price' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'vat_tax' => 'nullable|in:exempted,standard_5,zero_rated',
            'discount_start_date' => 'nullable|date',
            'discount_end_date' => 'nullable|date|after:discount_start_date',
            'stock' => 'nullable|integer|min:0',
            'sku' => 'nullable|string|max:255',
            'system_sku' => 'nullable|string|max:255',
            'barcode' => 'nullable|string|max:255',
            'weight' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'length' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'width' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'height' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'is_active' => 'nullable|boolean'
        ];
    }
}
