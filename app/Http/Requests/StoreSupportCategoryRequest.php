<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupportCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name_en' => 'required|string|max:255|unique:support_categories,name_en',
            'name_ar' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
        ];
    }
}
