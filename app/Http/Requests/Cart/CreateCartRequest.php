<?php

namespace App\Http\Requests\Cart;

use Illuminate\Foundation\Http\FormRequest;

class CreateCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'session_id' => 'nullable|string|max:255',
            'currency' => 'nullable|string|size:3|in:AED,USD,EUR,GBP',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'currency.size' => 'Currency must be exactly 3 characters.',
            'currency.in' => 'Currency must be one of: AED, USD, EUR, GBP.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'metadata.*.max' => 'Each metadata value cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'session_id' => 'session identifier',
            'currency' => 'currency code',
            'notes' => 'cart notes',
            'metadata' => 'cart metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'currency' => $this->currency ?? 'AED',
            'session_id' => $this->session_id ?? session()->getId(),
        ]);
    }
}
