<?php

namespace App\Http\Requests\Cart;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Vendor;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddToCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'product_id' => [
                'required',
                'integer',
                Rule::exists('products', 'id')->where(function ($query) {
                    $query->where('status', 'active');
                }),
            ],
            'variant_id' => [
                'nullable',
                'integer',
                Rule::exists('product_variants', 'id')->where(function ($query) {
                    $query->where('product_id', $this->product_id)
                          ->where('status', 'active');
                }),
            ],
            'quantity' => [
                'required',
                'integer',
                'min:1',
                'max:999',
                function ($attribute, $value, $fail) {
                    $this->validateQuantityConstraints($value, $fail);
                },
            ],
            'customizations' => 'nullable|array',
            'customizations.*' => 'nullable|string|max:255',
            'special_instructions' => 'nullable|string|max:500',
            'metadata' => 'nullable|array',
            'metadata.*' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product is required.',
            'product_id.exists' => 'Selected product is not available.',
            'variant_id.exists' => 'Selected product variant is not available.',
            'quantity.required' => 'Quantity is required.',
            'quantity.min' => 'Quantity must be at least 1.',
            'quantity.max' => 'Quantity cannot exceed 999.',
            'special_instructions.max' => 'Special instructions cannot exceed 500 characters.',
            'customizations.*.max' => 'Each customization cannot exceed 255 characters.',
            'metadata.*.max' => 'Each metadata value cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'variant_id' => 'product variant',
            'quantity' => 'quantity',
            'customizations' => 'customizations',
            'special_instructions' => 'special instructions',
            'metadata' => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure quantity is an integer
        if ($this->has('quantity')) {
            $this->merge([
                'quantity' => (int) $this->quantity,
            ]);
        }

        // Clean up variant_id if it's empty
        if ($this->variant_id === '' || $this->variant_id === '0') {
            $this->merge(['variant_id' => null]);
        }
    }

    /**
     * Validate quantity constraints based on product settings.
     */
    protected function validateQuantityConstraints(int $quantity, $fail): void
    {
        $product = Product::find($this->product_id);
        
        if (!$product) {
            $fail('Product not found.');
            return;
        }

        // Check minimum quantity
        if ($product->min_cart_quantity && $quantity < $product->min_cart_quantity) {
            $fail("Minimum quantity for this product is {$product->min_cart_quantity}.");
            return;
        }

        // Check maximum quantity
        if ($product->max_cart_quantity && $quantity > $product->max_cart_quantity) {
            $fail("Maximum quantity for this product is {$product->max_cart_quantity}.");
            return;
        }

        // Check quantity increment
        if ($product->cart_increment && $product->cart_increment > 1) {
            if ($quantity % $product->cart_increment !== 0) {
                $fail("Quantity must be in increments of {$product->cart_increment}.");
                return;
            }
        }

        // Check stock availability
        $this->validateStockAvailability($product, $quantity, $fail);
    }

    /**
     * Validate stock availability.
     */
    protected function validateStockAvailability(Product $product, int $quantity, $fail): void
    {
        if ($this->variant_id) {
            $variant = ProductVariant::find($this->variant_id);
            if (!$variant) {
                $fail('Product variant not found.');
                return;
            }

            $availableStock = $variant->stock_quantity ?? 0;
            $productName = $variant->name ?? $product->name_en;
        } else {
            $availableStock = $product->stock_quantity ?? 0;
            $productName = $product->name_en;
        }

        // Check if product allows backorders
        if (!$product->allow_backorder && $quantity > $availableStock) {
            if ($availableStock === 0) {
                $fail("{$productName} is currently out of stock.");
            } else {
                $fail("Only {$availableStock} units of {$productName} are available.");
            }
        }
    }

    /**
     * Get the validated data with additional computed fields.
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // Get product and vendor information
        $product = Product::with('vendor')->find($validated['product_id']);
        
        if ($product) {
            $validated['vendor_id'] = $product->vendor_id;
            
            // Get pricing information
            if ($validated['variant_id'] ?? null) {
                $variant = ProductVariant::find($validated['variant_id']);
                $validated['unit_price'] = $variant->price ?? $product->price;
                $validated['base_price'] = $variant->original_price ?? $product->original_price ?? $variant->price ?? $product->price;
            } else {
                $validated['unit_price'] = $product->price;
                $validated['base_price'] = $product->original_price ?? $product->price;
            }
            
            $validated['total_price'] = $validated['unit_price'] * $validated['quantity'];
        }
        
        return $validated;
    }
}
