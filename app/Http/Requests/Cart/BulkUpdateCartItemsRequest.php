<?php

namespace App\Http\Requests\Cart;

use App\Models\CartItem;
use App\Models\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;

class BulkUpdateCartItemsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user owns the cart or it's a guest session
        $cart = $this->route('cart') ?? ShoppingCart::where('uuid', $this->route('cartId'))->first();
        
        if (!$cart) {
            return false;
        }

        // If user is authenticated, check ownership
        if (auth()->check()) {
            return $cart->user_id === auth()->id();
        }
        
        // For guest users, check session
        return $cart->session_id === session()->getId();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'items' => 'required|array|min:1|max:50',
            'items.*.id' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) {
                    $this->validateCartItemOwnership($value, $fail);
                },
            ],
            'items.*.quantity' => 'required|integer|min:0|max:999',
            'items.*.customizations' => 'nullable|array',
            'items.*.customizations.*' => 'nullable|string|max:255',
            'items.*.special_instructions' => 'nullable|string|max:500',
            'items.*.metadata' => 'nullable|array',
            'items.*.metadata.*' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'items.required' => 'Items array is required.',
            'items.array' => 'Items must be an array.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Cannot update more than 50 items at once.',
            'items.*.id.required' => 'Item ID is required.',
            'items.*.id.integer' => 'Item ID must be a number.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.integer' => 'Quantity must be a number.',
            'items.*.quantity.min' => 'Quantity cannot be negative.',
            'items.*.quantity.max' => 'Quantity cannot exceed 999.',
            'items.*.special_instructions.max' => 'Special instructions cannot exceed 500 characters.',
            'items.*.customizations.*.max' => 'Each customization cannot exceed 255 characters.',
            'items.*.metadata.*.max' => 'Each metadata value cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'items' => 'items',
            'items.*.id' => 'item ID',
            'items.*.quantity' => 'quantity',
            'items.*.customizations' => 'customizations',
            'items.*.special_instructions' => 'special instructions',
            'items.*.metadata' => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $items = $this->items ?? [];
        
        // Ensure quantities are integers and clean up data
        foreach ($items as $index => $item) {
            if (isset($item['quantity'])) {
                $items[$index]['quantity'] = (int) $item['quantity'];
            }
            
            if (isset($item['id'])) {
                $items[$index]['id'] = (int) $item['id'];
            }
        }
        
        $this->merge(['items' => $items]);
    }

    /**
     * Validate that the cart item belongs to the current cart.
     */
    protected function validateCartItemOwnership(int $itemId, $fail): void
    {
        $cart = $this->route('cart') ?? ShoppingCart::where('uuid', $this->route('cartId'))->first();
        
        if (!$cart) {
            $fail('Cart not found.');
            return;
        }

        $cartItem = CartItem::where('id', $itemId)
            ->where('cart_id', $cart->id)
            ->first();
            
        if (!$cartItem) {
            $fail("Item with ID {$itemId} does not belong to this cart.");
        }
    }

    /**
     * Get additional validation rules after initial validation.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateQuantityConstraints($validator);
            $this->validateStockAvailability($validator);
        });
    }

    /**
     * Validate quantity constraints for all items.
     */
    protected function validateQuantityConstraints($validator): void
    {
        $items = $this->items ?? [];
        
        foreach ($items as $index => $itemData) {
            $cartItem = CartItem::find($itemData['id']);
            
            if (!$cartItem || !$cartItem->product) {
                continue;
            }

            $product = $cartItem->product;
            $quantity = $itemData['quantity'];

            // Skip validation for items being removed
            if ($quantity === 0) {
                continue;
            }

            // Check minimum quantity
            if ($product->min_cart_quantity && $quantity < $product->min_cart_quantity) {
                $validator->errors()->add(
                    "items.{$index}.quantity",
                    "Minimum quantity for {$product->name_en} is {$product->min_cart_quantity}."
                );
            }

            // Check maximum quantity
            if ($product->max_cart_quantity && $quantity > $product->max_cart_quantity) {
                $validator->errors()->add(
                    "items.{$index}.quantity",
                    "Maximum quantity for {$product->name_en} is {$product->max_cart_quantity}."
                );
            }

            // Check quantity increment
            if ($product->cart_increment && $product->cart_increment > 1) {
                if ($quantity % $product->cart_increment !== 0) {
                    $validator->errors()->add(
                        "items.{$index}.quantity",
                        "Quantity for {$product->name_en} must be in increments of {$product->cart_increment}."
                    );
                }
            }
        }
    }

    /**
     * Validate stock availability for all items.
     */
    protected function validateStockAvailability($validator): void
    {
        $items = $this->items ?? [];
        
        foreach ($items as $index => $itemData) {
            $cartItem = CartItem::find($itemData['id']);
            
            if (!$cartItem || !$cartItem->product) {
                continue;
            }

            $product = $cartItem->product;
            $quantity = $itemData['quantity'];

            // Skip validation for items being removed
            if ($quantity === 0) {
                continue;
            }

            // Get available stock
            if ($cartItem->variant_id) {
                $variant = $cartItem->variant;
                $availableStock = $variant ? $variant->stock_quantity : 0;
                $productName = $variant ? $variant->name : $product->name_en;
            } else {
                $availableStock = $product->stock_quantity ?? 0;
                $productName = $product->name_en;
            }

            // Check if product allows backorders
            if (!$product->allow_backorder && $quantity > $availableStock) {
                if ($availableStock === 0) {
                    $validator->errors()->add(
                        "items.{$index}.quantity",
                        "{$productName} is currently out of stock."
                    );
                } else {
                    $validator->errors()->add(
                        "items.{$index}.quantity",
                        "Only {$availableStock} units of {$productName} are available."
                    );
                }
            }
        }
    }

    /**
     * Get items to be deleted (quantity = 0).
     */
    public function getItemsToDelete(): array
    {
        return array_filter($this->items ?? [], function ($item) {
            return $item['quantity'] === 0;
        });
    }

    /**
     * Get items to be updated (quantity > 0).
     */
    public function getItemsToUpdate(): array
    {
        return array_filter($this->items ?? [], function ($item) {
            return $item['quantity'] > 0;
        });
    }
}
