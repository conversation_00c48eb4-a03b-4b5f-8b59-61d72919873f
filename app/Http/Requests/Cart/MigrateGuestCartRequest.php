<?php

namespace App\Http\Requests\Cart;

use App\Models\CartSession;
use App\Models\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;

class MigrateGuestCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can migrate guest carts
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'guest_session_id' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $this->validateGuestSession($value, $fail);
                },
            ],
            'merge_strategy' => 'nullable|string|in:merge,replace,keep_both',
            'clear_guest_cart' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'guest_session_id.required' => 'Guest session ID is required.',
            'guest_session_id.string' => 'Guest session ID must be a string.',
            'guest_session_id.max' => 'Guest session ID cannot exceed 255 characters.',
            'merge_strategy.in' => 'Merge strategy must be one of: merge, replace, keep_both.',
            'clear_guest_cart.boolean' => 'Clear guest cart must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'guest_session_id' => 'guest session ID',
            'merge_strategy' => 'merge strategy',
            'clear_guest_cart' => 'clear guest cart flag',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'merge_strategy' => $this->merge_strategy ?? 'merge',
            'clear_guest_cart' => $this->clear_guest_cart ?? true,
        ]);
    }

    /**
     * Validate that the guest session exists and is valid.
     */
    protected function validateGuestSession(string $sessionId, $fail): void
    {
        // Check if session exists in cart_sessions table
        $cartSession = CartSession::where('session_id', $sessionId)
            ->where('is_migrated', false)
            ->first();

        if ($cartSession) {
            // Check if session is expired
            if ($cartSession->is_expired) {
                $fail('Guest cart session has expired.');
                return;
            }

            // Check if session has items
            if ($cartSession->items_count === 0) {
                $fail('Guest cart is empty.');
                return;
            }

            return;
        }

        // Check if there's an active shopping cart with this session
        $shoppingCart = ShoppingCart::where('session_id', $sessionId)
            ->where('status', 'active')
            ->whereNull('user_id')
            ->first();

        if (!$shoppingCart) {
            $fail('Guest cart session not found or already migrated.');
            return;
        }

        // Check if cart has items
        if ($shoppingCart->isEmpty()) {
            $fail('Guest cart is empty.');
            return;
        }

        // Check if cart is expired
        if ($shoppingCart->hasExpired()) {
            $fail('Guest cart has expired.');
        }
    }

    /**
     * Get the guest cart session.
     */
    public function getGuestCartSession(): ?CartSession
    {
        return CartSession::where('session_id', $this->guest_session_id)
            ->where('is_migrated', false)
            ->first();
    }

    /**
     * Get the guest shopping cart.
     */
    public function getGuestShoppingCart(): ?ShoppingCart
    {
        return ShoppingCart::where('session_id', $this->guest_session_id)
            ->where('status', 'active')
            ->whereNull('user_id')
            ->first();
    }

    /**
     * Get the user's existing cart.
     */
    public function getUserCart(): ?ShoppingCart
    {
        return ShoppingCart::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();
    }

    /**
     * Determine if the user has an existing cart.
     */
    public function userHasExistingCart(): bool
    {
        return $this->getUserCart() !== null;
    }

    /**
     * Get the effective merge strategy based on user's existing cart.
     */
    public function getEffectiveMergeStrategy(): string
    {
        // If user doesn't have an existing cart, always use 'replace'
        if (!$this->userHasExistingCart()) {
            return 'replace';
        }

        return $this->merge_strategy;
    }

    /**
     * Validate the migration is possible.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateMigrationPossibility($validator);
        });
    }

    /**
     * Validate that the migration can be performed.
     */
    protected function validateMigrationPossibility($validator): void
    {
        $guestCart = $this->getGuestShoppingCart();
        $guestSession = $this->getGuestCartSession();
        
        if (!$guestCart && !$guestSession) {
            $validator->errors()->add('guest_session_id', 'No valid guest cart found for migration.');
            return;
        }

        // Check for conflicts if merge strategy is 'merge'
        if ($this->merge_strategy === 'merge' && $this->userHasExistingCart()) {
            $userCart = $this->getUserCart();
            
            // Get guest cart items
            $guestItems = [];
            if ($guestCart) {
                $guestItems = $guestCart->items->map(function ($item) {
                    return [
                        'product_id' => $item->product_id,
                        'variant_id' => $item->variant_id,
                    ];
                })->toArray();
            } elseif ($guestSession) {
                $guestItems = collect($guestSession->cart_data['items'] ?? [])->map(function ($item) {
                    return [
                        'product_id' => $item['product_id'],
                        'variant_id' => $item['variant_id'] ?? null,
                    ];
                })->toArray();
            }

            // Check for potential conflicts
            foreach ($guestItems as $guestItem) {
                $existingItem = $userCart->items()
                    ->where('product_id', $guestItem['product_id'])
                    ->where('variant_id', $guestItem['variant_id'])
                    ->first();

                if ($existingItem) {
                    // This is just a warning, not an error
                    // The merge will handle quantity updates
                    continue;
                }
            }
        }
    }
}
