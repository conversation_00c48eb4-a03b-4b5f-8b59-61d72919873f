<?php

namespace App\Http\Requests\Cart;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCartItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user owns the cart item or it's a guest session
        $cartItem = $this->route('cartItem') ?? CartItem::find($this->route('itemId'));
        
        if (!$cartItem) {
            return false;
        }

        $cart = $cartItem->cart;
        
        // If user is authenticated, check ownership
        if (auth()->check()) {
            return $cart->user_id === auth()->id();
        }
        
        // For guest users, check session
        return $cart->session_id === session()->getId();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'quantity' => [
                'required',
                'integer',
                'min:0', // Allow 0 to remove item
                'max:999',
                function ($attribute, $value, $fail) {
                    if ($value > 0) {
                        $this->validateQuantityConstraints($value, $fail);
                    }
                },
            ],
            'customizations' => 'nullable|array',
            'customizations.*' => 'nullable|string|max:255',
            'special_instructions' => 'nullable|string|max:500',
            'metadata' => 'nullable|array',
            'metadata.*' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'quantity.required' => 'Quantity is required.',
            'quantity.integer' => 'Quantity must be a number.',
            'quantity.min' => 'Quantity cannot be negative.',
            'quantity.max' => 'Quantity cannot exceed 999.',
            'special_instructions.max' => 'Special instructions cannot exceed 500 characters.',
            'customizations.*.max' => 'Each customization cannot exceed 255 characters.',
            'metadata.*.max' => 'Each metadata value cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'quantity' => 'quantity',
            'customizations' => 'customizations',
            'special_instructions' => 'special instructions',
            'metadata' => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure quantity is an integer
        if ($this->has('quantity')) {
            $this->merge([
                'quantity' => (int) $this->quantity,
            ]);
        }
    }

    /**
     * Validate quantity constraints based on product settings.
     */
    protected function validateQuantityConstraints(int $quantity, $fail): void
    {
        $cartItem = $this->route('cartItem') ?? CartItem::find($this->route('itemId'));
        
        if (!$cartItem) {
            $fail('Cart item not found.');
            return;
        }

        $product = $cartItem->product;
        
        if (!$product) {
            $fail('Product not found.');
            return;
        }

        // Check minimum quantity
        if ($product->min_cart_quantity && $quantity < $product->min_cart_quantity) {
            $fail("Minimum quantity for this product is {$product->min_cart_quantity}.");
            return;
        }

        // Check maximum quantity
        if ($product->max_cart_quantity && $quantity > $product->max_cart_quantity) {
            $fail("Maximum quantity for this product is {$product->max_cart_quantity}.");
            return;
        }

        // Check quantity increment
        if ($product->cart_increment && $product->cart_increment > 1) {
            if ($quantity % $product->cart_increment !== 0) {
                $fail("Quantity must be in increments of {$product->cart_increment}.");
                return;
            }
        }

        // Check stock availability
        $this->validateStockAvailability($cartItem, $quantity, $fail);
    }

    /**
     * Validate stock availability.
     */
    protected function validateStockAvailability(CartItem $cartItem, int $quantity, $fail): void
    {
        $product = $cartItem->product;
        
        if ($cartItem->variant_id) {
            $variant = $cartItem->variant;
            if (!$variant) {
                $fail('Product variant not found.');
                return;
            }

            $availableStock = $variant->stock_quantity ?? 0;
            $productName = $variant->name ?? $product->name_en;
        } else {
            $availableStock = $product->stock_quantity ?? 0;
            $productName = $product->name_en;
        }

        // Get current quantity in other carts (excluding this item)
        $otherCartQuantity = CartItem::where('product_id', $cartItem->product_id)
            ->where('variant_id', $cartItem->variant_id)
            ->where('id', '!=', $cartItem->id)
            ->whereHas('cart', function ($query) {
                $query->where('status', 'active');
            })
            ->sum('quantity');

        $totalAvailable = $availableStock - $otherCartQuantity;

        // Check if product allows backorders
        if (!$product->allow_backorder && $quantity > $totalAvailable) {
            if ($totalAvailable <= 0) {
                $fail("{$productName} is currently out of stock.");
            } else {
                $fail("Only {$totalAvailable} units of {$productName} are available.");
            }
        }
    }

    /**
     * Get the validated data with additional computed fields.
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // If quantity is 0, mark for deletion
        if ($validated['quantity'] === 0) {
            $validated['_delete'] = true;
        }
        
        return $validated;
    }

    /**
     * Determine if the item should be deleted based on quantity.
     */
    public function shouldDeleteItem(): bool
    {
        return $this->quantity === 0;
    }
}
