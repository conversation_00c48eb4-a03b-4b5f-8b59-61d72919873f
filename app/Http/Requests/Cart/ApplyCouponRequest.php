<?php

namespace App\Http\Requests\Cart;

use App\Models\Coupon;
use App\Models\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ApplyCouponRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user owns the cart or it's a guest session
        $cart = $this->route('cart') ?? ShoppingCart::where('uuid', $this->route('cartId'))->first();
        
        if (!$cart) {
            return false;
        }

        // If user is authenticated, check ownership
        if (auth()->check()) {
            return $cart->user_id === auth()->id();
        }
        
        // For guest users, check session
        return $cart->session_id === session()->getId();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'coupon_code' => [
                'required',
                'string',
                'max:50',
                Rule::exists('coupons', 'code')->where(function ($query) {
                    $query->where('is_active', true)
                          ->where('start_date', '<=', now())
                          ->where('end_date', '>=', now());
                }),
                function ($attribute, $value, $fail) {
                    $this->validateCouponEligibility($value, $fail);
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'coupon_code.required' => 'Coupon code is required.',
            'coupon_code.exists' => 'Invalid or expired coupon code.',
            'coupon_code.max' => 'Coupon code cannot exceed 50 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'coupon_code' => 'coupon code',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize coupon code to uppercase
        if ($this->has('coupon_code')) {
            $this->merge([
                'coupon_code' => strtoupper(trim($this->coupon_code)),
            ]);
        }
    }

    /**
     * Validate coupon eligibility for the current cart.
     */
    protected function validateCouponEligibility(string $couponCode, $fail): void
    {
        $coupon = Coupon::where('code', $couponCode)->first();
        
        if (!$coupon) {
            $fail('Coupon not found.');
            return;
        }

        $cart = $this->route('cart') ?? ShoppingCart::where('uuid', $this->route('cartId'))->first();
        
        if (!$cart) {
            $fail('Cart not found.');
            return;
        }

        // Check if coupon is already applied
        $appliedCoupons = $cart->applied_coupons ?? [];
        if (in_array($couponCode, array_column($appliedCoupons, 'code'))) {
            $fail('This coupon is already applied to your cart.');
            return;
        }

        // Check minimum order value
        if ($coupon->min_order_value && $cart->subtotal < $coupon->min_order_value) {
            $fail("Minimum order value of {$coupon->min_order_value} {$cart->currency} required for this coupon.");
            return;
        }

        // Check usage limits
        if ($coupon->usage_limit && $coupon->usage_count >= $coupon->usage_limit) {
            $fail('This coupon has reached its usage limit.');
            return;
        }

        // Check per-user limit
        if (auth()->check() && $coupon->per_user_limit) {
            $userUsageCount = $this->getUserCouponUsageCount($coupon->id, auth()->id());
            if ($userUsageCount >= $coupon->per_user_limit) {
                $fail('You have reached the usage limit for this coupon.');
                return;
            }
        }

        // Check vendor-specific coupons
        if ($coupon->vendor_id) {
            $cartHasVendorItems = $cart->items()
                ->where('vendor_id', $coupon->vendor_id)
                ->exists();
                
            if (!$cartHasVendorItems) {
                $fail('This coupon is only valid for specific vendor products not in your cart.');
                return;
            }
        }

        // Check user-specific coupons
        if ($coupon->user_id && (!auth()->check() || $coupon->user_id !== auth()->id())) {
            $fail('This coupon is not valid for your account.');
            return;
        }
    }

    /**
     * Get the number of times a user has used a specific coupon.
     */
    protected function getUserCouponUsageCount(int $couponId, int $userId): int
    {
        // This would typically check order history or coupon usage tracking
        // For now, we'll return 0 as a placeholder
        // TODO: Implement actual usage tracking
        return 0;
    }

    /**
     * Get the validated coupon model.
     */
    public function getCoupon(): ?Coupon
    {
        return Coupon::where('code', $this->coupon_code)->first();
    }

    /**
     * Calculate the discount amount for the coupon.
     */
    public function calculateDiscount(ShoppingCart $cart): float
    {
        $coupon = $this->getCoupon();
        
        if (!$coupon) {
            return 0.0;
        }

        $applicableAmount = $cart->subtotal;

        // If vendor-specific, only apply to vendor items
        if ($coupon->vendor_id) {
            $applicableAmount = $cart->items()
                ->where('vendor_id', $coupon->vendor_id)
                ->sum('total_price');
        }

        if ($coupon->type === 'percentage') {
            return min(
                ($applicableAmount * $coupon->value / 100),
                $applicableAmount
            );
        } elseif ($coupon->type === 'fixed') {
            return min($coupon->value, $applicableAmount);
        }

        return 0.0;
    }
}
