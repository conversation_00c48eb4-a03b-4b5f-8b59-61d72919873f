<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreWarehouseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    
    public function authorize(): bool
    {
        return true;
    }
            

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'name_en' => 'required|string|max:255',
            'name_ar'=> 'nullable|string|max:255',
            'code' => 'nullable|string|unique:warehouses|max:50',
            'address' => 'required|string|max:800',
            'location' => 'nullable|string|max:800',
            'contact_person' => 'nullable|string|max:100',
            'contact_number' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'status' => 'required|in:active,inactive',
            'is_global' => 'nullable|boolean',
        ];
    }
}
