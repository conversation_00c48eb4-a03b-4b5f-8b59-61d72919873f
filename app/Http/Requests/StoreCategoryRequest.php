<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => 'nullable|string|max:255|unique:categories,code',
            'fee_text' => 'nullable|string|max:255',
            'name_en' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'type' => 'required|in:main,sub',
            'parent_id' => 'nullable|exists:categories,id',
            'ordering_number' => 'nullable|integer',
            'banner_id' => 'nullable|exists:banners,id',
            'icon' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'slug' => 'required|string|max:255|unique:categories,slug',
        ];
    }
}
