<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVendorInformationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'eoi_id' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:pending,draft,submitted',
            'code' => 'nullable|string|max:255',
            'name_tl_en' => 'required|string|max:255',
            'name_tl_ar' => 'nullable|string|max:255',
            'vendor_display_name_en' => 'nullable|string|max:255',
            'vendor_display_name_ar' => 'nullable|string|max:255',
            'website' => 'nullable|string|max:255',
            'instagram_page' => 'nullable|string|max:255',
            'facebook_page' => 'nullable|string|max:255',
            'other_social_media' => 'nullable|string|max:255',
            'business_type' => 'nullable|json',
            'manufacturer_brands' => 'nullable|string|max:1000',
            'categories_to_sell' => 'nullable|string|max:1000',
            'tl_license_issuing_authority' => 'nullable|string|max:255',
            'tl_license_first_issue_date' => 'nullable|date',
            'tl_license_renewal_date' => 'nullable|date',
            'tl_license_valid_till' => 'nullable|date',
            'tl_entity_type' => 'nullable|string|max:255',
            'tl_no_of_partners' => 'nullable|integer',
            'tl_doc_copy_of_trade_license' => 'nullable|string|max:255',
            'tax_registration_number' => 'nullable|string|max:255',
            'trn_issue_date' => 'nullable|date',
            'trn_name_in_english' => 'nullable|string|max:255',
            'trn_name_in_arabic' => 'nullable|string|max:255',
            'vat_doc_copy_of_registration_certificate' => 'nullable|string|max:255',
            'director_name' => 'nullable|string|max:255',
            'director_designation' => 'nullable|string|max:255',
            'director_full_name_passport' => 'nullable|string|max:255',
            'director_passport_number' => 'nullable|string|max:255',
            'director_emirates_id_number' => 'nullable|string|max:255',
            'director_emirates_id_issue_date' => 'nullable|date',
            'director_emirates_id_expiry_date' => 'nullable|date',
            'director_email' => 'nullable|string|max:255',
            'director_mobile' => 'nullable|string|max:255',
            'director_preferred_language' => 'nullable|string|max:255',
            'director_passport_copy' => 'nullable|string|max:255',
            'director_emirates_id_copy' => 'nullable|string|max:255',
            'spoc_name' => 'nullable|string|max:255',
            'spoc_designation' => 'nullable|string|max:255',
            'spoc_email' => 'nullable|string|max:255',
            'spoc_mobile' => 'nullable|string|max:255',
            'spoc_passport_number' => 'nullable|string|max:255',
            'spoc_emirates_id_number' => 'nullable|string|max:255',
            'spoc_emirates_id_issue_date' => 'nullable|date',
            'spoc_emirates_id_expiry_date' => 'nullable|date',
            'spoc_letter_of_authorization' => 'nullable|string|max:255',
            'spoc_passport_copy' => 'nullable|string|max:255',
            'spoc_emirates_id_copy' => 'nullable|string|max:255',
            'spoc_loa_copy' => 'nullable|string|max:255',
            'additional_info' => 'nullable|string|max:1000',
            'signing_self_declaration' => 'nullable|string|max:255',
            'approval_status' => 'nullable|string|in:Pending,Approved,Rejected,OnHold,Cancelled',
            'approved_by' => 'nullable|integer',
            'is_active' => 'boolean|in:0,1',

            'vendor_address' => 'sometimes|nullable|array',
            'vendor_address.*.type' => 'required|in:office,retail_outlet,warehouse',
            'vendor_address.*.address' => 'nullable|string',
            'vendor_address.*.lat' => 'nullable|numeric',
            'vendor_address.*.long' => 'nullable|numeric',
            'vendor_address.*.map_location' => 'nullable|string',

            'vendor_contacts' => 'sometimes|nullable|array',
            'vendor_contacts.*.type' => 'required|in:alternate_sales_marketing,logistics,finance_accounting,others',
            'vendor_contacts.*.full_name' => 'required|string|max:255',
            'vendor_contacts.*.designation' => 'nullable|string|max:255',
            'vendor_contacts.*.email' => 'nullable|string|email|max:255',
            'vendor_contacts.*.mobile_number' => 'nullable|string|max:255',

            'vendor_banks' => 'sometimes|nullable|array',
            'vendor_banks.*.bank_name' => 'nullable|string',
            'vendor_banks.*.branch_name' => 'nullable|string',
            'vendor_banks.*.account_holder_name' => 'nullable|string',
            'vendor_banks.*.iban_number' => 'nullable|string',
            'vendor_banks.*.original_cheque_number' => 'nullable|string',
            'vendor_banks.*.bank_certificate_copy' => 'nullable|string',
            'vendor_banks.*.is_primary' => 'nullable|boolean',
            'vendor_banks.*.is_active' => 'nullable|boolean',

            'warehouse_types'=> 'nullable|in:own,global', 
            'warehouse_id' => 'required_if:warehouse_types,global|integer',
            'warehouse_name_en' => 'required_if:warehouse_types,own|string|max:255',
            //nullable 
            'warehouse_name_ar' => 'nullable|string|max:255',
            'warehouse_code' => 'required_if:warehouse_types,own|string|max:255',
            'warehouse_address' => 'required_if:warehouse_types,own|string|max:255',
            'warehouse_location' => 'required_if:warehouse_types,own|string|max:255',
            'warehouse_contact_person' => 'required_if:warehouse_types,own|string|max:255',
            'warehouse_contact_number' => 'required_if:warehouse_types,own|string|max:255',
            'warehouse_email' => 'required_if:warehouse_types,own|string|email|max:255',
            'warehouse_status' => 'required_if:warehouse_types,own|string|in:active,inactive',


        ];
    }
}
