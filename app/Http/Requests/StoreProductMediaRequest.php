<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductMediaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'sometimes|required|exists:products,id',
            'product_variant_id' => 'nullable|exists:product_variants,id',
            'type' => 'sometimes|required|in:image,video,lifestyle',
            'path' => 'sometimes|required|string|max:255',
            'title' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'lang_code' => 'nullable|string|max:5',
            'position' => 'nullable|integer|min:0',
            'is_primary' => 'nullable|in:0,1'
        ];
    }
}
