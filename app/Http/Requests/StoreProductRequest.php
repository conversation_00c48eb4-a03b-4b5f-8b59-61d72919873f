<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Essential required fields for product creation
            'category_id'     => 'required|exists:categories,id',
            'sub_category_id' => 'required|exists:categories,id',
            'class_id'        => 'required|exists:product_classes,id',
            'sub_class_id'    => 'nullable|integer',
            'vendor_sku'      => 'required|string|max:255', //todo: need to |unique:products,vendor_sku
            'model_number'    => 'required|string|max:255',
            'brand_id'        => 'required|exists:brands,id',

            // Optional fields
            'barcode'                      => 'nullable|string|max:255',
            'supplement_image'             => 'nullable|string|max:255',
            'storage_conditions'           => 'nullable|integer|exists:dropdown_options,id',
            'country_of_origin'            => 'nullable|integer|exists:dropdown_options,id',
            'is_returnable'                => 'nullable|integer|exists:dropdown_options,id',
            'warranty'                     => 'nullable|integer|exists:dropdown_options,id',
        ];
    }
}
