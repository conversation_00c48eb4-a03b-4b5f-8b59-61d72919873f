<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FiltersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'category_id' => 'nullable|integer|exists:categories,id',
            'subcategory_id' => 'nullable|integer|exists:categories,id',
            'brand_id' => 'nullable|integer|exists:brands,id',
            'search' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'category_id.integer' => 'The category ID must be an integer.',
            'category_id.exists' => 'The selected category does not exist.',
            'subcategory_id.integer' => 'The subcategory ID must be an integer.',
            'subcategory_id.exists' => 'The selected subcategory does not exist.',
            'brand_id.integer' => 'The brand ID must be an integer.',
            'brand_id.exists' => 'The selected brand does not exist.',
            'search.string' => 'The search term must be a string.',
            'search.max' => 'The search term cannot exceed 255 characters.',
        ];
    }
}
