<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVendorContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    
    public function authorize(): bool
    {
        return true;
    }
            

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vendor_id' => 'required|integer|exists:vendors,id',
            'type' => 'required|in:alternate_sales_marketing,logistics,finance_accounting,others',
            'full_name' => 'required|string|max:255',
            'designation' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'mobile_number' => 'nullable|string|max:255',
        ];
    }
}
