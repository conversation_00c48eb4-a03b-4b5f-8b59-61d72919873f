<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBannerItemRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'banner_id' => 'required|exists:banners,id',
            'title_en' => 'nullable|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'media_path' => 'required|string|max:255',
            'link_url' => 'nullable|url|max:1024',
            'target' => 'nullable|string|in:_self,_blank',
            'alt_text' => 'nullable|string|max:255',
            'position' => 'nullable|integer|min:0',
            'is_active'   => 'nullable|in:0,1',
        ];
    }
}
