<?php

namespace App\Http\Requests;

use App\Traits\PhoneHelper;
use Illuminate\Foundation\Http\FormRequest;


class RegisterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        $input = $this->input('email_or_phone');

        if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
            $normalized = PhoneHelper::normalize($input);
            $this->merge(['email_or_phone' => $normalized]);
        }
    }

    public function rules(): array
    {
        return [
            'name' => 'required|max:255',
            'email_or_phone' => 'required|string|max:255',
            'password' => 'required|min:8|confirmed',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date',
            'customer_type' => 'nullable|in:retail,wholesale',
        ];
    }

    public function messages(): array
    {
        return [
            'email_or_phone.required' => 'Please provide either an email address or phone number.',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $emailOrPhone = $this->input('email_or_phone');

            if ($emailOrPhone) {
                if (filter_var($emailOrPhone, FILTER_VALIDATE_EMAIL)) {
                    if (\App\Models\User::where('email', $emailOrPhone)->exists()) {
                        $validator->errors()->add('email_or_phone', 'This email address is already registered.');
                    }
                } else {
                    if (\App\Models\User::where('phone', $emailOrPhone)->exists()) {
                        $validator->errors()->add('email_or_phone', 'This phone number is already registered.');
                    }
                }
            }
        });
    }
}
