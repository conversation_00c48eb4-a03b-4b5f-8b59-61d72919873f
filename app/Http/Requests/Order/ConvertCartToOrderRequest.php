<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConvertCartToOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Cart identification
            'cart_uuid' => 'required|string|exists:shopping_carts,uuid',
            
            // Order preferences
            'split_by_vendor' => 'nullable|boolean',
            'payment_method' => ['nullable', Rule::in(['cod', 'card', 'wallet', 'bank'])],
            'customer_note' => 'nullable|string|max:1000',
            
            // Shipping address
            'shipping_address' => 'required|array',
            'shipping_address.first_name' => 'required|string|max:255',
            'shipping_address.last_name' => 'required|string|max:255',
            'shipping_address.company' => 'nullable|string|max:255',
            'shipping_address.address_line_1' => 'required|string|max:255',
            'shipping_address.address_line_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required|string|max:255',
            'shipping_address.state' => 'nullable|string|max:255',
            'shipping_address.postal_code' => 'nullable|string|max:20',
            'shipping_address.country' => 'required|string|max:3',
            'shipping_address.phone' => 'nullable|string|max:20',
            'shipping_address.email' => 'nullable|email|max:255',
            'shipping_address.special_instructions' => 'nullable|string|max:500',
            
            // Billing address (optional)
            'billing_address' => 'nullable|array',
            'billing_address.first_name' => 'required_with:billing_address|string|max:255',
            'billing_address.last_name' => 'required_with:billing_address|string|max:255',
            'billing_address.company' => 'nullable|string|max:255',
            'billing_address.address_line_1' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line_2' => 'nullable|string|max:255',
            'billing_address.city' => 'required_with:billing_address|string|max:255',
            'billing_address.state' => 'nullable|string|max:255',
            'billing_address.postal_code' => 'nullable|string|max:20',
            'billing_address.country' => 'required_with:billing_address|string|max:3',
            'billing_address.phone' => 'nullable|string|max:20',
            'billing_address.email' => 'nullable|email|max:255',
            
            // Conversion options
            'preserve_cart' => 'nullable|boolean',
            'apply_member_pricing' => 'nullable|boolean',
            'recalculate_pricing' => 'nullable|boolean',
            'validate_inventory' => 'nullable|boolean',
            'use_shipping_for_billing' => 'nullable|boolean',
            
            // Additional options
            'save_addresses' => 'nullable|boolean',
            'newsletter_consent' => 'nullable|boolean',
            'terms_accepted' => 'required|boolean|accepted',
            
            // Metadata
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'cart_uuid.required' => 'Cart is required for order conversion.',
            'cart_uuid.exists' => 'The specified cart does not exist.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
            'shipping_address.required' => 'Shipping address is required.',
            'shipping_address.first_name.required' => 'First name is required for shipping address.',
            'shipping_address.last_name.required' => 'Last name is required for shipping address.',
            'shipping_address.address_line_1.required' => 'Address line 1 is required for shipping address.',
            'shipping_address.city.required' => 'City is required for shipping address.',
            'shipping_address.country.required' => 'Country is required for shipping address.',
            'payment_method.in' => 'Invalid payment method selected.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'split_by_vendor' => $this->boolean('split_by_vendor', false),
            'preserve_cart' => $this->boolean('preserve_cart', false),
            'apply_member_pricing' => $this->boolean('apply_member_pricing', true),
            'recalculate_pricing' => $this->boolean('recalculate_pricing', true),
            'validate_inventory' => $this->boolean('validate_inventory', true),
            'use_shipping_for_billing' => $this->boolean('use_shipping_for_billing', false),
            'save_addresses' => $this->boolean('save_addresses', false),
            'newsletter_consent' => $this->boolean('newsletter_consent', false),
            'terms_accepted' => $this->boolean('terms_accepted'),
        ]);

        // If use_shipping_for_billing is true, copy shipping address to billing
        if ($this->boolean('use_shipping_for_billing') && $this->has('shipping_address')) {
            $this->merge([
                'billing_address' => $this->input('shipping_address')
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateCartAccess($validator);
            $this->validateCartStatus($validator);
            $this->validateCartContents($validator);
            $this->validateAddresses($validator);
            $this->validateConversionOptions($validator);
        });
    }

    /**
     * Validate cart access permissions
     */
    protected function validateCartAccess($validator): void
    {
        if (!$this->has('cart_uuid')) {
            return;
        }

        $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
        
        if (!$cart) {
            return; // Will be caught by exists rule
        }

        // Check ownership for authenticated users
        if (auth()->check() && $cart->user_id !== auth()->id()) {
            $validator->errors()->add('cart_uuid', 'You do not have permission to access this cart.');
        }

        // Check session ownership for guest users
        if (!auth()->check() && $cart->session_id !== session()->getId()) {
            $validator->errors()->add('cart_uuid', 'You do not have permission to access this cart.');
        }
    }

    /**
     * Validate cart status and eligibility
     */
    protected function validateCartStatus($validator): void
    {
        if (!$this->has('cart_uuid')) {
            return;
        }

        $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
        
        if (!$cart) {
            return;
        }

        // Check if cart is active
        if ($cart->status !== 'active') {
            $validator->errors()->add('cart_uuid', 'Cart is not active and cannot be converted to order.');
        }

        // Check if cart has expired
        if ($cart->hasExpired()) {
            $validator->errors()->add('cart_uuid', 'Cart has expired and cannot be converted to order.');
        }

        // Check if cart is empty
        if ($cart->items()->count() === 0) {
            $validator->errors()->add('cart_uuid', 'Cart is empty and cannot be converted to order.');
        }
    }

    /**
     * Validate cart contents
     */
    protected function validateCartContents($validator): void
    {
        if (!$this->has('cart_uuid')) {
            return;
        }

        $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
        
        if (!$cart) {
            return;
        }

        // Validate inventory if requested
        if ($this->boolean('validate_inventory')) {
            foreach ($cart->items as $item) {
                $product = $item->product;
                $variant = $item->variant;

                // Check product availability
                if (!$product || !$product->is_active) {
                    $validator->errors()->add('cart_contents', 
                        "Product '{$item->product_title}' is no longer available.");
                    continue;
                }

                // Check variant availability
                if ($variant && !$variant->is_active) {
                    $validator->errors()->add('cart_contents', 
                        "Product variant '{$item->product_title}' is no longer available.");
                    continue;
                }

                // Check stock availability
                $availableStock = $variant ? $variant->stock_quantity : $product->stock_quantity;
                if ($availableStock < $item->quantity) {
                    $validator->errors()->add('cart_contents', 
                        "Insufficient stock for '{$item->product_title}'. Available: {$availableStock}, Requested: {$item->quantity}");
                }
            }
        }

        // Check for vendor-specific requirements
        if ($this->boolean('split_by_vendor')) {
            $vendorGroups = $cart->items->groupBy('vendor_id');
            
            foreach ($vendorGroups as $vendorId => $items) {
                $vendor = \App\Models\Vendor::find($vendorId);
                
                if (!$vendor || !$vendor->is_active) {
                    $validator->errors()->add('cart_contents', 
                        "Vendor for some products is no longer active.");
                }
            }
        }
    }

    /**
     * Validate addresses
     */
    protected function validateAddresses($validator): void
    {
        $supportedCountries = ['AE', 'UAE', 'US', 'USA', 'GB', 'UK'];
        
        // Validate shipping address country
        if ($this->has('shipping_address.country')) {
            $country = strtoupper($this->input('shipping_address.country'));
            if (!in_array($country, $supportedCountries)) {
                $validator->errors()->add('shipping_address.country', 
                    'Shipping to this country is not supported.');
            }
        }
        
        // Validate billing address country
        if ($this->has('billing_address.country')) {
            $country = strtoupper($this->input('billing_address.country'));
            if (!in_array($country, $supportedCountries)) {
                $validator->errors()->add('billing_address.country', 
                    'Billing to this country is not supported.');
            }
        }
        
        // Validate phone numbers
        if ($this->has('shipping_address.phone')) {
            $phone = $this->input('shipping_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('shipping_address.phone', 'Invalid phone number format.');
            }
        }
        
        if ($this->has('billing_address.phone')) {
            $phone = $this->input('billing_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('billing_address.phone', 'Invalid phone number format.');
            }
        }
    }

    /**
     * Validate conversion options
     */
    protected function validateConversionOptions($validator): void
    {
        // If member pricing is requested, check if user has a tier
        if ($this->boolean('apply_member_pricing') && auth()->check()) {
            $customer = auth()->user()->customer;
            
            if (!$customer || !$customer->hasActiveTier()) {
                // This is just a warning, not an error
                // Member pricing will simply not be applied
            }
        }

        // Validate split by vendor option
        if ($this->boolean('split_by_vendor')) {
            $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
            
            if ($cart) {
                $vendorCount = $cart->items->groupBy('vendor_id')->count();
                
                if ($vendorCount <= 1) {
                    $validator->errors()->add('split_by_vendor', 
                        'Cart contains items from only one vendor. Split by vendor is not applicable.');
                }
            }
        }
    }

    /**
     * Get validated data with computed values
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // Add computed values
        $validated['user_id'] = auth()->id();
        $validated['currency'] = 'AED'; // Default currency
        $validated['conversion_timestamp'] = now()->toISOString();
        
        // Normalize country codes
        if (isset($validated['shipping_address']['country'])) {
            $validated['shipping_address']['country'] = strtoupper($validated['shipping_address']['country']);
        }
        
        if (isset($validated['billing_address']['country'])) {
            $validated['billing_address']['country'] = strtoupper($validated['billing_address']['country']);
        }

        // Add conversion metadata
        $conversionMetadata = [
            'converted_by' => auth()->id(),
            'conversion_method' => 'api_request',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'conversion_options' => [
                'split_by_vendor' => $validated['split_by_vendor'],
                'apply_member_pricing' => $validated['apply_member_pricing'],
                'recalculate_pricing' => $validated['recalculate_pricing'],
                'validate_inventory' => $validated['validate_inventory'],
            ],
        ];

        if (isset($validated['metadata'])) {
            $validated['metadata'] = array_merge($validated['metadata'], $conversionMetadata);
        } else {
            $validated['metadata'] = $conversionMetadata;
        }

        return $validated;
    }
}
