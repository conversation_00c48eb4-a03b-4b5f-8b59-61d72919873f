<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $order = $this->route('order');
        
        // Admin can update any order
        if (auth()->user()->hasRole('admin')) {
            return true;
        }
        
        // Vendor can update their own orders
        if (auth()->user()->hasRole('vendor') && $order->vendor_id === auth()->user()->vendor_id) {
            return true;
        }
        
        // Customer can update their own orders (limited fields)
        if (auth()->user()->hasRole('customer') && $order->user_id === auth()->id()) {
            return true;
        }
        
        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $order = $this->route('order');
        $user = auth()->user();
        
        $rules = [];
        
        // Admin can update most fields
        if ($user->hasRole('admin')) {
            $rules = [
                'payment_method' => ['nullable', Rule::in(['cod', 'card', 'wallet', 'bank'])],
                'customer_note' => 'nullable|string|max:1000',
                'admin_note' => 'nullable|string|max:1000',
                'tracking_number' => 'nullable|string|max:255',
                'shipping_fee' => 'nullable|numeric|min:0',
                'metadata' => 'nullable|array',
                
                // Address updates
                'shipping_address' => 'nullable|array',
                'shipping_address.first_name' => 'required_with:shipping_address|string|max:255',
                'shipping_address.last_name' => 'required_with:shipping_address|string|max:255',
                'shipping_address.company' => 'nullable|string|max:255',
                'shipping_address.address_line_1' => 'required_with:shipping_address|string|max:255',
                'shipping_address.address_line_2' => 'nullable|string|max:255',
                'shipping_address.city' => 'required_with:shipping_address|string|max:255',
                'shipping_address.state' => 'nullable|string|max:255',
                'shipping_address.postal_code' => 'nullable|string|max:20',
                'shipping_address.country' => 'required_with:shipping_address|string|max:3',
                'shipping_address.phone' => 'nullable|string|max:20',
                'shipping_address.email' => 'nullable|email|max:255',
                'shipping_address.special_instructions' => 'nullable|string|max:500',
                
                'billing_address' => 'nullable|array',
                'billing_address.first_name' => 'required_with:billing_address|string|max:255',
                'billing_address.last_name' => 'required_with:billing_address|string|max:255',
                'billing_address.company' => 'nullable|string|max:255',
                'billing_address.address_line_1' => 'required_with:billing_address|string|max:255',
                'billing_address.address_line_2' => 'nullable|string|max:255',
                'billing_address.city' => 'required_with:billing_address|string|max:255',
                'billing_address.state' => 'nullable|string|max:255',
                'billing_address.postal_code' => 'nullable|string|max:20',
                'billing_address.country' => 'required_with:billing_address|string|max:3',
                'billing_address.phone' => 'nullable|string|max:20',
                'billing_address.email' => 'nullable|email|max:255',
            ];
        }
        
        // Vendor can update limited fields
        if ($user->hasRole('vendor')) {
            $rules = [
                'tracking_number' => 'nullable|string|max:255',
                'admin_note' => 'nullable|string|max:1000', // Vendor notes
                'metadata' => 'nullable|array',
            ];
        }
        
        // Customer can update very limited fields
        if ($user->hasRole('customer')) {
            $rules = [
                'customer_note' => 'nullable|string|max:1000',
                'shipping_address' => 'nullable|array',
                'shipping_address.phone' => 'nullable|string|max:20',
                'shipping_address.special_instructions' => 'nullable|string|max:500',
            ];
        }
        
        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'payment_method.in' => 'Invalid payment method selected.',
            'shipping_fee.numeric' => 'Shipping fee must be a valid number.',
            'shipping_fee.min' => 'Shipping fee cannot be negative.',
            'tracking_number.max' => 'Tracking number cannot exceed 255 characters.',
            'customer_note.max' => 'Customer note cannot exceed 1000 characters.',
            'admin_note.max' => 'Admin note cannot exceed 1000 characters.',
            'shipping_address.phone.max' => 'Phone number cannot exceed 20 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateOrderStatus($validator);
            $this->validateAddresses($validator);
            $this->validatePermissions($validator);
        });
    }

    /**
     * Validate order status allows updates
     */
    protected function validateOrderStatus($validator): void
    {
        $order = $this->route('order');
        
        // Orders can only be updated in certain statuses
        $updatableStatuses = ['pending', 'confirmed'];
        
        if (!in_array($order->fulfillment_status, $updatableStatuses)) {
            $validator->errors()->add('order', 'Order cannot be updated in its current status.');
        }
        
        // Check if order is cancelled
        if ($order->fulfillment_status === 'cancelled') {
            $validator->errors()->add('order', 'Cancelled orders cannot be updated.');
        }
    }

    /**
     * Validate addresses
     */
    protected function validateAddresses($validator): void
    {
        $supportedCountries = ['AE', 'UAE', 'US', 'USA', 'GB', 'UK'];
        
        // Validate shipping address country
        if ($this->has('shipping_address.country')) {
            $country = strtoupper($this->input('shipping_address.country'));
            if (!in_array($country, $supportedCountries)) {
                $validator->errors()->add('shipping_address.country', 'Shipping to this country is not supported.');
            }
        }
        
        // Validate billing address country
        if ($this->has('billing_address.country')) {
            $country = strtoupper($this->input('billing_address.country'));
            if (!in_array($country, $supportedCountries)) {
                $validator->errors()->add('billing_address.country', 'Billing to this country is not supported.');
            }
        }
        
        // Validate phone numbers
        if ($this->has('shipping_address.phone')) {
            $phone = $this->input('shipping_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('shipping_address.phone', 'Invalid phone number format.');
            }
        }
        
        if ($this->has('billing_address.phone')) {
            $phone = $this->input('billing_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('billing_address.phone', 'Invalid phone number format.');
            }
        }
    }

    /**
     * Validate user permissions for specific fields
     */
    protected function validatePermissions($validator): void
    {
        $user = auth()->user();
        $order = $this->route('order');
        
        // Customer-specific validations
        if ($user->hasRole('customer')) {
            // Customers can only update their own orders
            if ($order->user_id !== $user->id) {
                $validator->errors()->add('order', 'You can only update your own orders.');
            }
            
            // Customers cannot update certain fields
            $restrictedFields = ['payment_method', 'admin_note', 'tracking_number', 'shipping_fee'];
            foreach ($restrictedFields as $field) {
                if ($this->has($field)) {
                    $validator->errors()->add($field, 'You do not have permission to update this field.');
                }
            }
        }
        
        // Vendor-specific validations
        if ($user->hasRole('vendor')) {
            // Vendors can only update orders for their products
            if ($order->vendor_id !== $user->vendor_id) {
                $validator->errors()->add('order', 'You can only update orders for your products.');
            }
            
            // Vendors cannot update certain fields
            $restrictedFields = ['payment_method', 'customer_note', 'shipping_fee'];
            foreach ($restrictedFields as $field) {
                if ($this->has($field)) {
                    $validator->errors()->add($field, 'You do not have permission to update this field.');
                }
            }
        }
    }

    /**
     * Get validated data with computed values
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // Normalize country codes
        if (isset($validated['shipping_address']['country'])) {
            $validated['shipping_address']['country'] = strtoupper($validated['shipping_address']['country']);
        }
        
        if (isset($validated['billing_address']['country'])) {
            $validated['billing_address']['country'] = strtoupper($validated['billing_address']['country']);
        }
        
        // Add update metadata
        $validated['updated_by'] = auth()->id();
        $validated['updated_at'] = now();
        
        return $validated;
    }
}
