<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;

class CancelOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $order = $this->route('order');
        $user = auth()->user();
        
        // Admin can cancel any order
        if ($user->hasRole('admin')) {
            return true;
        }
        
        // Customer can cancel their own orders
        if ($user->hasRole('customer') && $order->user_id === $user->id) {
            return true;
        }
        
        // Vendor can cancel orders for their products (with restrictions)
        if ($user->hasRole('vendor') && $order->vendor_id === $user->vendor_id) {
            return true;
        }
        
        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'reason' => 'required|string|max:500',
            'cancellation_type' => 'required|in:customer_request,vendor_request,admin_action,system_auto,inventory_issue,payment_issue',
            'refund_requested' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
            'notify_customer' => 'nullable|boolean',
            'notify_vendor' => 'nullable|boolean',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'reason.required' => 'A reason for cancellation is required.',
            'reason.max' => 'Cancellation reason cannot exceed 500 characters.',
            'cancellation_type.required' => 'Cancellation type is required.',
            'cancellation_type.in' => 'Invalid cancellation type selected.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'cancellation_type' => 'cancellation type',
            'refund_requested' => 'refund request',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values based on user role
        $user = auth()->user();
        
        if ($user->hasRole('customer')) {
            $this->merge([
                'cancellation_type' => 'customer_request',
                'refund_requested' => true,
                'notify_vendor' => true,
            ]);
        } elseif ($user->hasRole('vendor')) {
            $this->merge([
                'cancellation_type' => 'vendor_request',
                'notify_customer' => true,
            ]);
        } elseif ($user->hasRole('admin')) {
            $this->merge([
                'cancellation_type' => $this->input('cancellation_type', 'admin_action'),
                'notify_customer' => $this->boolean('notify_customer', true),
                'notify_vendor' => $this->boolean('notify_vendor', true),
            ]);
        }
        
        // Set default notification preferences
        $this->merge([
            'refund_requested' => $this->boolean('refund_requested', false),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateOrderCancellable($validator);
            $this->validateCancellationPermissions($validator);
            $this->validateRefundEligibility($validator);
        });
    }

    /**
     * Validate that the order can be cancelled
     */
    protected function validateOrderCancellable($validator): void
    {
        $order = $this->route('order');
        
        // Check if order is already cancelled
        if ($order->fulfillment_status === 'cancelled') {
            $validator->errors()->add('order', 'Order is already cancelled.');
            return;
        }
        
        // Check if order can be cancelled based on status
        $cancellableStatuses = ['pending', 'confirmed', 'processing'];
        
        if (!in_array($order->fulfillment_status, $cancellableStatuses)) {
            $validator->errors()->add('order', 'Order cannot be cancelled in its current status.');
        }
        
        // Check if order is delivered
        if ($order->fulfillment_status === 'delivered') {
            $validator->errors()->add('order', 'Delivered orders cannot be cancelled. Please request a return instead.');
        }
        
        // Check if order is shipped (special handling required)
        if ($order->fulfillment_status === 'shipped') {
            $user = auth()->user();
            
            // Only admin can cancel shipped orders
            if (!$user->hasRole('admin')) {
                $validator->errors()->add('order', 'Shipped orders can only be cancelled by administrators.');
            }
        }
    }

    /**
     * Validate cancellation permissions based on user role
     */
    protected function validateCancellationPermissions($validator): void
    {
        $order = $this->route('order');
        $user = auth()->user();
        $cancellationType = $this->input('cancellation_type');
        
        // Customer-specific validations
        if ($user->hasRole('customer')) {
            // Customers can only cancel their own orders
            if ($order->user_id !== $user->id) {
                $validator->errors()->add('order', 'You can only cancel your own orders.');
            }
            
            // Customers cannot use certain cancellation types
            $customerAllowedTypes = ['customer_request'];
            if (!in_array($cancellationType, $customerAllowedTypes)) {
                $validator->errors()->add('cancellation_type', 'Invalid cancellation type for customer.');
            }
            
            // Check cancellation time limit for customers
            $orderAge = $order->created_at->diffInHours(now());
            $cancellationTimeLimit = 24; // 24 hours
            
            if ($orderAge > $cancellationTimeLimit && $order->fulfillment_status !== 'pending') {
                $validator->errors()->add('order', 'Orders can only be cancelled within 24 hours of placement.');
            }
        }
        
        // Vendor-specific validations
        if ($user->hasRole('vendor')) {
            // Vendors can only cancel orders for their products
            if ($order->vendor_id !== $user->vendor_id) {
                $validator->errors()->add('order', 'You can only cancel orders for your products.');
            }
            
            // Vendors cannot use certain cancellation types
            $vendorAllowedTypes = ['vendor_request', 'inventory_issue'];
            if (!in_array($cancellationType, $vendorAllowedTypes)) {
                $validator->errors()->add('cancellation_type', 'Invalid cancellation type for vendor.');
            }
        }
    }

    /**
     * Validate refund eligibility
     */
    protected function validateRefundEligibility($validator): void
    {
        $order = $this->route('order');
        $refundRequested = $this->boolean('refund_requested');
        
        if ($refundRequested) {
            // Check if order is paid
            if ($order->payment_status !== 'paid') {
                $validator->errors()->add('refund_requested', 'Refund can only be requested for paid orders.');
            }
            
            // Check if order is already refunded
            if ($order->payment_status === 'refunded') {
                $validator->errors()->add('refund_requested', 'Order is already refunded.');
            }
            
            // Check refund policy (example: no refunds after 30 days)
            $orderAge = $order->created_at->diffInDays(now());
            $refundTimeLimit = 30; // 30 days
            
            if ($orderAge > $refundTimeLimit) {
                $validator->errors()->add('refund_requested', 'Refund period has expired.');
            }
        }
    }

    /**
     * Get validated data with computed values
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        $order = $this->route('order');
        
        // Add cancellation metadata
        $cancellationMetadata = [
            'cancelled_by' => auth()->id(),
            'cancelled_by_role' => auth()->user()->roles->pluck('name')->first(),
            'cancelled_at' => now()->toISOString(),
            'order_status_at_cancellation' => $order->fulfillment_status,
            'payment_status_at_cancellation' => $order->payment_status,
            'order_total' => $order->total,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ];
        
        if (isset($validated['metadata'])) {
            $validated['metadata'] = array_merge($validated['metadata'], $cancellationMetadata);
        } else {
            $validated['metadata'] = $cancellationMetadata;
        }
        
        // Set default notification preferences if not specified
        if (!isset($validated['notify_customer'])) {
            $validated['notify_customer'] = true;
        }
        
        if (!isset($validated['notify_vendor'])) {
            $validated['notify_vendor'] = true;
        }
        
        return $validated;
    }
}
