<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOrderStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $order = $this->route('order');
        $user = auth()->user();
        
        // Admin can update any order status
        if ($user->hasRole('admin')) {
            return true;
        }
        
        // Vendor can update status for their orders
        if ($user->hasRole('vendor') && $order->vendor_id === $user->vendor_id) {
            return true;
        }
        
        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $order = $this->route('order');
        $user = auth()->user();
        
        // Get valid status transitions for current order status
        $validStatuses = $this->getValidStatusTransitions($order->fulfillment_status, $user);
        $validPaymentStatuses = $this->getValidPaymentStatusTransitions($order->payment_status, $user);
        
        return [
            'fulfillment_status' => [
                'nullable',
                Rule::in($validStatuses)
            ],
            'payment_status' => [
                'nullable',
                Rule::in($validPaymentStatuses)
            ],
            'reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'tracking_number' => 'nullable|string|max:255',
            'notify_customer' => 'nullable|boolean',
            'notify_vendor' => 'nullable|boolean',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'fulfillment_status.in' => 'Invalid fulfillment status transition.',
            'payment_status.in' => 'Invalid payment status transition.',
            'reason.max' => 'Reason cannot exceed 500 characters.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'tracking_number.max' => 'Tracking number cannot exceed 255 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateStatusTransitions($validator);
            $this->validateRequiredFields($validator);
            $this->validatePermissions($validator);
        });
    }

    /**
     * Validate status transitions
     */
    protected function validateStatusTransitions($validator): void
    {
        $order = $this->route('order');
        
        // Validate fulfillment status transition
        if ($this->has('fulfillment_status')) {
            $newStatus = $this->input('fulfillment_status');
            $currentStatus = $order->fulfillment_status;
            
            if (!$this->isValidStatusTransition($currentStatus, $newStatus)) {
                $validator->errors()->add('fulfillment_status', 
                    "Cannot transition from '{$currentStatus}' to '{$newStatus}'.");
            }
        }
        
        // Validate payment status transition
        if ($this->has('payment_status')) {
            $newStatus = $this->input('payment_status');
            $currentStatus = $order->payment_status;
            
            if (!$this->isValidPaymentStatusTransition($currentStatus, $newStatus)) {
                $validator->errors()->add('payment_status', 
                    "Cannot transition payment status from '{$currentStatus}' to '{$newStatus}'.");
            }
        }
    }

    /**
     * Validate required fields for specific status changes
     */
    protected function validateRequiredFields($validator): void
    {
        $fulfillmentStatus = $this->input('fulfillment_status');
        $paymentStatus = $this->input('payment_status');
        
        // Tracking number required when marking as shipped
        if ($fulfillmentStatus === 'shipped' && !$this->filled('tracking_number')) {
            $validator->errors()->add('tracking_number', 'Tracking number is required when marking order as shipped.');
        }
        
        // Reason required for cancellation
        if ($fulfillmentStatus === 'cancelled' && !$this->filled('reason')) {
            $validator->errors()->add('reason', 'Reason is required when cancelling an order.');
        }
        
        // Reason required for refund
        if ($paymentStatus === 'refunded' && !$this->filled('reason')) {
            $validator->errors()->add('reason', 'Reason is required when processing a refund.');
        }
    }

    /**
     * Validate user permissions for specific status changes
     */
    protected function validatePermissions($validator): void
    {
        $user = auth()->user();
        $fulfillmentStatus = $this->input('fulfillment_status');
        $paymentStatus = $this->input('payment_status');
        
        // Only admins can change payment status
        if ($this->has('payment_status') && !$user->hasRole('admin')) {
            $validator->errors()->add('payment_status', 'You do not have permission to change payment status.');
        }
        
        // Vendors have limited fulfillment status changes
        if ($user->hasRole('vendor')) {
            $vendorAllowedStatuses = ['processing', 'shipped', 'delivered'];
            
            if ($fulfillmentStatus && !in_array($fulfillmentStatus, $vendorAllowedStatuses)) {
                $validator->errors()->add('fulfillment_status', 
                    'You do not have permission to set this fulfillment status.');
            }
        }
    }

    /**
     * Get valid status transitions for current status
     */
    protected function getValidStatusTransitions(string $currentStatus, $user): array
    {
        $allTransitions = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['processing', 'cancelled'],
            'processing' => ['shipped', 'cancelled'],
            'shipped' => ['delivered', 'returned'],
            'delivered' => ['returned'],
            'cancelled' => [],
            'returned' => [],
        ];
        
        $validTransitions = $allTransitions[$currentStatus] ?? [];
        
        // Filter based on user role
        if ($user->hasRole('vendor')) {
            // Vendors cannot cancel orders, only admins can
            $validTransitions = array_filter($validTransitions, function($status) {
                return $status !== 'cancelled';
            });
        }
        
        return $validTransitions;
    }

    /**
     * Get valid payment status transitions
     */
    protected function getValidPaymentStatusTransitions(string $currentStatus, $user): array
    {
        // Only admins can change payment status
        if (!$user->hasRole('admin')) {
            return [];
        }
        
        $transitions = [
            'pending' => ['paid', 'failed'],
            'paid' => ['refunded'],
            'failed' => ['paid'],
            'refunded' => [],
        ];
        
        return $transitions[$currentStatus] ?? [];
    }

    /**
     * Check if status transition is valid
     */
    protected function isValidStatusTransition(string $from, string $to): bool
    {
        $validTransitions = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['processing', 'cancelled'],
            'processing' => ['shipped', 'cancelled'],
            'shipped' => ['delivered', 'returned'],
            'delivered' => ['returned'],
            'cancelled' => [],
            'returned' => [],
        ];
        
        return in_array($to, $validTransitions[$from] ?? []);
    }

    /**
     * Check if payment status transition is valid
     */
    protected function isValidPaymentStatusTransition(string $from, string $to): bool
    {
        $validTransitions = [
            'pending' => ['paid', 'failed'],
            'paid' => ['refunded'],
            'failed' => ['paid'],
            'refunded' => [],
        ];
        
        return in_array($to, $validTransitions[$from] ?? []);
    }

    /**
     * Get validated data with computed values
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // Add metadata about the status change
        $statusChangeMetadata = [
            'changed_by' => auth()->id(),
            'changed_by_role' => auth()->user()->roles->pluck('name')->first(),
            'changed_at' => now()->toISOString(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ];
        
        if (isset($validated['metadata'])) {
            $validated['metadata'] = array_merge($validated['metadata'], $statusChangeMetadata);
        } else {
            $validated['metadata'] = $statusChangeMetadata;
        }
        
        // Set default notification preferences
        $validated['notify_customer'] = $validated['notify_customer'] ?? true;
        $validated['notify_vendor'] = $validated['notify_vendor'] ?? true;
        
        return $validated;
    }
}
