<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Order basic information
            'cart_uuid' => 'required|string|exists:shopping_carts,uuid',
            'payment_method' => ['nullable', Rule::in(['cod', 'card', 'wallet', 'bank'])],
            'customer_note' => 'nullable|string|max:1000',
            
            // Shipping address
            'shipping_address' => 'required|array',
            'shipping_address.first_name' => 'required|string|max:255',
            'shipping_address.last_name' => 'required|string|max:255',
            'shipping_address.company' => 'nullable|string|max:255',
            'shipping_address.address_line_1' => 'required|string|max:255',
            'shipping_address.address_line_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required|string|max:255',
            'shipping_address.state' => 'nullable|string|max:255',
            'shipping_address.postal_code' => 'nullable|string|max:20',
            'shipping_address.country' => 'required|string|max:3',
            'shipping_address.phone' => 'nullable|string|max:20',
            'shipping_address.email' => 'nullable|email|max:255',
            'shipping_address.special_instructions' => 'nullable|string|max:500',
            
            // Billing address (optional, defaults to shipping if not provided)
            'billing_address' => 'nullable|array',
            'billing_address.first_name' => 'required_with:billing_address|string|max:255',
            'billing_address.last_name' => 'required_with:billing_address|string|max:255',
            'billing_address.company' => 'nullable|string|max:255',
            'billing_address.address_line_1' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line_2' => 'nullable|string|max:255',
            'billing_address.city' => 'required_with:billing_address|string|max:255',
            'billing_address.state' => 'nullable|string|max:255',
            'billing_address.postal_code' => 'nullable|string|max:20',
            'billing_address.country' => 'required_with:billing_address|string|max:3',
            'billing_address.phone' => 'nullable|string|max:20',
            'billing_address.email' => 'nullable|email|max:255',
            
            // Additional options
            'use_shipping_for_billing' => 'nullable|boolean',
            'save_addresses' => 'nullable|boolean',
            'newsletter_consent' => 'nullable|boolean',
            'terms_accepted' => 'required|boolean|accepted',
            
            // Metadata
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'cart_uuid.required' => 'Cart is required to create an order.',
            'cart_uuid.exists' => 'The specified cart does not exist.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
            'shipping_address.required' => 'Shipping address is required.',
            'shipping_address.first_name.required' => 'First name is required for shipping address.',
            'shipping_address.last_name.required' => 'Last name is required for shipping address.',
            'shipping_address.address_line_1.required' => 'Address line 1 is required for shipping address.',
            'shipping_address.city.required' => 'City is required for shipping address.',
            'shipping_address.country.required' => 'Country is required for shipping address.',
            'payment_method.in' => 'Invalid payment method selected.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'cart_uuid' => 'cart',
            'shipping_address.first_name' => 'shipping first name',
            'shipping_address.last_name' => 'shipping last name',
            'shipping_address.address_line_1' => 'shipping address',
            'shipping_address.city' => 'shipping city',
            'shipping_address.country' => 'shipping country',
            'billing_address.first_name' => 'billing first name',
            'billing_address.last_name' => 'billing last name',
            'billing_address.address_line_1' => 'billing address',
            'billing_address.city' => 'billing city',
            'billing_address.country' => 'billing country',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // If use_shipping_for_billing is true, copy shipping address to billing
        if ($this->boolean('use_shipping_for_billing') && $this->has('shipping_address')) {
            $this->merge([
                'billing_address' => $this->input('shipping_address')
            ]);
        }

        // Set default values
        $this->merge([
            'terms_accepted' => $this->boolean('terms_accepted'),
            'save_addresses' => $this->boolean('save_addresses', false),
            'newsletter_consent' => $this->boolean('newsletter_consent', false),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            $this->validateCartOwnership($validator);
            $this->validateCartStatus($validator);
            $this->validateAddresses($validator);
        });
    }

    /**
     * Validate cart ownership
     */
    protected function validateCartOwnership($validator): void
    {
        if (!$this->has('cart_uuid')) {
            return;
        }

        $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
        
        if (!$cart) {
            return; // Will be caught by exists rule
        }

        // Check ownership for authenticated users
        if (auth()->check() && $cart->user_id !== auth()->id()) {
            $validator->errors()->add('cart_uuid', 'You do not have permission to access this cart.');
        }

        // Check session ownership for guest users
        if (!auth()->check() && $cart->session_id !== session()->getId()) {
            $validator->errors()->add('cart_uuid', 'You do not have permission to access this cart.');
        }
    }

    /**
     * Validate cart status
     */
    protected function validateCartStatus($validator): void
    {
        if (!$this->has('cart_uuid')) {
            return;
        }

        $cart = \App\Models\ShoppingCart::where('uuid', $this->input('cart_uuid'))->first();
        
        if (!$cart) {
            return;
        }

        if ($cart->status !== 'active') {
            $validator->errors()->add('cart_uuid', 'Cart is not active and cannot be used for order creation.');
        }

        if ($cart->hasExpired()) {
            $validator->errors()->add('cart_uuid', 'Cart has expired and cannot be used for order creation.');
        }

        if ($cart->items()->count() === 0) {
            $validator->errors()->add('cart_uuid', 'Cart is empty and cannot be used for order creation.');
        }
    }

    /**
     * Validate addresses
     */
    protected function validateAddresses($validator): void
    {
        // Validate country codes
        $supportedCountries = ['AE', 'UAE', 'US', 'USA', 'GB', 'UK']; // Add more as needed
        
        if ($this->has('shipping_address.country')) {
            $shippingCountry = strtoupper($this->input('shipping_address.country'));
            if (!in_array($shippingCountry, $supportedCountries)) {
                $validator->errors()->add('shipping_address.country', 'Shipping to this country is not supported.');
            }
        }

        if ($this->has('billing_address.country')) {
            $billingCountry = strtoupper($this->input('billing_address.country'));
            if (!in_array($billingCountry, $supportedCountries)) {
                $validator->errors()->add('billing_address.country', 'Billing to this country is not supported.');
            }
        }

        // Validate phone numbers
        if ($this->has('shipping_address.phone')) {
            $phone = $this->input('shipping_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('shipping_address.phone', 'Invalid phone number format.');
            }
        }

        if ($this->has('billing_address.phone')) {
            $phone = $this->input('billing_address.phone');
            if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{7,15}$/', $phone)) {
                $validator->errors()->add('billing_address.phone', 'Invalid phone number format.');
            }
        }
    }

    /**
     * Get validated data with computed values
     */
    public function validatedWithComputed(): array
    {
        $validated = $this->validated();
        
        // Add computed values
        $validated['user_id'] = auth()->id();
        $validated['currency'] = 'AED'; // Default currency
        
        // Normalize country codes
        if (isset($validated['shipping_address']['country'])) {
            $validated['shipping_address']['country'] = strtoupper($validated['shipping_address']['country']);
        }
        
        if (isset($validated['billing_address']['country'])) {
            $validated['billing_address']['country'] = strtoupper($validated['billing_address']['country']);
        }

        return $validated;
    }
}
