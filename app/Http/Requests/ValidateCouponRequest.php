<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateCouponRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => 'required|string|max:50',
            'vendor_id' => 'nullable|exists:vendors,id',
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Coupon code is required.',
            'code.string' => 'Coupon code must be a string.',
            'code.max' => 'Coupon code cannot exceed 50 characters.',
            'vendor_id.exists' => 'Selected vendor does not exist.',
        ];
    }
}
