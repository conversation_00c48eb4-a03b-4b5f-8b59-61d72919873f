<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateVendorInformationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => 'nullable|string|max:255',
            'avatar' => 'nullable|string|max:255',
            'name_tl_en' => 'required|string|max:255',
            'name_tl_ar' => 'nullable|string|max:255',
            'vendor_display_name_en' => 'nullable|string|max:255',
            'vendor_display_name_ar' => 'nullable|string|max:255',
            'website' => 'nullable|string|max:255',
            'instagram_page' => 'nullable|string|max:255',
            'facebook_page' => 'nullable|string|max:255',
            'other_social_media' => 'nullable|string|max:255',
            'business_type' => 'nullable|array',
            'manufacturer_brands' => 'nullable|string|max:1000',
            'categories_to_sell' => 'nullable|string|max:1000',
            'tl_license_issuing_authority' => 'nullable|string|max:255',
            'tl_license_first_issue_date' => 'nullable|date',
            'tl_license_renewal_date' => 'nullable|date',
            'tl_license_valid_till' => 'nullable|date',
            'tl_entity_type' => 'nullable|string|max:255',
            'tl_no_of_partners' => 'nullable|integer',
            'tl_doc_copy_of_trade_license' => 'nullable|string|max:255',
            'tax_registration_number' => 'nullable|string|max:255',
            'trn_issue_date' => 'nullable|date',
            'trn_name_in_english' => 'nullable|string|max:255',
            'trn_name_in_arabic' => 'nullable|string|max:255',
            'vat_doc_copy_of_registration_certificate' => 'nullable|string|max:255',
            'director_name' => 'nullable|string|max:255',
            'director_designation' => 'nullable|string|max:255',
            'director_full_name_passport' => 'nullable|string|max:255',
            'director_passport_number' => 'nullable|string|max:255',
            'director_emirates_id_number' => 'nullable|string|max:255',
            'director_emirates_id_issue_date' => 'nullable|date',
            'director_emirates_id_expiry_date' => 'nullable|date',
            'director_email' => 'nullable|string|max:255',
            'director_mobile' => 'nullable|string|max:255',
            'director_preferred_language' => 'nullable|string|max:255',
            'director_passport_copy' => 'nullable|string|max:255',
            'director_emirates_id_copy' => 'nullable|string|max:255',
            'spoc_name' => 'nullable|string|max:255',
            'spoc_designation' => 'nullable|string|max:255',
            'spoc_email' => 'nullable|string|max:255',
            'spoc_mobile' => 'nullable|string|max:255',
            'spoc_passport_number' => 'nullable|string|max:255',
            'spoc_emirates_id_number' => 'nullable|string|max:255',
            'spoc_emirates_id_issue_date' => 'nullable|date',
            'spoc_emirates_id_expiry_date' => 'nullable|date',
            'spoc_letter_of_authorization' => 'nullable|string|max:255',
            'spoc_passport_copy' => 'nullable|string|max:255',
            'spoc_emirates_id_copy' => 'nullable|string|max:255',
            'spoc_loa_copy' => 'nullable|string|max:255',
            'additional_info' => 'nullable|string|max:1000',
            'signing_self_declaration' => 'nullable|string|max:255',
            'approval_status' => 'nullable|string|in:Pending,Approved,Rejected,OnHold,Cancelled',
            'approved_by' => 'nullable|integer',
            'is_active' => 'boolean|in:0,1',
        ];
    }
}
