<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UniversalProductsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Context filters
            'category_id' => 'nullable|integer|exists:categories,id',
            'subcategory_id' => 'nullable|integer|exists:categories,id',
            'brand_id' => 'nullable|array',
            'brand_id.*' => 'integer|exists:brands,id',
            
            // Filter parameters
            'user_group_id' => 'nullable|array',
            'user_group_id.*' => 'integer|exists:dropdown_options,id',
            'country_of_origin_id' => 'nullable|array',
            'country_of_origin_id.*' => 'integer|exists:dropdown_options,id',
            'formulation_id' => 'nullable|integer|exists:dropdown_options,id',
            'flavour_id' => 'nullable|integer|exists:dropdown_options,id',
            'storage_conditions_id' => 'nullable|integer|exists:dropdown_options,id',
            'is_returnable_id' => 'nullable|integer|exists:dropdown_options,id',
            'warranty_id' => 'nullable|integer|exists:dropdown_options,id',
            
            // Price range filters
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0|gte:min_price',
            
            // Boolean filters
            'is_vegan' => 'nullable|boolean',
            'is_vegetarian' => 'nullable|boolean',
            'is_halal' => 'nullable|boolean',
            
            // Search
            'search' => 'nullable|string|max:255',
            
            // Sorting
            'sort_by' => 'nullable|string|in:created_at,title_en,title_ar,regular_price,offer_price,short_name',
            'sort_order' => 'nullable|string|in:asc,desc',
            
            // Pagination
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'category_id.integer' => 'The category ID must be an integer.',
            'category_id.exists' => 'The selected category does not exist.',
            'subcategory_id.integer' => 'The subcategory ID must be an integer.',
            'subcategory_id.exists' => 'The selected subcategory does not exist.',
            'brand_id.array' => 'The brand ID must be an array.',
            'brand_id.*.integer' => 'Each brand ID must be an integer.',
            'brand_id.*.exists' => 'One or more selected brands do not exist.',
            'user_group_id.array' => 'The user group ID must be an array.',
            'user_group_id.*.integer' => 'Each user group ID must be an integer.',
            'user_group_id.*.exists' => 'One or more selected user groups do not exist.',
            'country_of_origin_id.array' => 'The country of origin ID must be an array.',
            'country_of_origin_id.*.integer' => 'Each country of origin ID must be an integer.',
            'country_of_origin_id.*.exists' => 'One or more selected countries do not exist.',
            'formulation_id.integer' => 'The formulation ID must be an integer.',
            'formulation_id.exists' => 'The selected formulation does not exist.',
            'flavour_id.integer' => 'The flavour ID must be an integer.',
            'flavour_id.exists' => 'The selected flavour does not exist.',
            'storage_conditions_id.integer' => 'The storage conditions ID must be an integer.',
            'storage_conditions_id.exists' => 'The selected storage condition does not exist.',
            'is_returnable_id.integer' => 'The return policy ID must be an integer.',
            'is_returnable_id.exists' => 'The selected return policy does not exist.',
            'warranty_id.integer' => 'The warranty ID must be an integer.',
            'warranty_id.exists' => 'The selected warranty does not exist.',
            'min_price.numeric' => 'The minimum price must be a number.',
            'min_price.min' => 'The minimum price must be at least 0.',
            'max_price.numeric' => 'The maximum price must be a number.',
            'max_price.min' => 'The maximum price must be at least 0.',
            'max_price.gte' => 'The maximum price must be greater than or equal to the minimum price.',
            'is_vegan.boolean' => 'The vegan filter must be true or false.',
            'is_vegetarian.boolean' => 'The vegetarian filter must be true or false.',
            'is_halal.boolean' => 'The halal filter must be true or false.',
            'search.string' => 'The search term must be a string.',
            'search.max' => 'The search term cannot exceed 255 characters.',
            'sort_by.string' => 'The sort field must be a string.',
            'sort_by.in' => 'The sort field must be one of: created_at, title_en, title_ar, regular_price, offer_price, short_name.',
            'sort_order.string' => 'The sort order must be a string.',
            'sort_order.in' => 'The sort order must be either asc or desc.',
            'page.integer' => 'The page number must be an integer.',
            'page.min' => 'The page number must be at least 1.',
            'per_page.integer' => 'The per page value must be an integer.',
            'per_page.min' => 'The per page value must be at least 1.',
            'per_page.max' => 'The per page value cannot exceed 50.',
        ];
    }
}
