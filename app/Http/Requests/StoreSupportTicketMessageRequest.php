<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupportTicketMessageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ticket_id' => 'required|exists:support_tickets,id',
            'message' => 'required_without:attachments|string',
            'attachments.*' => 'file|max:5120', // max 5MB per file
        ];
    }
}
