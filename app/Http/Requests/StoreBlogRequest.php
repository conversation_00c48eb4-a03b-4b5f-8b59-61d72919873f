<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBlogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'blog_category_id' => 'nullable|exists:blog_categories,id',
            'user_id' => 'nullable|exists:users,id',
            'title_en' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'summary_en' => 'nullable|string',
            'summary_ar' => 'nullable|string',
            'content_en' => 'nullable|string',
            'content_ar' => 'nullable|string',
            'slug' => ['required', 'string', 'max:255', Rule::unique('blogs', 'slug')],
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'keywords' => 'nullable|string|max:255',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published',
            'published_at' => 'nullable|date',
        ];
    }
}
