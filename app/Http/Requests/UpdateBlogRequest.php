<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateBlogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $blogId = $this->route('blog');

        return [
            'blog_category_id' => 'sometimes|nullable|exists:blog_categories,id',
            'user_id' => 'sometimes|nullable|exists:users,id',
            'title_en' => 'sometimes|required|string|max:255',
            'title_ar' => 'sometimes|nullable|string|max:255',
            'summary_en' => 'sometimes|nullable|string',
            'summary_ar' => 'sometimes|nullable|string',
            'content_en' => 'sometimes|nullable|string',
            'content_ar' => 'sometimes|nullable|string',
            'slug' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('blogs', 'slug')->ignore($blogId),
            ],
            'meta_title' => 'sometimes|nullable|string|max:255',
            'meta_description' => 'sometimes|nullable|string|max:255',
            'keywords' => 'sometimes|nullable|string|max:255',
            'featured_image' => 'nullable|string',
            'status' => 'sometimes|required|in:draft,published',
            'published_at' => 'sometimes|nullable|date',
        ];
    }
}
