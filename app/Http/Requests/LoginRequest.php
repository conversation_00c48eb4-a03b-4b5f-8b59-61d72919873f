<?php

namespace App\Http\Requests;

use App\Traits\PhoneHelper;
use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        $input = $this->input('email_or_phone');

        if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
            $normalized = PhoneHelper::normalize($input);
            $this->merge(['email_or_phone' => $normalized]);
        }
    }

    public function rules(): array
    {
        return [
            'email_or_phone' => [
                'required',
                function ($attribute, $value, $fail) {
                    $isEmail = filter_var($value, FILTER_VALIDATE_EMAIL);
                    $column = $isEmail ? 'email' : 'phone';

                    if (!\App\Models\User::where($column, $value)->exists()) {
                        $fail("The provided $column does not exist.");
                    }
                },
            ],
            'password' => 'required',
        ];
    }
}
