<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateVendorStaffRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('staff');

        return [
            'name'        => 'required|string|max:255',
            'email'       => 'required|string|email|max:255|unique:users,email,' . $userId,
            'password'    => 'nullable|string|confirmed|min:8',
            'phone'       => 'nullable|string|unique:users,phone,' . $userId,
            'avatar'      => 'nullable|string',
            'status'      => 'nullable|string|in:pending,active,inactive,banned',
            'is_active'   => 'in:0,1',
            'roles'       => 'nullable|array',
            'permissions' => 'nullable|array',
            // '_method'    => 'sometimes|in:PUT,PATCH',
        ];
    }
}
