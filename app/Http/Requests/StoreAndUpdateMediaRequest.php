<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class StoreAndUpdateMediaRequest extends FormRequest
 {
        public function authorize(): bool
        {
            return true;
        }

        public function rules(): array
        {
            return [
                'media' => 'required|array|min:1',
                'media.*.product_id' => 'required|exists:products,id',
                'media.*.product_variant_id' => 'nullable|exists:product_variants,id',
                'media.*.type' => 'required|in:image,video,lifestyle',
                'media.*.path' => 'required|string|max:255',
                'media.*.title' => 'nullable|string|max:255',
                'media.*.alt_text' => 'nullable|string|max:255',
                'media.*.lang_code' => 'nullable|string|max:5',
                'media.*.position' => 'nullable|integer|min:0',
                'media.*.is_primary' => 'nullable|in:0,1',
            ];
        }

        public function withValidator(Validator $validator): void
        {
            $validator->after(function ($validator) {
                $media = $this->input('media', []);

                // Count how many are marked as primary
                $primaryCount = collect($media)->where('is_primary', '1')->count();

                if ($primaryCount > 1) {
                    $validator->errors()->add('media', 'Only one media item can have is_primary set to 1.');
                }

                if ($primaryCount === 0) {
                    $validator->errors()->add('media', 'At least one media item must have is_primary set to 1.');
                }
            });
        }
    }
