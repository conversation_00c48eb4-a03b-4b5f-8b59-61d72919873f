<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupportTicketRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'category_id' => 'nullable|exists:support_categories,id',
            'topic_id' => 'nullable|exists:support_topics,id',
            'vendor_id' => 'nullable|exists:vendors,id',
            'order_id' => 'nullable',
        ];
    }
}
