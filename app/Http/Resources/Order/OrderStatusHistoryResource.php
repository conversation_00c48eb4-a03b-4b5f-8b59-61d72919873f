<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderStatusHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            
            // Status change information
            'status_change' => [
                'from' => $this->from_status,
                'to' => $this->to_status,
                'type' => $this->status_type,
                'display' => $this->status_change_display,
                'icon' => $this->getStatusIcon(),
                'color' => $this->getStatusColor(),
            ],
            
            // Change details
            'details' => [
                'reason' => $this->reason,
                'notes' => $this->notes,
                'is_upgrade' => $this->isStatusUpgrade(),
                'is_downgrade' => $this->isStatusDowngrade(),
            ],
            
            // Who made the change
            'changed_by' => [
                'user_id' => $this->user_id,
                'user_name' => $this->changed_by_display,
                'user_type' => $this->changed_by_type,
                'user_details' => $this->when($this->user, [
                    'name' => $this->user?->name,
                    'email' => $this->user?->email,
                ]),
            ],
            
            // Timing
            'timing' => [
                'changed_at' => $this->changed_at,
                'changed_at_human' => $this->time_since_change,
                'changed_at_formatted' => $this->changed_at->format('M j, Y g:i A'),
            ],
            
            // Metadata (admin only)
            'metadata' => $this->when(
                $this->shouldShowMetadata($request),
                $this->metadata
            ),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timeline_position' => $this->getTimelinePosition(),
                'is_milestone' => $this->isMilestone(),
                'next_expected_status' => $this->getNextExpectedStatus(),
            ],
        ];
    }

    /**
     * Determine if metadata should be shown
     */
    protected function shouldShowMetadata(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show metadata only to admins
        return $user->hasRole('admin');
    }

    /**
     * Get timeline position for UI display
     */
    protected function getTimelinePosition(): int
    {
        $statusOrder = [
            'pending' => 1,
            'confirmed' => 2,
            'processing' => 3,
            'shipped' => 4,
            'delivered' => 5,
            'cancelled' => 0,
            'returned' => 0,
        ];

        return $statusOrder[$this->to_status] ?? 0;
    }

    /**
     * Check if this is a milestone status
     */
    protected function isMilestone(): bool
    {
        $milestoneStatuses = ['confirmed', 'shipped', 'delivered', 'cancelled'];
        return in_array($this->to_status, $milestoneStatuses);
    }

    /**
     * Get next expected status
     */
    protected function getNextExpectedStatus(): ?string
    {
        $nextStatuses = [
            'pending' => 'confirmed',
            'confirmed' => 'processing',
            'processing' => 'shipped',
            'shipped' => 'delivered',
            'delivered' => null,
            'cancelled' => null,
            'returned' => null,
        ];

        return $nextStatuses[$this->to_status] ?? null;
    }
}
