<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            
            // Personal information
            'contact' => [
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'full_name' => $this->full_name,
                'company' => $this->company,
                'phone' => $this->phone,
                'email' => $this->email,
            ],
            
            // Address details
            'address' => [
                'address_line_1' => $this->address_line_1,
                'address_line_2' => $this->address_line_2,
                'city' => $this->city,
                'state' => $this->state,
                'postal_code' => $this->postal_code,
                'country' => $this->country,
                'formatted' => $this->formatted_address,
            ],
            
            // Additional information
            'special_instructions' => $this->special_instructions,
            
            // Address type flags
            'is_shipping_address' => $this->is_shipping_address,
            'is_billing_address' => $this->is_billing_address,
            
            // Metadata (admin only)
            'metadata' => $this->when(
                $this->shouldShowMetadata($request),
                $this->metadata
            ),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Determine if metadata should be shown
     */
    protected function shouldShowMetadata(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show metadata only to admins
        return $user->hasRole('admin');
    }
}
