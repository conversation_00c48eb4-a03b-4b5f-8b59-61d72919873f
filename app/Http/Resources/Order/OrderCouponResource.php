<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderCouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            
            // Coupon information
            'coupon' => [
                'id' => $this->coupon_id,
                'code' => $this->coupon_code,
                'title' => $this->coupon_title,
                'display' => $this->coupon_display,
            ],
            
            // Coupon configuration
            'configuration' => [
                'type' => $this->coupon_type,
                'value' => $this->coupon_value,
                'min_order_value' => $this->min_order_value,
            ],
            
            // Discount details
            'discount' => [
                'amount' => $this->discount_amount,
                'percentage' => $this->discount_percentage,
                'display' => $this->savings_display,
                'order_subtotal_at_application' => $this->order_subtotal_at_application,
            ],
            
            // Vendor information
            'vendor' => $this->when($this->vendor_id, [
                'id' => $this->vendor_id,
                'name' => $this->vendor?->name,
                'is_vendor_specific' => $this->is_vendor_specific,
            ]),
            
            // Application details
            'application' => [
                'applied_at' => $this->applied_at,
                'applied_at_human' => $this->time_since_applied,
                'applied_at_formatted' => $this->applied_at->format('M j, Y g:i A'),
            ],
            
            // Breakdown
            'breakdown' => $this->getDiscountBreakdown(),
            
            // Metadata (admin only)
            'metadata' => $this->when(
                $this->shouldShowMetadata($request),
                $this->metadata
            ),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Determine if metadata should be shown
     */
    protected function shouldShowMetadata(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show metadata only to admins
        return $user->hasRole('admin');
    }
}
