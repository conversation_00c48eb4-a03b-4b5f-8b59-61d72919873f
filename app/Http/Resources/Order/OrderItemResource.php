<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            
            // Product information
            'product' => [
                'id' => $this->product_id,
                'title' => $this->product_title,
                'name' => $this->product_name,
                'sku' => $this->sku,
                'barcode' => $this->barcode,
                'image' => $this->product_image,
                'url' => $this->when($this->product, route('products.show', $this->product_id)),
            ],
            
            // Variant information
            'variant' => $this->when($this->product_variant_id, [
                'id' => $this->product_variant_id,
                'name' => $this->productVariant?->name,
                'sku' => $this->productVariant?->sku,
                'attributes' => $this->productVariant?->attributes,
            ]),
            
            // Vendor information
            'vendor' => [
                'id' => $this->vendor_id,
                'name' => $this->vendor?->name,
                'email' => $this->vendor?->email,
            ],
            
            // Quantity and pricing
            'quantity' => $this->quantity,
            'pricing' => [
                'base_price' => $this->base_price,
                'promotional_price' => $this->promotional_price,
                'member_price' => $this->member_price,
                'unit_price' => $this->price,
                'final_unit_price' => $this->final_unit_price,
                'subtotal' => $this->total,
                'discount' => $this->discount,
                'tax' => $this->tax,
                'total_with_tax' => $this->total_with_tax,
                'unit_price_with_tax' => $this->unit_price_with_tax,
                'savings_amount' => $this->savings_amount,
                'applied_pricing_type' => $this->getAppliedPricingType(),
            ],
            
            // Pricing breakdown
            'pricing_breakdown' => $this->getPricingBreakdown(),
            
            // Status
            'status' => [
                'fulfillment' => $this->fulfillment_status,
                'fulfillment_display' => $this->status_display,
            ],
            
            // Customizations and instructions
            'customizations' => $this->customizations,
            'special_instructions' => $this->special_instructions,
            'applied_discounts' => $this->applied_discounts,
            
            // Product snapshot (for historical accuracy)
            'product_snapshot' => $this->when(
                $this->shouldShowProductSnapshot($request),
                $this->product_snapshot
            ),
            
            // Metadata
            'metadata' => $this->when(
                $this->shouldShowMetadata($request),
                $this->metadata
            ),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'can_return' => $this->canBeReturned(),
                'can_exchange' => $this->canBeExchanged(),
                'return_deadline' => $this->getReturnDeadline(),
            ],
        ];
    }

    /**
     * Determine if product snapshot should be shown
     */
    protected function shouldShowProductSnapshot(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show product snapshot to admins and vendors
        return $user->hasRole(['admin', 'vendor']);
    }

    /**
     * Determine if metadata should be shown
     */
    protected function shouldShowMetadata(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show metadata only to admins
        return $user->hasRole('admin');
    }

    /**
     * Check if item can be returned
     */
    protected function canBeReturned(): bool
    {
        // Items can be returned if order is delivered and within return period
        if ($this->order->fulfillment_status !== 'delivered') {
            return false;
        }
        
        $returnPeriodDays = 30; // 30 days return policy
        $orderAge = $this->order->created_at->diffInDays(now());
        
        return $orderAge <= $returnPeriodDays;
    }

    /**
     * Check if item can be exchanged
     */
    protected function canBeExchanged(): bool
    {
        // Similar to return policy but might have different rules
        return $this->canBeReturned() && $this->product && $this->product->is_active;
    }

    /**
     * Get return deadline
     */
    protected function getReturnDeadline(): ?string
    {
        if (!$this->canBeReturned()) {
            return null;
        }
        
        $returnPeriodDays = 30;
        return $this->order->created_at->addDays($returnPeriodDays)->format('Y-m-d');
    }
}
