<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'order_number' => $this->order_number,
            
            // Customer information (minimal)
            'customer' => [
                'id' => $this->user_id,
                'name' => $this->user->name,
                'email' => $this->user->email,
            ],
            
            // Vendor information (minimal)
            'vendor' => $this->when($this->vendor_id, [
                'id' => $this->vendor_id,
                'name' => $this->vendor?->name,
            ]),
            
            // Financial summary
            'pricing' => [
                'total' => $this->total,
                'currency' => $this->currency,
                'total_items' => $this->total_items,
                'total_quantity' => $this->total_quantity,
            ],
            
            // Status information
            'status' => [
                'fulfillment' => $this->fulfillment_status,
                'fulfillment_display' => $this->status_display,
                'payment' => $this->payment_status,
                'payment_display' => $this->payment_status_display,
                'is_paid' => $this->is_paid,
            ],
            
            // Key details
            'details' => [
                'payment_method' => $this->payment_method,
                'tracking_number' => $this->tracking_number,
                'has_customer_note' => !empty($this->customer_note),
                'has_admin_note' => !empty($this->admin_note),
            ],
            
            // Quick actions
            'actions' => [
                'can_cancel' => $this->can_cancel,
                'can_refund' => $this->can_refund,
                'view_url' => $this->getViewUrl($request),
            ],
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_at_human' => $this->created_at->diffForHumans(),
            'order_age_days' => $this->created_at->diffInDays(now()),
        ];
    }

    /**
     * Get the appropriate view URL based on user role
     */
    protected function getViewUrl(Request $request): string
    {
        $user = $request->user();
        
        if (!$user) {
            return '';
        }
        
        if ($user->hasRole('admin')) {
            return route('admin.orders.show', $this->uuid);
        }
        
        if ($user->hasRole('vendor') && $this->vendor_id === $user->vendor_id) {
            return route('vendor.orders.show', $this->uuid);
        }
        
        if ($user->hasRole('customer') && $this->user_id === $user->id) {
            return route('client.orders.show', $this->uuid);
        }
        
        return '';
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'estimated_delivery' => $this->getEstimatedDeliveryDate(),
                'priority_level' => $this->getPriorityLevel(),
            ],
        ];
    }

    /**
     * Get estimated delivery date
     */
    protected function getEstimatedDeliveryDate(): ?string
    {
        if ($this->fulfillment_status === 'delivered') {
            return null;
        }
        
        $estimatedDays = match($this->fulfillment_status) {
            'pending' => 5,
            'confirmed' => 4,
            'processing' => 3,
            'shipped' => 2,
            default => null,
        };
        
        if ($estimatedDays) {
            return now()->addDays($estimatedDays)->format('Y-m-d');
        }
        
        return null;
    }

    /**
     * Get priority level based on order characteristics
     */
    protected function getPriorityLevel(): string
    {
        // High priority for large orders or VIP customers
        if ($this->total > 1000) {
            return 'high';
        }
        
        if ($this->user->customer?->pricingTier?->code === 'VIP') {
            return 'high';
        }
        
        // Medium priority for orders over 500 AED
        if ($this->total > 500) {
            return 'medium';
        }
        
        return 'normal';
    }
}
