<?php

namespace App\Http\Resources\Cart;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'cart_id' => $this->cart_id,
            'product_id' => $this->product_id,
            'vendor_id' => $this->vendor_id,
            'variant_id' => $this->variant_id,
            
            // Quantity and pricing
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'total_price' => $this->total_price,
            'base_price' => $this->base_price,
            'promotional_price' => $this->promotional_price,
            'final_unit_price' => $this->final_unit_price,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'savings_amount' => $this->savings_amount,
            
            // Product information from snapshot
            'product_name' => $this->product_name,
            'product_name_ar' => $this->product_snapshot['name_ar'] ?? null,
            'product_image' => $this->product_image,
            'product_sku' => $this->product_snapshot['sku'] ?? null,
            'product_description' => $this->product_snapshot['description'] ?? null,
            
            // Variant information
            'variant_name' => $this->product_snapshot['variant']['name'] ?? null,
            'variant_sku' => $this->product_snapshot['variant']['sku'] ?? null,
            'variant_attributes' => $this->product_snapshot['variant']['attributes'] ?? null,
            
            // Vendor information
            'vendor_name' => $this->product_snapshot['vendor']['name'] ?? null,
            'vendor_display_name' => $this->product_snapshot['vendor']['display_name'] ?? null,
            
            // Customizations and metadata
            'customizations' => $this->customizations,
            'special_instructions' => $this->special_instructions,
            'metadata' => $this->metadata,
            'applied_discounts' => $this->applied_discounts,
            
            // Availability status
            'is_available' => $this->isAvailable(),
            'has_insufficient_stock' => $this->hasInsufficientStock(),
            'stock_status' => $this->getStockStatus(),
            
            // Product snapshot for historical accuracy
            'product_snapshot' => $this->when(
                $request->has('include_snapshot') && $request->include_snapshot === 'true',
                $this->product_snapshot
            ),
            
            // Timestamps
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Live product data (if relationships are loaded)
            'product' => $this->when(
                $this->relationLoaded('product'),
                [
                    'id' => $this->product?->id,
                    'name_en' => $this->product?->name_en,
                    'name_ar' => $this->product?->name_ar,
                    'sku' => $this->product?->sku,
                    'status' => $this->product?->status,
                    'current_price' => $this->product?->price,
                    'original_price' => $this->product?->original_price,
                    'stock_quantity' => $this->product?->stock_quantity,
                    'featured_image' => $this->product?->featured_image,
                    'min_cart_quantity' => $this->product?->min_cart_quantity,
                    'max_cart_quantity' => $this->product?->max_cart_quantity,
                    'cart_increment' => $this->product?->cart_increment,
                    'allow_backorder' => $this->product?->allow_backorder,
                ]
            ),
            
            // Live variant data (if relationships are loaded)
            'variant' => $this->when(
                $this->variant_id && $this->relationLoaded('variant'),
                [
                    'id' => $this->variant?->id,
                    'name' => $this->variant?->name,
                    'sku' => $this->variant?->sku,
                    'status' => $this->variant?->status,
                    'price' => $this->variant?->price,
                    'original_price' => $this->variant?->original_price,
                    'stock_quantity' => $this->variant?->stock_quantity,
                    'attributes' => $this->variant?->attributes,
                ]
            ),
            
            // Vendor data (if relationships are loaded)
            'vendor' => $this->when(
                $this->relationLoaded('vendor'),
                [
                    'id' => $this->vendor?->id,
                    'name' => $this->vendor?->name_tl_en,
                    'display_name' => $this->vendor?->vendor_display_name_en,
                    'status' => $this->vendor?->status,
                    'min_order_value' => $this->vendor?->min_order_value,
                    'free_shipping_threshold' => $this->vendor?->free_shipping_threshold,
                ]
            ),
            
            // Price comparison
            'price_comparison' => [
                'has_discount' => $this->base_price > $this->unit_price,
                'discount_percentage' => $this->getDiscountPercentage(),
                'you_save' => $this->savings_amount,
            ],
            
            // Quantity constraints
            'quantity_constraints' => [
                'min_quantity' => $this->product?->min_cart_quantity ?? 1,
                'max_quantity' => $this->product?->max_cart_quantity,
                'increment' => $this->product?->cart_increment ?? 1,
                'available_stock' => $this->getAvailableStock(),
            ],
        ];
    }

    /**
     * Get stock status for the item.
     */
    protected function getStockStatus(): string
    {
        if (!$this->isAvailable()) {
            return 'unavailable';
        }

        if ($this->hasInsufficientStock()) {
            return 'insufficient_stock';
        }

        $availableStock = $this->getAvailableStock();
        
        if ($availableStock <= 0) {
            return $this->product?->allow_backorder ? 'backorder' : 'out_of_stock';
        }

        if ($availableStock <= 5) {
            return 'low_stock';
        }

        return 'in_stock';
    }

    /**
     * Get available stock for the item.
     */
    protected function getAvailableStock(): int
    {
        if ($this->variant_id && $this->variant) {
            return $this->variant->stock_quantity ?? 0;
        }

        return $this->product?->stock_quantity ?? 0;
    }

    /**
     * Get discount percentage.
     */
    protected function getDiscountPercentage(): float
    {
        if ($this->base_price <= 0 || $this->unit_price >= $this->base_price) {
            return 0;
        }

        return round((($this->base_price - $this->unit_price) / $this->base_price) * 100, 1);
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'currency' => $this->cart?->currency ?? 'AED',
                'currency_symbol' => $this->getCurrencySymbol($this->cart?->currency ?? 'AED'),
            ],
        ];
    }

    /**
     * Get currency symbol.
     */
    protected function getCurrencySymbol(string $currency): string
    {
        return match ($currency) {
            'AED' => 'د.إ',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            default => $currency,
        };
    }
}
