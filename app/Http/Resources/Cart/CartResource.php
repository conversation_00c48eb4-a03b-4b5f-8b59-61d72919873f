<?php

namespace App\Http\Resources\Cart;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'user_id' => $this->user_id,
            'session_id' => $this->when(!$this->user_id, $this->session_id),
            'currency' => $this->currency,
            'status' => $this->status,
            
            // Cart totals
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'shipping_amount' => $this->shipping_amount,
            'total_amount' => $this->total_amount,
            
            // Cart metadata
            'items_count' => $this->items_count,
            'total_quantity' => $this->total_quantity,
            'is_expired' => $this->is_expired,
            'notes' => $this->notes,
            'metadata' => $this->metadata,
            
            // Applied discounts and coupons
            'applied_coupons' => $this->applied_coupons,
            'applied_discounts' => $this->applied_discounts,
            
            // Timestamps
            'expires_at' => $this->expires_at?->toISOString(),
            'last_activity_at' => $this->last_activity_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'items' => CartItemResource::collection($this->whenLoaded('items')),
            'vendor_groups' => $this->when(
                $this->relationLoaded('items') && $this->items->isNotEmpty(),
                $this->vendor_groups
            ),
            
            // User information (if authenticated)
            'user' => $this->when(
                $this->user_id && $this->relationLoaded('user'),
                [
                    'id' => $this->user?->id,
                    'name' => $this->user?->name,
                    'email' => $this->user?->email,
                ]
            ),
            
            // Cart validation status
            'validation' => $this->when(
                $request->has('validate') && $request->validate === 'true',
                function () {
                    $validationService = app(\App\Services\CartValidationService::class);
                    return $validationService->validateCartForCheckout($this->resource);
                }
            ),
            
            // Pricing breakdown
            'pricing_breakdown' => $this->when(
                $request->has('include_breakdown') && $request->include_breakdown === 'true',
                function () {
                    $calculationService = app(\App\Services\CartCalculationService::class);
                    return $calculationService->getPricingBreakdown($this->resource);
                }
            ),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'currency_symbol' => $this->getCurrencySymbol($this->currency),
                'tax_rate' => 0.05, // 5% VAT in UAE
                'free_shipping_threshold' => $this->getFreeShippingThreshold(),
                'cart_limits' => [
                    'max_items' => config('cart.max_items_per_cart', 100),
                    'max_value' => config('cart.max_cart_value', 50000),
                ],
            ],
        ];
    }

    /**
     * Get currency symbol.
     */
    protected function getCurrencySymbol(string $currency): string
    {
        return match ($currency) {
            'AED' => 'د.إ',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            default => $currency,
        };
    }

    /**
     * Get free shipping threshold for the cart.
     */
    protected function getFreeShippingThreshold(): ?float
    {
        if (!$this->relationLoaded('items') || $this->items->isEmpty()) {
            return null;
        }

        // Get the lowest free shipping threshold among vendors
        $thresholds = $this->items
            ->pluck('vendor.free_shipping_threshold')
            ->filter()
            ->unique();

        return $thresholds->isEmpty() ? null : $thresholds->min();
    }
}
