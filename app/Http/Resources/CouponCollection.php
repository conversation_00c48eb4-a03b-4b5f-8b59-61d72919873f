<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CouponCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => CouponResource::collection($this->collection),
            'meta' => [
                'total_count' => $this->collection->count(),
                'active_count' => $this->collection->where('is_active', true)->count(),
                'expired_count' => $this->collection->filter(function ($coupon) {
                    return $coupon->is_expired;
                })->count(),
                'upcoming_count' => $this->collection->filter(function ($coupon) {
                    return !$coupon->is_started;
                })->count(),
                'platform_wide_count' => $this->collection->whereNull('vendor_id')->count(),
                'vendor_specific_count' => $this->collection->whereNotNull('vendor_id')->count(),
                'percentage_coupons' => $this->collection->where('type', 'percentage')->count(),
                'fixed_coupons' => $this->collection->where('type', 'fixed')->count(),
            ],
            'summary' => [
                'total_discount_value' => $this->getTotalDiscountValue(),
                'average_discount' => $this->getAverageDiscount(),
                'most_valuable_coupon' => $this->getMostValuableCoupon(),
                'expiring_soon' => $this->getExpiringSoon(),
            ],
        ];
    }

    /**
     * Get total discount value (for fixed coupons only)
     */
    private function getTotalDiscountValue(): float
    {
        return $this->collection
            ->where('type', 'fixed')
            ->sum('value');
    }

    /**
     * Get average discount value
     */
    private function getAverageDiscount(): array
    {
        $percentageCoupons = $this->collection->where('type', 'percentage');
        $fixedCoupons = $this->collection->where('type', 'fixed');

        return [
            'percentage' => $percentageCoupons->count() > 0 
                ? round($percentageCoupons->avg('value'), 2) . '%'
                : null,
            'fixed' => $fixedCoupons->count() > 0 
                ? 'AED ' . number_format($fixedCoupons->avg('value'), 2)
                : null,
        ];
    }

    /**
     * Get most valuable coupon
     */
    private function getMostValuableCoupon(): ?array
    {
        $mostValuable = $this->collection->sortByDesc(function ($coupon) {
            // For comparison, convert percentage to estimated value based on average order
            if ($coupon->type === 'percentage') {
                $estimatedOrderValue = $coupon->min_order_value ?: 100; // Default estimate
                return ($estimatedOrderValue * $coupon->value) / 100;
            }
            return $coupon->value;
        })->first();

        if (!$mostValuable) {
            return null;
        }

        return [
            'id' => $mostValuable->id,
            'code' => $mostValuable->code,
            'title' => $mostValuable->title,
            'type' => $mostValuable->type,
            'value' => $mostValuable->value,
            'formatted_value' => $mostValuable->type === 'percentage' 
                ? $mostValuable->value . '%'
                : 'AED ' . number_format($mostValuable->value, 2),
        ];
    }

    /**
     * Get coupons expiring soon (within 7 days)
     */
    private function getExpiringSoon(): array
    {
        $expiringSoon = $this->collection->filter(function ($coupon) {
            if (!$coupon->end_date) {
                return false;
            }
            
            $daysUntilExpiry = now()->diffInDays($coupon->end_date, false);
            return $daysUntilExpiry >= 0 && $daysUntilExpiry <= 7;
        });

        return $expiringSoon->map(function ($coupon) {
            return [
                'id' => $coupon->id,
                'code' => $coupon->code,
                'title' => $coupon->title,
                'end_date' => $coupon->end_date->format('Y-m-d H:i:s'),
                'days_until_expiry' => now()->diffInDays($coupon->end_date, false),
            ];
        })->values()->toArray();
    }

    /**
     * Additional pagination information when applicable
     */
    public function with(Request $request): array
    {
        return [
            'filters_applied' => $this->getAppliedFilters($request),
            'available_filters' => $this->getAvailableFilters(),
        ];
    }

    /**
     * Get applied filters from request
     */
    private function getAppliedFilters(Request $request): array
    {
        $filters = [];
        
        if ($request->filled('is_active')) {
            $filters['status'] = $request->boolean('is_active') ? 'active' : 'inactive';
        }
        
        if ($request->filled('type')) {
            $filters['type'] = $request->input('type');
        }
        
        if ($request->filled('vendor_id')) {
            $filters['vendor_id'] = $request->input('vendor_id');
        }
        
        if ($request->boolean('platform_wide')) {
            $filters['scope'] = 'platform_wide';
        }
        
        if ($request->boolean('valid_only')) {
            $filters['validity'] = 'valid_only';
        }
        
        return $filters;
    }

    /**
     * Get available filter options
     */
    private function getAvailableFilters(): array
    {
        return [
            'status' => ['active', 'inactive'],
            'type' => ['percentage', 'fixed'],
            'scope' => ['platform_wide', 'vendor_specific'],
            'validity' => ['all', 'valid_only', 'expired_only'],
        ];
    }
}
