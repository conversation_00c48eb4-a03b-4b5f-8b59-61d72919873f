<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title_en' => $this->title_en,
            'title_ar' => $this->title_ar,
            'description_en' => $this->description_en,
            'description_ar' => $this->description_ar,
            'type' => $this->type,
            'value' => $this->value,
            'formatted_value' => $this->getFormattedValue(),
            'min_order_value' => $this->min_order_value,
            'formatted_min_order_value' => $this->getFormattedMinOrderValue(),
            'usage_limit' => $this->usage_limit,
            'per_user_limit' => $this->per_user_limit,
            'start_date' => $this->start_date?->format('Y-m-d H:i:s'),
            'end_date' => $this->end_date?->format('Y-m-d H:i:s'),
            'is_active' => $this->is_active,
            
            // Computed attributes
            'is_expired' => $this->is_expired,
            'is_started' => $this->is_started,
            'usage_count' => $this->usage_count,
            'remaining_uses' => $this->remaining_uses,
            'is_valid' => $this->isValidForUse(),
            'status' => $this->getStatus(),
            'validity_period' => $this->getValidityPeriod(),
            
            // Relationships
            'vendor' => $this->when($this->relationLoaded('vendor'), function () {
                return [
                    'id' => $this->vendor?->id,
                    'name' => $this->vendor?->name,
                    'display_name_en' => $this->vendor?->display_name_en,
                    'display_name_ar' => $this->vendor?->display_name_ar,
                ];
            }),
            
            'user' => $this->when($this->relationLoaded('user'), function () {
                return [
                    'id' => $this->user?->id,
                    'name' => $this->user?->name,
                    'email' => $this->user?->email,
                ];
            }),
            
            // Metadata
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Get formatted discount value
     */
    private function getFormattedValue(): string
    {
        if ($this->type === 'percentage') {
            return $this->value . '%';
        }
        
        return 'AED ' . number_format($this->value, 2);
    }

    /**
     * Get formatted minimum order value
     */
    private function getFormattedMinOrderValue(): ?string
    {
        if (!$this->min_order_value) {
            return null;
        }
        
        return 'AED ' . number_format($this->min_order_value, 2);
    }

    /**
     * Get coupon status
     */
    private function getStatus(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }
        
        if ($this->is_expired) {
            return 'expired';
        }
        
        if (!$this->is_started) {
            return 'upcoming';
        }
        
        if ($this->usage_limit && $this->usage_count >= $this->usage_limit) {
            return 'exhausted';
        }
        
        return 'active';
    }

    /**
     * Get validity period description
     */
    private function getValidityPeriod(): string
    {
        if (!$this->start_date && !$this->end_date) {
            return 'No expiry';
        }
        
        if ($this->start_date && !$this->end_date) {
            return 'From ' . $this->start_date->format('M d, Y');
        }
        
        if (!$this->start_date && $this->end_date) {
            return 'Until ' . $this->end_date->format('M d, Y');
        }
        
        return $this->start_date->format('M d, Y') . ' - ' . $this->end_date->format('M d, Y');
    }
}
