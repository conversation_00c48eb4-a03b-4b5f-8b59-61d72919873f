<?php

namespace App\Http\Middleware;

use App\Models\CartSession;
use App\Models\ShoppingCart;
use App\Services\CartService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CartSessionMiddleware
{
    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Initialize or update cart session
        $this->handleCartSession($request);

        // Handle automatic cart migration for newly authenticated users
        $this->handleAutoMigration($request);

        return $next($request);
    }

    /**
     * Handle cart session management.
     */
    protected function handleCartSession(Request $request): void
    {
        $sessionId = session()->getId();

        if (!Auth::check()) {
            // For guest users, manage cart session
            $this->manageGuestCartSession($request, $sessionId);
        } else {
            // For authenticated users, ensure they have a user cart
            $this->ensureUserCart($request);
        }
    }

    /**
     * Manage guest cart session.
     */
    protected function manageGuestCartSession(Request $request, string $sessionId): void
    {
        // Check if we have an active cart session
        $cartSession = CartSession::where('session_id', $sessionId)
            ->where('is_migrated', false)
            ->first();

        if (!$cartSession) {
            // Check if there's a shopping cart for this session
            $shoppingCart = ShoppingCart::where('session_id', $sessionId)
                ->where('status', 'active')
                ->whereNull('user_id')
                ->first();

            if ($shoppingCart && !$shoppingCart->isEmpty()) {
                // Create cart session record for tracking
                $this->createCartSessionFromShoppingCart($shoppingCart, $request);
            }
        } else {
            // Update last accessed time
            $cartSession->update([
                'last_accessed_at' => now(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Extend expiry if close to expiration
            if ($cartSession->expires_at->diffInHours(now()) < 24) {
                $cartSession->extendExpiry(7); // Extend by 7 days
            }
        }

        // Store cart session info in request for controllers
        $request->attributes->set('cart_session_id', $sessionId);
    }

    /**
     * Ensure authenticated user has a cart.
     */
    protected function ensureUserCart(Request $request): void
    {
        $user = Auth::user();
        
        // Get or create user cart
        $userCart = ShoppingCart::where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        if (!$userCart) {
            $userCart = $this->cartService->createCart([
                'user_id' => $user->id,
                'expires_at' => now()->addDays(30),
            ]);
        }

        // Store user cart info in request
        $request->attributes->set('user_cart_id', $userCart->id);
        $request->attributes->set('user_cart_uuid', $userCart->uuid);
    }

    /**
     * Handle automatic cart migration when user logs in.
     */
    protected function handleAutoMigration(Request $request): void
    {
        // Only attempt migration if user just authenticated
        if (!Auth::check() || !$this->isRecentlyAuthenticated($request)) {
            return;
        }

        $sessionId = session()->getId();
        $user = Auth::user();

        try {
            // Check for guest cart to migrate
            $guestCart = $this->findGuestCartForMigration($sessionId);
            
            if ($guestCart) {
                $this->performAutoMigration($user, $guestCart, $sessionId);
            }
        } catch (\Exception $e) {
            // Log migration errors but don't break the request
            Log::warning('Auto cart migration failed', [
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if user was recently authenticated.
     */
    protected function isRecentlyAuthenticated(Request $request): bool
    {
        // Check if this is a login request or if user was authenticated in this session
        return $request->is('*/login') || 
               $request->is('*/auth/*') || 
               session()->has('recently_authenticated') ||
               !session()->has('user_cart_migrated');
    }

    /**
     * Find guest cart that needs migration.
     */
    protected function findGuestCartForMigration(string $sessionId): ?array
    {
        // First check cart_sessions table
        $cartSession = CartSession::where('session_id', $sessionId)
            ->where('is_migrated', false)
            ->first();

        if ($cartSession && $cartSession->items_count > 0) {
            return [
                'type' => 'session',
                'data' => $cartSession,
            ];
        }

        // Then check shopping_carts table
        $shoppingCart = ShoppingCart::where('session_id', $sessionId)
            ->where('status', 'active')
            ->whereNull('user_id')
            ->first();

        if ($shoppingCart && !$shoppingCart->isEmpty()) {
            return [
                'type' => 'cart',
                'data' => $shoppingCart,
            ];
        }

        return null;
    }

    /**
     * Perform automatic cart migration.
     */
    protected function performAutoMigration($user, array $guestCart, string $sessionId): void
    {
        $migratedCart = null;

        if ($guestCart['type'] === 'session') {
            $migratedCart = $guestCart['data']->migrateToUser($user);
        } elseif ($guestCart['type'] === 'cart') {
            $migratedCart = $this->cartService->migrateGuestCart($user, $sessionId, 'merge');
        }

        if ($migratedCart) {
            // Mark session as migrated to prevent duplicate migrations
            session()->put('user_cart_migrated', true);
            
            Log::info('Cart auto-migrated successfully', [
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'cart_id' => $migratedCart->id,
                'items_count' => $migratedCart->items_count,
            ]);
        }
    }

    /**
     * Create cart session from existing shopping cart.
     */
    protected function createCartSessionFromShoppingCart(ShoppingCart $cart, Request $request): CartSession
    {
        $cartData = [
            'items' => $cart->items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product_id' => $item->product_id,
                    'variant_id' => $item->variant_id,
                    'vendor_id' => $item->vendor_id,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                    'product_snapshot' => $item->product_snapshot,
                    'added_at' => $item->created_at->toISOString(),
                ];
            })->toArray(),
            'totals' => [
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'discount_amount' => $cart->discount_amount,
                'shipping_amount' => $cart->shipping_amount,
                'total_amount' => $cart->total_amount,
            ],
            'metadata' => $cart->metadata,
            'created_at' => $cart->created_at->toISOString(),
        ];

        return CartSession::create([
            'session_id' => $cart->session_id,
            'cart_data' => $cartData,
            'expires_at' => $cart->expires_at ?? now()->addDays(7),
            'last_accessed_at' => now(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'fingerprint' => $this->generateFingerprint($request),
        ]);
    }

    /**
     * Generate browser fingerprint for additional security.
     */
    protected function generateFingerprint(Request $request): string
    {
        $components = [
            $request->userAgent(),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding'),
            $request->ip(),
        ];

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Clean up expired cart sessions.
     */
    public function cleanupExpiredSessions(): int
    {
        return CartSession::where('expires_at', '<', now())
            ->where('is_migrated', false)
            ->delete();
    }

    /**
     * Get cart session statistics.
     */
    public function getSessionStatistics(): array
    {
        return [
            'active_sessions' => CartSession::active()->count(),
            'expired_sessions' => CartSession::expired()->count(),
            'migrated_sessions' => CartSession::where('is_migrated', true)->count(),
            'total_sessions' => CartSession::count(),
            'sessions_with_items' => CartSession::whereRaw('JSON_LENGTH(cart_data->"$.items") > 0')->count(),
        ];
    }
}
