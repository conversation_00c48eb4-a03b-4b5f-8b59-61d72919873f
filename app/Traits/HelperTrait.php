<?php

namespace App\Traits;

use App\Exceptions\ErrorMessageException;
use App\Models\Coupon;
use App\Models\Order;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Storage;

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Illuminate\Support\Str;

trait HelperTrait
{
    private function applySorting($query, Request $request): void
    {
        $sortBy = $request->input('sortBy', 'id');
        $sortDesc = $request->boolean('sortDesc', true) ? 'desc' : 'asc';
        $query->orderBy($sortBy, $sortDesc);
    }

    private function applySearch($query, ?string $searchValue, array $searchKeys): void
    {
        if ($searchValue) {
            $query->where(function ($query) use ($searchValue, $searchKeys) {
                foreach ($searchKeys as $key) {
                    $query->orWhereRaw('LOWER(' . $key . ') LIKE ?', ['%' . strtolower($searchValue) . '%']);
                }
            });
        }
    }

    private function paginateOrGet($query, Request $request, $optional = null): Collection|LengthAwarePaginator|array
    {
        if ($request->boolean('pagination', true)) {
            $itemsPerPage = $request->input('per_page', 10);
            $currentPage = Paginator::resolveCurrentPage('page');

            $items = $query->paginate($itemsPerPage, ['*'], 'page', $currentPage);

            return [
                'optional' => $optional ? $optional : null,
                'data' => $items->items(),
                'current_page' => $items->currentPage(),
                'per_page' => $items->perPage(),
                'total_items' => $items->total(),
                'total_pages' => $items->lastPage(),

            ];
        } elseif (!empty($request->input('per_page'))) {
            $query->take($request->input('per_page'));
        }

        return $query->get();
    }

    public function applyActive(Builder $query, Request $request): Builder
    {
        return $query->when($request->has('is_active'), function ($q) use ($request) {
            $isActive = filter_var($request->input('is_active'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if (! is_null($isActive)) {
                $q->where('is_active', $isActive);
            }
        });
    }
    private function applyFilters($query, $request, $filters)
    {
        foreach ($filters as $key => $operator) {
            if (!$request->filled($key)) {
                continue;
            }

            $value = $request->input($key);

            if (in_array($operator, ['=', 'like', '>', '<', '>=', '<='])) {
                $query->where($key, $operator, $operator === 'like' ? "%$value%" : $value);
            }
        }
    }

    protected function successResponse($data, $message, $statusCode = 200): JsonResponse
    {
        $array = [
            'status' => true,
            'data' => $data,
            'message' => $message,
        ];

        return response()->json($array, $statusCode);
    }

    protected function errorResponse($errors, $message, $statusCode, $error = null): JsonResponse
    {
        $array = [
            'status' => false,
            'error' => $error,
            'errors' => $errors,
            'message' => $message,
        ];

        return response()->json($array, $statusCode);
    }

    protected function s3FileUpload($request, $fileName, $path)
    {
        if (! $request->hasFile($fileName)) {
            return '';
        }

        try {
            $file = $request->file($fileName);
            $path = rtrim($path, '/') . '/';
            $extension = $file->getClientOriginalExtension();
            $uniqueName = Str::uuid() . '.' . $extension;
            $key = $path . $uniqueName;
            // Initialize S3 client
            $s3 = new S3Client([
                'version'     => 'latest',
                'region'      => config('filesystems.disks.s3.region', env('AWS_DEFAULT_REGION')),
                'credentials' => [
                    'key'    => config('filesystems.disks.s3.key', env('AWS_ACCESS_KEY_ID')),
                    'secret' => config('filesystems.disks.s3.secret', env('AWS_SECRET_ACCESS_KEY')),
                ],
            ]);

            // Upload the file manually (default is private)
            $s3->putObject([
                'Bucket'      => config('filesystems.disks.s3.bucket', env('AWS_BUCKET')),
                'Key'         => $key,
                'SourceFile'  => $file->getPathname(),
                'ContentType' => $file->getMimeType(),
            ]);

            // Generate a signed URL (valid for 60 minutes)
            $cmd = $s3->getCommand('GetObject', [
                'Bucket' => config('filesystems.disks.s3.bucket', env('AWS_BUCKET')),
                'Key'    => $key,
            ]);
            $presignedRequest = $s3->createPresignedRequest($cmd, '+60 minutes');
            $signedUrl = (string) $presignedRequest->getUri();

            return [
                'path'     => $key,
                'full_url' => $signedUrl,
            ];
        } catch (AwsException $e) {
            \Log::error('S3 Upload error: ' . $e->getAwsErrorMessage());
            throw new \Exception('S3 Upload error: ' . $e->getAwsErrorMessage());
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            throw new \Exception('File upload error: ' . $e->getMessage());
        }
    }

    // protected function s3FileUpload($request, $fileName, $path)
    // {
    //     if (! $request->hasFile($fileName)) {
    //         return '';
    //     }

    //     try {
    //         $file = $request->file($fileName);

    //         // Ensure the path has a trailing slash
    //         $path = rtrim($path, '/') . '/';

    //         // Upload the file to S3 and get the file path
    //         $filePath = Storage::putFile($path, $file, 'public');

    //         if ($filePath) {
    //             // Get the full URL of the uploaded file
    //             $fullUrl = Storage::url($filePath);

    //             return [
    //                 'path' => $filePath,
    //                 'full_url' => $fullUrl,
    //             ];
    //         }

    //         return '';
    //     } catch (\Exception $e) {
    //         \Log::error('File upload error: ' . $e->getMessage());
    //         throw new ErrorMessageException('File upload error: ' . $e->getMessage());
    //     }
    // }

    protected function localFileUpload($request, $fileName, $path): bool|string
    {
        if (! $request->hasFile($fileName)) {
            return '';
        }

        try {
            $file = $request->file($fileName);

            // Ensure the path has a trailing slash
            $path = rtrim($path, '/');

            // Upload the file to the local disk and get the file path
            $filePath = Storage::disk('public')->put($path, $file);

            if ($filePath) {
                // Get the full URL of the uploaded file
                return $filePath;
            }

            return '';
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            throw new ErrorMessageException('File upload error: ' . $e->getMessage());
        }
    }

    protected function uploadMultipleFiles(Request $request, $fileName, $path)
    {
        if (!$request->hasFile($fileName)) {
            return [];
        }

        try {
            $files = $request->file($fileName);

            // Ensure the path has a trailing slash
            $path = rtrim($path, '/');

            // Upload each file and store the file paths in an array
            $filePaths = [];
            foreach ($files as $file) {
                $filePaths[] = Storage::disk('public')->put($path, $file);
            }

            return $filePaths; // Return an array of file paths
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            throw new ErrorMessageException('File upload error: ' . $e->getMessage());
        }
    }

    protected function s3MultipleFileUpload(Request $request, $fileName, $path)
    {
        if (!$request->hasFile($fileName)) {
            return [];
        }

        try {
            $files = $request->file($fileName);
            $path = rtrim($path, '/') . '/';

            // Initialize S3 client
            $s3 = new S3Client([
                'version'     => 'latest',
                'region'      => config('filesystems.disks.s3.region', env('AWS_DEFAULT_REGION')),
                'credentials' => [
                    'key'    => config('filesystems.disks.s3.key', env('AWS_ACCESS_KEY_ID')),
                    'secret' => config('filesystems.disks.s3.secret', env('AWS_SECRET_ACCESS_KEY')),
                ],
            ]);

            $results = [];

            foreach ($files as $file) {
                $extension = $file->getClientOriginalExtension();
                $uniqueName = Str::uuid() . '.' . $extension;
                $key = $path . $uniqueName;

                // Upload the file manually (default is private)
                $s3->putObject([
                    'Bucket'      => config('filesystems.disks.s3.bucket', env('AWS_BUCKET')),
                    'Key'         => $key,
                    'SourceFile'  => $file->getPathname(),
                    'ContentType' => $file->getMimeType(),
                    // 'ACL'         => 'public-read', // Make it publicly accessible
                ]);

                // Generate a signed URL (valid for 60 minutes)
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => config('filesystems.disks.s3.bucket', env('AWS_BUCKET')),
                    'Key'    => $key,
                ]);
                $presignedRequest = $s3->createPresignedRequest($cmd, '+60 minutes');
                $signedUrl = (string) $presignedRequest->getUri();

                $results[] = [
                    'path'     => $key,
                    'full_url' => $signedUrl,
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ];
            }

            return $results; // Return an array of file information
        } catch (AwsException $e) {
            \Log::error('S3 Upload error: ' . $e->getAwsErrorMessage());
            throw new \Exception('S3 Upload error: ' . $e->getAwsErrorMessage());
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            throw new \Exception('File upload error: ' . $e->getMessage());
        }
    }

    protected function convertToClassName($input)
    {
        $parts = explode('_', $input);
        $classNameParts = array_map('ucfirst', $parts);

        return 'App\Models\\' . implode('', $classNameParts);
    }

    protected function modelToSnakeCase($input)
    {
        $input = str_replace('App\\Models\\', '', $input);

        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $input));
    }

    private function getArrayById(array $array): mixed
    {
        return array_reduce($array, function ($result, $item) {
            $result[$item['id']] = $item;

            return $result;
        }, []);
    }

    protected function camelToSnakeCase($input)
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $input));
    }

    protected function ftpFileUploadForPost($file, $fileName, $path): bool|string
    {
        try {

            // Ensure the directory exists
            if (! Storage::exists($path)) {
                Storage::makeDirectory($path);
            }

            $filePath = Storage::put($path, $file);
            $prefix = env('SFTP_ROOT', 'plc');
            if ($filePath) {
                return "{$prefix}/{$filePath}";
            }

            return '';
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            throw new ErrorMessageException('File upload error: ' . $e->getMessage());
        }
    }

    public function applyEComFilters(Builder $query, Request $request): Builder
    {
        if ($request->has('genre_id')) {
            $query->where('ebooks.genre_id', $request->input('genre_id'));
        }

        // if ($request->has('book_type')) {
        //     $query->where('ebooks.book_type', $request->input('book_type'));
        // }

        if ($request->has('min_price') && $request->has('max_price')) {
            $query->whereBetween('ebooks.final_price', [$request->input('min_price'), $request->input('max_price')]);
        }

        if ($request->has('discounted')) {
            $query->where('ebooks.discount', '>', 0);
        }

        // Multi-Author Filtering (Fixing ID ambiguity)
        if ($request->has('authors')) {
            $authorIds = explode(',', $request->input('authors'));
            $query->whereHas('authors', function ($q) use ($authorIds) {
                $q->whereIn('authors.id', $authorIds);
            });
        }
        // multi-category filtering
        if ($request->has('categories')) {
            $categoryIds = explode(',', $request->input('categories'));
            $query->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        if ($request->has('rating')) {
            $query->whereIn('ebooks.id', function ($subQuery) use ($request) {
                $subQuery->selectRaw('book_id')
                    ->from('reviews')
                    ->groupBy('book_id')
                    ->havingRaw('AVG(rating) >= ?', [$request->input('rating')]);
            });
        }

        return $query;
    }

    public function applyEComSorting(Builder $query, Request $request): Builder
    {
        $sortType = $request->input('sort');

        switch ($sortType) {
            case 'best_selling':
                $query->orderBy('sales_count', 'desc');
                break;
            case 'price_low':
                $query->orderBy('final_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('final_price', 'desc');
                break;
            case 'most_reviewed':
                $query->orderBy('reviews_count', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        return $query;
    }

/*    public function applyCoupon(?string $couponCode, float $subtotal, array $items): array
    {
        $discount = 0;
        $coupon = null;
        $userId = auth()->id();

        if (!$couponCode) {
            return ['discount' => 0, 'coupon' => null];
        }

        // Fetch coupon details
        $coupon = Coupon::where('code', $couponCode)
            ->where('is_active', 1)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();

        if (!$coupon) {
            return ['discount' => 0, 'coupon' => null];
        }

        // Check if the coupon has already been used in an active order
        $existingOrder = Order::where('user_id', $userId)
            ->where('coupon_id', $coupon->id)
            ->whereNotIn('status', ['cancelled']) // Exclude cancelled orders
            ->exists();

        if ($existingOrder) {
            return ['discount' => 0, 'coupon' => null]; // Coupon has already been used in an active order
        }

        // Apply coupon based on type
        if ($coupon->applies_to === 'order') {
            // Order-wide discount
            if ($coupon->discount_type === 'percentage') {
                $discount = ($subtotal * $coupon->discount_value) / 100;
            } else {
                $discount = min($coupon->discount_value, $subtotal);
            }
        } elseif ($coupon->applies_to === 'item' && $coupon->item_id) {
            // Item-specific discount
            foreach ($items as &$item) {
                if ($item['ebook']->id == $coupon->item_id) {
                    if ($coupon->discount_type === 'percentage') {
                        $discount = ($item['subtotal'] * $coupon->discount_value) / 100;
                    } else {
                        $discount = min($coupon->discount_value, $item['subtotal']);
                    }
                    break; // Apply only once
                }
            }
        }

        // Enforce max discount
        if ($coupon->max_discount) {
            $discount = min($discount, $coupon->max_discount);
        }

        // Check minimum order amount
        if ($coupon->min_order_amount && $subtotal < $coupon->min_order_amount) {
            $discount = 0;
        }

        return ['discount' => $discount, 'coupon' => $coupon];
    }*/
}
