<?php

namespace App\Traits;

use libphonenumber\PhoneNumberUtil;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\NumberParseException;

class PhoneHelper
{
    public static function normalize(string $number, string $region = 'BD'): ?string // TODO:: need to change region based on your requirements
    {
        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $proto = $phoneUtil->parse($number, $region);

            if ($phoneUtil->isValidNumber($proto)) {
                return $phoneUtil->format($proto, PhoneNumberFormat::E164); // +8801712345678
            }
        } catch (NumberParseException $e) {
            return null;
        }

        return null;
    }
}

