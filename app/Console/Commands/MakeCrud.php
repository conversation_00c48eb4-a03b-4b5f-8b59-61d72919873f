<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class MakeCrud extends Command
{
    protected $signature = 'make:crud {name} {--fillable=}';

    protected $description = 'Create a new CRUD operations including controller, service, model, and request files';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $name = $this->argument('name');

        // Generate the model and request files
        $this->info('Creating model and request files...');
        Artisan::call('make:model-from-migration', ['name' => $name]);
        $this->info(Artisan::output());

        // Generate the service
        $this->info('Creating service...');
        Artisan::call('make:service', ['name' => $name.'Service']);
        $this->info(Artisan::output());

        // Generate the controller
        $this->info('Creating controller...');
        Artisan::call('make:custom-controller', ['name' => $name]);
        $this->info(Artisan::output());

        // Add the route and namespace to the routes file
        $this->addRouteAndNamespace($name);

        // Clear the route cache
        Artisan::call('route:clear');
        $this->info('Routes cache cleared.');

        $this->info('CRUD operations created successfully.');
    }

    protected function addRouteAndNamespace($name)
    {
        $routeFile = base_path('routes/api.php');
        $controllerName = $name.'Controller';
        $namespaceLine = "use App\\Http\\Controllers\\$controllerName;";
        $routeSnippet = "        Route::apiResource('".strtolower($name)."s', $controllerName::class);";

        $content = File::get($routeFile);

        if (strpos($content, $namespaceLine) === false) {
            $useStatementsEnd = strpos($content, 'use Illuminate\Support\Facades\Route;') + strlen('use Illuminate\Support\Facades\Route;');
            $content = substr_replace($content, "\n$namespaceLine", $useStatementsEnd, 0);
        }

        $adminGroupPosition = strpos($content, "Route::prefix('admin')->middleware('role:admin')->group(function () {");

        if ($adminGroupPosition !== false) {
            $bracePosition = strpos($content, '{', $adminGroupPosition);
            $insertionPoint = $bracePosition + 1;
            $content = substr_replace($content, "\n$routeSnippet", $insertionPoint, 0);
        }

        File::put($routeFile, $content);

        $this->info('Route and namespace added successfully.');
    }
}
