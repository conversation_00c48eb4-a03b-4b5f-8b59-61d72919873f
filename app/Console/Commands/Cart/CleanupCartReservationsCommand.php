<?php

namespace App\Console\Commands\Cart;

use App\Jobs\Cart\CleanupExpiredCartReservationsJob;
use Illuminate\Console\Command;

class CleanupCartReservationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cart:cleanup-reservations
                            {--queue : Dispatch job to queue instead of running synchronously}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up expired cart item reservations';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Cleaning up expired cart reservations...');

        if ($this->option('queue')) {
            CleanupExpiredCartReservationsJob::dispatch();
            $this->info('Cart reservations cleanup job dispatched to queue.');
        } else {
            $job = new CleanupExpiredCartReservationsJob();
            $job->handle();
            $this->info('Cart reservations cleanup completed.');
        }

        return Command::SUCCESS;
    }
}
