<?php

namespace App\Console\Commands\Cart;

use App\Jobs\Cart\ProcessAbandonedCartsJob;
use Illuminate\Console\Command;

class ProcessAbandonedCartsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cart:process-abandoned
                            {--queue : Dispatch job to queue instead of running synchronously}';

    /**
     * The console command description.
     */
    protected $description = 'Process abandoned carts and send reminder emails';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing abandoned carts...');

        if ($this->option('queue')) {
            ProcessAbandonedCartsJob::dispatch();
            $this->info('Abandoned cart processing job dispatched to queue.');
        } else {
            $job = new ProcessAbandonedCartsJob();
            $job->handle();
            $this->info('Abandoned cart processing completed.');
        }

        return Command::SUCCESS;
    }
}
