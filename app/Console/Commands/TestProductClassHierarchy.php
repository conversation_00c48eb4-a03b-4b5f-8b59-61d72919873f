<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\ProductClass;
use Illuminate\Console\Command;

class TestProductClassHierarchy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:product-class-hierarchy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the product class hierarchy relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Product Class Hierarchy Relationships...');
        $this->newLine();

        // Test 1: Check if categories exist
        $this->info('1. Checking Categories...');
        $mainCategories = Category::where('type', 'main')->whereNull('parent_id')->count();
        $subCategories = Category::where('type', 'sub')->whereNotNull('parent_id')->count();
        
        $this->line("   Main Categories: {$mainCategories}");
        $this->line("   Sub Categories: {$subCategories}");
        
        if ($mainCategories === 0 || $subCategories === 0) {
            $this->error('   ❌ Categories not properly seeded!');
            return;
        }
        $this->info('   ✅ Categories look good');
        $this->newLine();

        // Test 2: Check product classes
        $this->info('2. Checking Product Classes...');
        $totalClasses = ProductClass::count();
        $topLevelClasses = ProductClass::whereNull('parent_id')->count();
        $subClasses = ProductClass::whereNotNull('parent_id')->count();
        
        $this->line("   Total Product Classes: {$totalClasses}");
        $this->line("   Top-level Classes: {$topLevelClasses}");
        $this->line("   Sub Classes: {$subClasses}");
        
        if ($totalClasses === 0) {
            $this->error('   ❌ No product classes found!');
            return;
        }
        $this->info('   ✅ Product classes exist');
        $this->newLine();

        // Test 3: Check relationships
        $this->info('3. Testing Relationships...');
        
        // Check category relationships
        $invalidCategoryRels = ProductClass::whereDoesntHave('category')->count();
        $invalidSubCategoryRels = ProductClass::whereNotNull('sub_category_id')
            ->whereDoesntHave('subCategory')->count();
            
        if ($invalidCategoryRels > 0) {
            $this->error("   ❌ {$invalidCategoryRels} product classes have invalid category relationships");
        } else {
            $this->info('   ✅ All category relationships are valid');
        }
        
        if ($invalidSubCategoryRels > 0) {
            $this->error("   ❌ {$invalidSubCategoryRels} product classes have invalid subcategory relationships");
        } else {
            $this->info('   ✅ All subcategory relationships are valid');
        }
        $this->newLine();

        // Test 4: Show sample hierarchy
        $this->info('4. Sample Hierarchy:');
        $sampleCategory = Category::where('type', 'main')->first();
        
        if ($sampleCategory) {
            $this->line("   📁 {$sampleCategory->name} (Main Category)");
            
            $subcategories = $sampleCategory->children()->take(2)->get();
            foreach ($subcategories as $subcategory) {
                $this->line("   ├── 📂 {$subcategory->name} (Sub Category)");
                
                $classes = ProductClass::where('category_id', $sampleCategory->id)
                    ->where('sub_category_id', $subcategory->id)
                    ->whereNull('parent_id')
                    ->take(2)
                    ->get();
                    
                foreach ($classes as $class) {
                    $this->line("   │   ├── 📋 {$class->name} (Class)");
                    
                    $subclasses = $class->subClasses()->take(2)->get();
                    foreach ($subclasses as $subclass) {
                        $this->line("   │   │   └── 📄 {$subclass->name} (Subclass)");
                    }
                }
            }
        }
        
        $this->newLine();
        $this->info('✅ Hierarchy test completed!');
    }
}
