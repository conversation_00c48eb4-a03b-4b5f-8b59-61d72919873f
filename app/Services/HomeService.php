<?php

namespace App\Services;

use App\Models\Banner;
use App\Models\Brand;
use App\Models\Category;
use App\Models\OfferAndDeal;
use App\Models\Product;
use App\Models\ProductClass;
use App\Traits\HelperTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomeService
{
    use HelperTrait;

    private $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Get complete home page data
     */
    public function getHomePageData(Request $request): array
    {
        return [
            'banners' => $this->getPromotionalBanners($request),
            'latest_products' => $this->getLatestProducts($request),
            'categories' => $this->getCategoriesWithProductCounts($request),
            'best_sellers' => $this->getBestSellersByCategory($request),
            'discount_products' => $this->getDiscountProducts($request),
            'discounted_product_banners' => $this->getDiscountedProductBanners($request),
            'featured_products' => $this->getFeaturedProducts($request),
            'popular_brands' => $this->getPopularBrands($request),
            'trending_products' => $this->getTrendingProducts($request),
            'brands' => $this->getBrands($request),
            'recently_viewed_products' => $this->getRecentViewedProducts($request),
            'offer_and_deals' => $this->getOfferAndDeals($request),

        ];
    }

    private function getOfferAndDeals(Request $request)
    {
        $offer = OfferAndDeal::where('is_active', true)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    // Offers with specific time range
                    $subQuery->whereNotNull('start_time')
                        ->whereNotNull('end_time')
                        ->where('start_time', '<=', now())
                        ->where('end_time', '>=', now());
                })
                    ->orWhere(function ($subQuery) {
                        // All-time offers (null dates)
                        $subQuery->whereNull('start_time')
                            ->whereNull('end_time');
                    })
                    ->orWhere(function ($subQuery) {
                        // Mixed cases - null start_time OR null end_time
                        $subQuery->whereNull('start_time')
                            ->whereNotNull('end_time')
                            ->where('end_time', '>=', now());
                    })
                    ->orWhere(function ($subQuery) {
                        $subQuery->whereNotNull('start_time')
                            ->whereNull('end_time')
                            ->where('start_time', '<=', now());
                    });
            })
            ->select(['id', 'title_en', 'title_ar', 'tag', 'description_en', 'description_ar', 'image', 'link', 'type', 'regular_price', 'offer_price', 'discount_percentage', 'start_time', 'end_time', 'is_active'])
            ->get();

        return $offer;
    }




    private function getRecentViewedProducts(Request $request)
    {

        $recentlyViewed = Product::select(['id', 'uuid', 'title_en', 'title_ar', 'short_name', 'regular_price', 'offer_price'])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->limit(10) // Example limit, adjust as needed
            ->with(['productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }])
            ->get();

        return $recentlyViewed;
    }

    private function getDiscountProducts(Request $request)
    {
        $discountProducts = Product::select(['id', 'uuid', 'title_en', 'title_ar', 'short_name', 'regular_price', 'offer_price'])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->limit(10) // Example limit, adjust as needed
            ->with(['productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }])
            ->get();

        return $discountProducts;
    }

    private function getDiscountedProductBanners(Request $request)
    {
        return Cache::remember('discounted_product_banners', 100, function () {
            $s3Url = config('filesystems.disks.s3.url');

            return Banner::with(['items' => function ($query) {
                $query->where('is_active', true)
                    ->orderBy('position')
                    ->limit(2)
                    ->select(['id', 'banner_id', 'media_path', 'link_url', 'alt_text', 'position']);
            }])
                ->where('is_active', true)
                // ->where('slug', 'discount')
                ->select(['id', 'title', 'description'])
                ->get()
                ->map(function ($banner) use ($s3Url) {
                    return [
                        'id' => $banner->id,
                        'title' => $banner->title,
                        'description' => $banner->description,
                        'items' => $banner->items->take(2)->map(function ($item) use ($s3Url) {
                            return [
                                'id' => $item->id,
                                'media_url' => $s3Url . '/' . $item->media_path,
                                'link_url' => $item->link_url,
                                'alt_text' => $item->alt_text,
                                'position' => $item->position,
                            ];
                        }),
                    ];
                })
                ->toArray();
        });
    }


    private function getTrendingProducts(Request $request)
    {
        $tendingProducts = Product::select(['id', 'uuid',  'title_en', 'title_ar', 'short_name', 'regular_price', 'offer_price'])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->limit(10) // Example limit, adjust as needed
            ->with(['productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }])
            ->get();

        return $tendingProducts;
    }

    private function getBrands(Request $request)
    {
        $limit = $request->input('limit', 6);
        return Brand::select(['id', 'name_en', 'name_ar', 'slug', 'logo'])
            ->where('is_active', true)
            ->limit($limit)
            ->get();
    }

    /**
     * Get featured/popular products
     */
    public function getFeaturedProducts(Request $request): array
    {
        $limit = $request->input('limit', 10);

        $products = Product::with([
            'category:id,name_en,name_ar,code',
            'productClass:id,name_en,name_ar,code',
            'brand:id,name_en,name_ar',
            'productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }
        ])
            ->where('is_active', true)
            ->where('is_approved', true)

            //TODO: Uncomment when product class is implemented

            // ->whereHas('productClass', function ($query) {
            //     $query->where('is_popular', true);
            // })
            ->select([
                'id',
                'uuid',
                'title_en',
                'title_ar',
                'short_name',
                'regular_price',
                'offer_price',
                'category_id',
                'class_id',
                'brand_id',
                'is_vegan',
                'is_vegetarian',
                'is_halal'
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $this->formatProductsForHomePage($products);
    }

    /**
     * Get latest products
     */
    public function getLatestProducts(Request $request): array
    {
        $limit = $request->input('limit', 10);

        $products = Product::with([
            'category:id,name_en,name_ar,code',
            'productClass:id,name_en,name_ar,code',
            'brand:id,name_en,name_ar',
            'productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }
        ])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->select([
                'id',
                'uuid',
                'title_en',
                'title_ar',
                'short_name',
                'regular_price',
                'offer_price',
                'category_id',
                'class_id',
                'brand_id',
                'is_vegan',
                'is_vegetarian',
                'is_halal'
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $this->formatProductsForHomePage($products);
    }

    /**
     * Get categories with product counts
     */
    public function getCategoriesWithProductCounts(Request $request): array
    {
        $limit = $request->input('limit', 8);

        return Cache::remember('categories_with_counts', 600, function () use ($limit) { // Cache for 10 minutes
            return Category::withCount(['products' => function ($query) {
                $query->where('is_active', true)
                    ->where('is_approved', true)
                ;
            }])

                ->whereNull('parent_id') // Only parent categories
                ->select(['id', 'name_en', 'name_ar', 'slug', 'icon', 'ordering_number'])
                ->orderBy('ordering_number')
                ->limit($limit)
                ->get()
                ->map(function ($category) {
                    return [
                        'id' => $category->id,
                        'name_en' => $category->name_en,
                        'name_ar' => $category->name_ar,
                        'slug' => $category->slug,
                        'icon_url' => $category->icon_url,
                        'products_count' => $category->products_count,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get promotional banners
     */
    public function getPromotionalBanners(Request $request): array
    {
        // return Cache::remember('promotional_banners', 1800, function () { // Cache for 30 minutes
        return Banner::with(['items' => function ($query) {
            $query->where('is_active', true)
                ->orderBy('position')
                ->select(['id', 'banner_id', 'media_path', 'link_url', 'alt_text', 'position']);
        }])
            ->where('is_active', true)
            ->select(['id', 'title', 'description'])
            ->get()
            ->map(function ($banner) {
                return [
                    'id' => $banner->id,
                    'title' => $banner->title,
                    'description' => $banner->description,
                    'items' => $banner->items->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'media_url' => config('filesystems.disks.s3.url') . '/' . $item->media_path,
                            'link_url' => $item->link_url,
                            'alt_text' => $item->alt_text,
                            'position' => $item->position,
                        ];
                    }),
                ];
            })
            ->toArray();
        // });
    }

    /**
     * Get best sellers by category
     */
    public function getBestSellersByCategory(Request $request): array
    {
        $categories = Category::where('status', 'active')
            ->whereNull('parent_id')
            ->select(['id', 'name_en', 'name_ar'])
            ->limit(2) // Limit to 2 categories as shown in design
            ->get();

        $result = [];

        foreach ($categories as $category) {
            $products = Product::with([
                'category:id,name_en,name_ar,code',
                'productClass:id,name_en,name_ar,code',
                'brand:id,name_en,name_ar',
                'productMedia' => function ($query) {
                    $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
                }
            ])
                ->where('category_id', $category->id)
                ->where('is_active', true)
                ->where('is_approved', true)
                ->select([
                    'id',
                    'uuid',
                    'title_en',
                    'title_ar',
                    'short_name',
                    'regular_price',
                    'offer_price',
                    'category_id',
                    'class_id',
                    'brand_id'
                ])
                ->orderBy('created_at', 'desc') // In real scenario, this would be ordered by sales
                ->limit(4)
                ->get();

            $result[] = [
                'category' => [
                    'id' => $category->id,
                    'name_en' => $category->name_en,        // Fixed: use name_en instead of name
                    'name_ar' => $category->name_ar,
                ],
                'products' => $this->formatProductsForHomePage($products),
            ];
        }

        return $result;
    }

    /**
     * Get product recommendations
     */
    public function getProductRecommendations(Request $request): array
    {
        $limit = $request->input('limit', 8);

        // For now, return random popular products
        // In a real scenario, this would be based on user behavior, purchase history, etc.
        $products = Product::with([
            'category:id,name_en,name_ar,code',
            'productClass:id,name_en,name_ar,code',
            'brand:id,name_en,name_ar',
            'productMedia' => function ($query) {
                $query->where('is_primary', true)->select('id', 'product_id', 'path', 'alt_text');
            }
        ])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->select([
                'id',
                'uuid',
                'title_en',
                'title_ar',
                'short_name',
                'regular_price',
                'offer_price',
                'category_id',
                'class_id',
                'brand_id'
            ])
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        return $this->formatProductsForHomePage($products);
    }

    /**
     * Get popular brands
     */
    public function getPopularBrands(Request $request): array
    {
        $limit = $request->input('limit', 6);

        return Cache::remember('popular_brands', 100, function () use ($limit) { // Cache for 30 minutes
            $brands = Brand::where('is_active', true)
                ->select(['id', 'name_en', 'name_ar', 'slug', 'logo'])
                ->get()
                ->take($limit);

            return $brands->map(function ($brand) {
                return [
                    'id' => $brand->id,
                    'name' => $brand->name_en,
                    'name_ar' => $brand->name_ar,
                    'slug' => $brand->slug,
                    'logo_url' => $brand->logo ? config('filesystems.disks.s3.url') . '/' . $brand->logo : null,
                ];
            })->values()->toArray();
        });
    }

    /**
     * Format products for home page display
     */
    private function formatProductsForHomePage($products): array
    {
        return $products->map(function ($product) {
            $primaryImage = $product->productMedia->first();

            return [
                'id' => $product->id,
                'uuid' => $product->uuid,
                'name_en' => $product->title_en,
                'name_ar' => $product->title_ar,
                'short_name' => $product->short_name,
                'regular_price' => $product->regular_price,
                'offer_price' => $product->offer_price,
                'has_discount' => $product->offer_price && $product->offer_price < $product->regular_price,
                'discount_percentage' => $product->offer_price && $product->regular_price > 0
                    ? round((($product->regular_price - $product->offer_price) / $product->regular_price) * 100)
                    : 0,
                'image_url' => $primaryImage ? $primaryImage->path_url : null,
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ] : null,
                'product_class' => $product->productClass ? [
                    'id' => $product->productClass->id,
                    'name' => $product->productClass->name,
                ] : null,
                'brand' => $product->brand ? [
                    'id' => $product->brand->id,
                    'name' => $product->brand->name_en,
                ] : null,
                'dietary_info' => [
                    'is_vegan' => $product->is_vegan ?? false,
                    'is_vegetarian' => $product->is_vegetarian ?? false,
                    'is_halal' => $product->is_halal ?? false,
                ],
            ];
        })->toArray();
    }
}
