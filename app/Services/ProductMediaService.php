<?php

namespace App\Services;

use App\Models\ProductMedia;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductMediaService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = ProductMedia::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareProductMediaData($request);

        return ProductMedia::create($data);
    }

    private function prepareProductMediaData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new ProductMedia())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'productMedia')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'productMedia');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'productMedia');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ProductMedia
    {
        return ProductMedia::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $productMedia = ProductMedia::findOrFail($id);
        $updateData = $this->prepareProductMediaData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $productMedia->update($updateData);

        return $productMedia;
    }

    public function storeUpdate($request)
    {
        $mediaItems = $request->input('media');
        $productId = $mediaItems[0]['product_id'] ?? null;

        $data = [];
        $incomingUrls = [];

        DB::transaction(function () use (&$data, $mediaItems, $productId, &$incomingUrls) {
            foreach ($mediaItems as $media) {
                $incomingUrls[] = $media['path'];

                $data[] = [
                    'product_id'          => $media['product_id'],
                    'product_variant_id'  => $media['product_variant_id'] ?? null,
                    'type'                => $media['type'],
                    'path'                => $media['path'],
                    'title'               => $media['title'] ?? null,
                    'alt_text'            => $media['alt_text'] ?? null,
                    'lang_code'           => $media['lang_code'] ?? null,
                    'position'            => $media['position'] ?? 0,
                    'is_primary'          => $media['is_primary'] ?? 0,
                    'created_at'          => now(),
                    'updated_at'          => now(),
                ];
            }

            // Upsert incoming media data (insert or update, including is_primary)
            ProductMedia::upsert(
                $data,
                ['product_id', 'path'],  // unique keys
                ['type', 'product_variant_id', 'title', 'alt_text', 'lang_code', 'position', 'is_primary', 'updated_at']
            );

            // Delete any media NOT in the incoming paths for this product
            ProductMedia::where('product_id', $productId)
                ->whereNotIn('path', $incomingUrls)
                ->delete();
        });

        return $data;
    }



    public function destroy(int $id): bool
    {
        $productMedia = ProductMedia::findOrFail($id);
        return $productMedia->delete();
    }
}
