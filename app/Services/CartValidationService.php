<?php

namespace App\Services;

use App\Models\CartItem;
use App\Models\CartReservation;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use Illuminate\Support\Collection;

class CartValidationService
{
    /**
     * Validate adding an item to cart.
     */
    public function validateAddToCart(ShoppingCart $cart, array $itemData): void
    {
        $product = Product::find($itemData['product_id']);
        
        if (!$product) {
            throw new \Exception('Product not found.');
        }

        if ($product->status !== 'active') {
            throw new \Exception('Product is not available.');
        }

        // Validate variant if specified
        if (isset($itemData['variant_id'])) {
            $variant = ProductVariant::find($itemData['variant_id']);
            
            if (!$variant || $variant->product_id !== $product->id) {
                throw new \Exception('Invalid product variant.');
            }

            if ($variant->status !== 'active') {
                throw new \Exception('Product variant is not available.');
            }
        }

        // Validate quantity constraints
        $this->validateQuantityConstraints($product, $itemData['quantity']);

        // Validate stock availability
        $this->validateStockAvailability($product, $itemData);

        // Validate vendor status
        $this->validateVendorStatus($product->vendor);

        // Validate cart limits
        $this->validateCartLimits($cart, $itemData);
    }

    /**
     * Validate quantity update for existing cart item.
     */
    public function validateQuantityUpdate(CartItem $cartItem, int $newQuantity): void
    {
        $product = $cartItem->product;
        
        if (!$product) {
            throw new \Exception('Product not found.');
        }

        // Validate quantity constraints
        $this->validateQuantityConstraints($product, $newQuantity);

        // Validate stock availability
        $this->validateStockAvailabilityForUpdate($cartItem, $newQuantity);
    }

    /**
     * Validate quantity constraints based on product settings.
     */
    protected function validateQuantityConstraints(Product $product, int $quantity): void
    {
        // Check minimum quantity
        if ($product->min_cart_quantity && $quantity < $product->min_cart_quantity) {
            throw new \Exception("Minimum quantity for {$product->name_en} is {$product->min_cart_quantity}.");
        }

        // Check maximum quantity
        if ($product->max_cart_quantity && $quantity > $product->max_cart_quantity) {
            throw new \Exception("Maximum quantity for {$product->name_en} is {$product->max_cart_quantity}.");
        }

        // Check quantity increment
        if ($product->cart_increment && $product->cart_increment > 1) {
            if ($quantity % $product->cart_increment !== 0) {
                throw new \Exception("Quantity for {$product->name_en} must be in increments of {$product->cart_increment}.");
            }
        }
    }

    /**
     * Validate stock availability for new items.
     */
    protected function validateStockAvailability(Product $product, array $itemData): void
    {
        $requestedQuantity = $itemData['quantity'];
        $variantId = $itemData['variant_id'] ?? null;

        // Get available stock
        $availableStock = $this->getAvailableStock($product, $variantId);

        // Check if product allows backorders
        if (!$product->allow_backorder && $requestedQuantity > $availableStock) {
            $productName = $variantId ? 
                ProductVariant::find($variantId)?->name ?? $product->name_en : 
                $product->name_en;

            if ($availableStock === 0) {
                throw new \Exception("{$productName} is currently out of stock.");
            } else {
                throw new \Exception("Only {$availableStock} units of {$productName} are available.");
            }
        }
    }

    /**
     * Validate stock availability for cart item updates.
     */
    protected function validateStockAvailabilityForUpdate(CartItem $cartItem, int $newQuantity): void
    {
        $product = $cartItem->product;
        $variantId = $cartItem->variant_id;

        // Get available stock (excluding current cart item)
        $availableStock = $this->getAvailableStock($product, $variantId, $cartItem->id);

        // Check if product allows backorders
        if (!$product->allow_backorder && $newQuantity > $availableStock) {
            $productName = $cartItem->variant ? 
                $cartItem->variant->name : 
                $product->name_en;

            if ($availableStock === 0) {
                throw new \Exception("{$productName} is currently out of stock.");
            } else {
                throw new \Exception("Only {$availableStock} units of {$productName} are available.");
            }
        }
    }

    /**
     * Get available stock for a product/variant.
     */
    protected function getAvailableStock(Product $product, ?int $variantId = null, ?int $excludeCartItemId = null): int
    {
        // Get base stock
        if ($variantId) {
            $variant = ProductVariant::find($variantId);
            $baseStock = $variant ? $variant->stock_quantity : 0;
        } else {
            $baseStock = $product->stock_quantity ?? 0;
        }

        // Subtract quantities in other active carts
        $cartItemsQuery = CartItem::where('product_id', $product->id)
            ->where('variant_id', $variantId)
            ->whereHas('cart', function ($query) {
                $query->where('status', 'active');
            });

        if ($excludeCartItemId) {
            $cartItemsQuery->where('id', '!=', $excludeCartItemId);
        }

        $reservedInCarts = $cartItemsQuery->sum('quantity');

        // Subtract active reservations
        $reservedQuantity = CartReservation::getTotalReservedQuantity($product->id, $variantId);

        return max(0, $baseStock - $reservedInCarts - $reservedQuantity);
    }

    /**
     * Validate vendor status.
     */
    protected function validateVendorStatus($vendor): void
    {
        if (!$vendor) {
            throw new \Exception('Vendor not found.');
        }

        if ($vendor->status !== 'active') {
            throw new \Exception('Vendor is currently not accepting orders.');
        }
    }

    /**
     * Validate cart limits and constraints.
     */
    protected function validateCartLimits(ShoppingCart $cart, array $itemData): void
    {
        // Check maximum items per cart
        $maxItemsPerCart = config('cart.max_items_per_cart', 100);
        if ($cart->items()->count() >= $maxItemsPerCart) {
            throw new \Exception("Maximum {$maxItemsPerCart} items allowed per cart.");
        }

        // Check maximum cart value
        $maxCartValue = config('cart.max_cart_value', 50000); // AED 50,000
        $currentValue = $cart->subtotal;
        $itemValue = $itemData['quantity'] * ($itemData['unit_price'] ?? 0);
        
        if (($currentValue + $itemValue) > $maxCartValue) {
            throw new \Exception("Cart value cannot exceed AED {$maxCartValue}.");
        }

        // Check duplicate items (same product + variant)
        $existingItem = $cart->items()
            ->where('product_id', $itemData['product_id'])
            ->where('variant_id', $itemData['variant_id'] ?? null)
            ->first();

        if ($existingItem) {
            // This is handled in the service layer by updating quantity
            // Just validate the combined quantity would be valid
            $product = Product::find($itemData['product_id']);
            $combinedQuantity = $existingItem->quantity + $itemData['quantity'];
            $this->validateQuantityConstraints($product, $combinedQuantity);
        }
    }

    /**
     * Validate entire cart for checkout.
     */
    public function validateCartForCheckout(ShoppingCart $cart): array
    {
        $errors = [];
        $warnings = [];

        if ($cart->isEmpty()) {
            $errors[] = 'Cart is empty.';
            return ['errors' => $errors, 'warnings' => $warnings];
        }

        // Validate each cart item
        foreach ($cart->items as $item) {
            $itemErrors = $this->validateCartItemForCheckout($item);
            if (!empty($itemErrors)) {
                $errors = array_merge($errors, $itemErrors);
            }
        }

        // Validate vendor minimum order values
        $vendorGroups = $cart->items->groupBy('vendor_id');
        foreach ($vendorGroups as $vendorId => $items) {
            $vendor = $items->first()->vendor;
            $vendorSubtotal = $items->sum('total_price');

            if ($vendor->min_order_value && $vendorSubtotal < $vendor->min_order_value) {
                $warnings[] = "Minimum order value for {$vendor->vendor_display_name_en} is AED {$vendor->min_order_value}. Current: AED {$vendorSubtotal}.";
            }
        }

        // Validate cart totals
        if ($cart->total_amount <= 0) {
            $errors[] = 'Cart total must be greater than zero.';
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'is_valid' => empty($errors),
        ];
    }

    /**
     * Validate individual cart item for checkout.
     */
    protected function validateCartItemForCheckout(CartItem $item): array
    {
        $errors = [];
        $product = $item->product;
        $variant = $item->variant;

        // Check product availability
        if (!$product || $product->status !== 'active') {
            $errors[] = "Product '{$item->product_name}' is no longer available.";
            return $errors;
        }

        // Check variant availability
        if ($item->variant_id && (!$variant || $variant->status !== 'active')) {
            $errors[] = "Product variant '{$item->product_name}' is no longer available.";
            return $errors;
        }

        // Check vendor status
        if (!$item->vendor || $item->vendor->status !== 'active') {
            $errors[] = "Vendor for '{$item->product_name}' is currently not accepting orders.";
            return $errors;
        }

        // Check stock availability
        try {
            $this->validateStockAvailabilityForUpdate($item, $item->quantity);
        } catch (\Exception $e) {
            $errors[] = $e->getMessage();
        }

        // Check price changes
        $currentPrice = $variant ? $variant->price : $product->price;
        if (abs($currentPrice - $item->unit_price) > 0.01) {
            $errors[] = "Price for '{$item->product_name}' has changed. Please update your cart.";
        }

        return $errors;
    }

    /**
     * Validate coupon application.
     */
    public function validateCouponApplication(ShoppingCart $cart, string $couponCode): void
    {
        $coupon = \App\Models\Coupon::where('code', $couponCode)->first();
        
        if (!$coupon) {
            throw new \Exception('Coupon not found.');
        }

        if (!$coupon->is_active) {
            throw new \Exception('Coupon is not active.');
        }

        if ($coupon->start_date > now()) {
            throw new \Exception('Coupon is not yet valid.');
        }

        if ($coupon->end_date < now()) {
            throw new \Exception('Coupon has expired.');
        }

        // Check if already applied
        $appliedCoupons = $cart->applied_coupons ?? [];
        if (collect($appliedCoupons)->contains('code', $couponCode)) {
            throw new \Exception('Coupon is already applied.');
        }

        // Check minimum order value
        if ($coupon->min_order_value && $cart->subtotal < $coupon->min_order_value) {
            throw new \Exception("Minimum order value of AED {$coupon->min_order_value} required.");
        }

        // Check usage limits
        if ($coupon->usage_limit && $coupon->usage_count >= $coupon->usage_limit) {
            throw new \Exception('Coupon usage limit reached.');
        }

        // Check vendor-specific coupons
        if ($coupon->vendor_id) {
            $hasVendorItems = $cart->items()
                ->where('vendor_id', $coupon->vendor_id)
                ->exists();
                
            if (!$hasVendorItems) {
                throw new \Exception('Coupon is only valid for specific vendor products.');
            }
        }

        // Check user-specific coupons
        if ($coupon->user_id && (!auth()->check() || $coupon->user_id !== auth()->id())) {
            throw new \Exception('Coupon is not valid for your account.');
        }
    }
}
