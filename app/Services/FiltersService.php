<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Brand;
use App\Models\DropdownOption;
use App\Traits\HelperTrait;
use Illuminate\Http\Request;

class FiltersService
{
    use HelperTrait;

    /**
     * Get dynamic filters based on context parameters
     * Supports filtering by category_id, subcategory_id, brand_id, etc.
     */
    public function getFilters(Request $request): array
    {
        $baseQuery = $this->buildBaseFilterQuery($request);
        
        return [
            'brands' => $this->getBrandFilters($baseQuery),
            'user_groups' => $this->getUserGroupFilters($baseQuery),
            'countries_of_origin' => $this->getCountryFilters($baseQuery),
            'formulations' => $this->getFormulationFilters($baseQuery),
            'flavours' => $this->getFlavourFilters($baseQuery),
            'storage_conditions' => $this->getStorageConditionFilters($baseQuery),
            'return_policies' => $this->getReturnPolicyFilters($baseQuery),
            'warranties' => $this->getWarrantyFilters($baseQuery),
            'price_range' => $this->getPriceRange($baseQuery),
            'boolean_filters' => $this->getBooleanFilters($baseQuery),
        ];
    }

    /**
     * Build base query for filters based on context parameters
     */
    private function buildBaseFilterQuery(Request $request)
    {
        $query = Product::where('is_active', true)
            ->where('is_approved', true);

        // Apply context filters to narrow down the product set
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        if ($request->filled('subcategory_id')) {
            $query->where('sub_category_id', $request->input('subcategory_id'));
        }

        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->input('brand_id'));
        }

        // Add other context filters as needed
        if ($request->filled('search')) {
            $searchTerm = '%' . strtolower($request->input('search')) . '%';
            $query->where(function ($query) use ($searchTerm) {
                $query->whereRaw('LOWER(title_en) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(title_ar) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(short_name) LIKE ?', [$searchTerm]);
            });
        }

        return $query;
    }

    /**
     * Get brand filters with product counts
     */
    private function getBrandFilters($baseQuery): array
    {
        $brandCounts = (clone $baseQuery)
            ->whereNotNull('brand_id')
            ->selectRaw('brand_id, COUNT(*) as product_count')
            ->groupBy('brand_id')
            ->pluck('product_count', 'brand_id');

        if ($brandCounts->isEmpty()) {
            return [];
        }

        return Brand::select('id', 'name_en', 'name_ar')
            ->whereIn('id', $brandCounts->keys())
            ->get()
            ->map(function ($brand) use ($brandCounts) {
                return [
                    'id' => $brand->id,
                    'name_en' => $brand->name_en,
                    'name_ar' => $brand->name_ar,
                    'product_count' => $brandCounts[$brand->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get user group filters with product counts
     */
    private function getUserGroupFilters($baseQuery): array
    {
        $userGroupCounts = (clone $baseQuery)
            ->whereNotNull('user_group_id')
            ->selectRaw('user_group_id, COUNT(*) as product_count')
            ->groupBy('user_group_id')
            ->pluck('product_count', 'user_group_id');

        if ($userGroupCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $userGroupCounts->keys())
            ->get()
            ->map(function ($userGroup) use ($userGroupCounts) {
                return [
                    'id' => $userGroup->id,
                    'value_en' => $userGroup->value_en,
                    'value_ar' => $userGroup->value_ar,
                    'product_count' => $userGroupCounts[$userGroup->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get country of origin filters with product counts
     */
    private function getCountryFilters($baseQuery): array
    {
        $countryCounts = (clone $baseQuery)
            ->whereNotNull('country_of_origin')
            ->selectRaw('country_of_origin, COUNT(*) as product_count')
            ->groupBy('country_of_origin')
            ->pluck('product_count', 'country_of_origin');

        if ($countryCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $countryCounts->keys())
            ->get()
            ->map(function ($country) use ($countryCounts) {
                return [
                    'id' => $country->id,
                    'value_en' => $country->value_en,
                    'value_ar' => $country->value_ar,
                    'product_count' => $countryCounts[$country->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get formulation filters with product counts
     */
    private function getFormulationFilters($baseQuery): array
    {
        $formulationCounts = (clone $baseQuery)
            ->whereNotNull('formulation_id')
            ->selectRaw('formulation_id, COUNT(*) as product_count')
            ->groupBy('formulation_id')
            ->pluck('product_count', 'formulation_id');

        if ($formulationCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $formulationCounts->keys())
            ->get()
            ->map(function ($formulation) use ($formulationCounts) {
                return [
                    'id' => $formulation->id,
                    'value_en' => $formulation->value_en,
                    'value_ar' => $formulation->value_ar,
                    'product_count' => $formulationCounts[$formulation->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get flavour filters with product counts
     */
    private function getFlavourFilters($baseQuery): array
    {
        $flavourCounts = (clone $baseQuery)
            ->whereNotNull('flavour_id')
            ->selectRaw('flavour_id, COUNT(*) as product_count')
            ->groupBy('flavour_id')
            ->pluck('product_count', 'flavour_id');

        if ($flavourCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $flavourCounts->keys())
            ->get()
            ->map(function ($flavour) use ($flavourCounts) {
                return [
                    'id' => $flavour->id,
                    'value_en' => $flavour->value_en,
                    'value_ar' => $flavour->value_ar,
                    'product_count' => $flavourCounts[$flavour->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get storage condition filters with product counts
     */
    private function getStorageConditionFilters($baseQuery): array
    {
        $storageCounts = (clone $baseQuery)
            ->whereNotNull('storage_conditions')
            ->selectRaw('storage_conditions, COUNT(*) as product_count')
            ->groupBy('storage_conditions')
            ->pluck('product_count', 'storage_conditions');

        if ($storageCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $storageCounts->keys())
            ->get()
            ->map(function ($storage) use ($storageCounts) {
                return [
                    'id' => $storage->id,
                    'value_en' => $storage->value_en,
                    'value_ar' => $storage->value_ar,
                    'product_count' => $storageCounts[$storage->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get return policy filters with product counts
     */
    private function getReturnPolicyFilters($baseQuery): array
    {
        $returnCounts = (clone $baseQuery)
            ->whereNotNull('is_returnable')
            ->selectRaw('is_returnable, COUNT(*) as product_count')
            ->groupBy('is_returnable')
            ->pluck('product_count', 'is_returnable');

        if ($returnCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $returnCounts->keys())
            ->get()
            ->map(function ($returnPolicy) use ($returnCounts) {
                return [
                    'id' => $returnPolicy->id,
                    'value_en' => $returnPolicy->value_en,
                    'value_ar' => $returnPolicy->value_ar,
                    'product_count' => $returnCounts[$returnPolicy->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get warranty filters with product counts
     */
    private function getWarrantyFilters($baseQuery): array
    {
        $warrantyCounts = (clone $baseQuery)
            ->whereNotNull('warranty')
            ->selectRaw('warranty, COUNT(*) as product_count')
            ->groupBy('warranty')
            ->pluck('product_count', 'warranty');

        if ($warrantyCounts->isEmpty()) {
            return [];
        }

        return DropdownOption::select('id', 'value_en', 'value_ar')
            ->whereIn('id', $warrantyCounts->keys())
            ->get()
            ->map(function ($warranty) use ($warrantyCounts) {
                return [
                    'id' => $warranty->id,
                    'value_en' => $warranty->value_en,
                    'value_ar' => $warranty->value_ar,
                    'product_count' => $warrantyCounts[$warranty->id] ?? 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get price range information
     */
    private function getPriceRange($baseQuery): array
    {
        $priceStats = (clone $baseQuery)
            ->selectRaw('MIN(regular_price) as min_price, MAX(regular_price) as max_price')
            ->first();

        return [
            'min_price' => $priceStats->min_price ?? 0,
            'max_price' => $priceStats->max_price ?? 0,
        ];
    }

    /**
     * Get boolean filter counts
     */
    private function getBooleanFilters($baseQuery): array
    {
        $booleanStats = (clone $baseQuery)
            ->selectRaw('
                SUM(CASE WHEN is_vegan = true THEN 1 ELSE 0 END) as vegan_count,
                SUM(CASE WHEN is_vegetarian = true THEN 1 ELSE 0 END) as vegetarian_count,
                SUM(CASE WHEN is_halal = true THEN 1 ELSE 0 END) as halal_count
            ')
            ->first();

        return [
            'vegan' => [
                'label_en' => 'Vegan',
                'label_ar' => 'نباتي',
                'product_count' => $booleanStats->vegan_count ?? 0,
            ],
            'vegetarian' => [
                'label_en' => 'Vegetarian',
                'label_ar' => 'نباتي',
                'product_count' => $booleanStats->vegetarian_count ?? 0,
            ],
            'halal' => [
                'label_en' => 'Halal',
                'label_ar' => 'حلال',
                'product_count' => $booleanStats->halal_count ?? 0,
            ],
        ];
    }
}
