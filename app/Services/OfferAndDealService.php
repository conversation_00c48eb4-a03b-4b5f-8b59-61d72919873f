<?php

namespace App\Services;

use App\Models\OfferAndDeal;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class OfferAndDealService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = OfferAndDeal::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['type' => '=', 'is_active' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'description_en', 'description_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareOfferAndDealData($request);

        return OfferAndDeal::create($data);
    }

    private function prepareOfferAndDealData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new OfferAndDeal())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'offerAndDeal')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'offerAndDeal');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'offerAndDeal');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): OfferAndDeal
    {
        return OfferAndDeal::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $offerAndDeal = OfferAndDeal::findOrFail($id);
        $updateData = $this->prepareOfferAndDealData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $offerAndDeal->update($updateData);

        return $offerAndDeal;
    }

    public function destroy(int $id): bool
    {
        $offerAndDeal = OfferAndDeal::findOrFail($id);
        return $offerAndDeal->delete();
    }
}
