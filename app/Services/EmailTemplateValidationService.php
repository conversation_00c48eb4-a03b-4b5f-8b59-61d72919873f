<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateVariable;
use Illuminate\Support\Facades\Validator;

class EmailTemplateValidationService
{
    /**
     * Validate template content and structure.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function validateTemplate(EmailTemplate $template): array
    {
        $errors = [];
        
        // Validate basic template structure
        $errors = array_merge($errors, $this->validateBasicStructure($template));
        
        // Validate HTML content
        $errors = array_merge($errors, $this->validateHtmlContent($template));
        
        // Validate variables
        $errors = array_merge($errors, $this->validateVariables($template));
        
        // Validate template syntax
        $errors = array_merge($errors, $this->validateTemplateSyntax($template));
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Validate basic template structure.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateBasicStructure(EmailTemplate $template): array
    {
        $errors = [];
        
        // Check required fields
        if (empty($template->name)) {
            $errors[] = 'Template name is required';
        }
        
        if (empty($template->subject)) {
            $errors[] = 'Template subject is required';
        }
        
        if (empty($template->body_html)) {
            $errors[] = 'Template HTML body is required';
        }
        
        // Check name length
        if (strlen($template->name) > 255) {
            $errors[] = 'Template name must not exceed 255 characters';
        }
        
        // Check subject length
        if (strlen($template->subject) > 500) {
            $errors[] = 'Template subject must not exceed 500 characters';
        }
        
        // Validate slug uniqueness (if provided)
        if ($template->slug) {
            $existingTemplate = EmailTemplate::where('slug', $template->slug)
                ->where('id', '!=', $template->id)
                ->first();
            
            if ($existingTemplate) {
                $errors[] = 'Template slug must be unique';
            }
        }
        
        return $errors;
    }

    /**
     * Validate HTML content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateHtmlContent(EmailTemplate $template): array
    {
        $errors = [];
        
        if (!empty($template->body_html)) {
            // Check for valid HTML structure
            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);
            
            if (!$dom->loadHTML('<?xml encoding="UTF-8">' . $template->body_html)) {
                $errors[] = 'Invalid HTML structure in template body';
            }
            
            libxml_clear_errors();
            
            // Check for potentially dangerous HTML elements
            $dangerousElements = ['script', 'iframe', 'object', 'embed', 'form'];
            foreach ($dangerousElements as $element) {
                if (stripos($template->body_html, "<$element") !== false) {
                    $errors[] = "Potentially dangerous HTML element detected: <$element>";
                }
            }
            
            // Check for inline JavaScript
            if (preg_match('/on\w+\s*=/', $template->body_html)) {
                $errors[] = 'Inline JavaScript event handlers are not allowed';
            }
        }
        
        return $errors;
    }

    /**
     * Validate template variables.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateVariables(EmailTemplate $template): array
    {
        $errors = [];
        
        // Extract variables from template content
        $usedVariables = $this->extractVariablesFromTemplate($template);
        
        // Get known variables from database
        $knownVariables = EmailTemplateVariable::pluck('key')->toArray();
        
        // Check for unknown variables
        foreach ($usedVariables as $variable) {
            if (!in_array($variable, $knownVariables)) {
                $errors[] = "Unknown variable: {{$variable}}";
            }
        }
        
        // Validate variable syntax
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Check for malformed variables
        if (preg_match_all('/\{[^{]|\}[^}]/', $content, $matches)) {
            $errors[] = 'Malformed variable syntax detected. Use {{variable}} format';
        }
        
        // Check for nested variables (not supported)
        if (preg_match('/\{\{[^}]*\{\{/', $content)) {
            $errors[] = 'Nested variables are not supported';
        }
        
        return $errors;
    }

    /**
     * Validate template syntax (conditionals, loops, etc.).
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateTemplateSyntax(EmailTemplate $template): array
    {
        $errors = [];
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Validate conditional blocks
        $errors = array_merge($errors, $this->validateConditionals($content));
        
        // Validate loop blocks
        $errors = array_merge($errors, $this->validateLoops($content));
        
        return $errors;
    }

    /**
     * Validate conditional blocks.
     *
     * @param string $content
     * @return array
     */
    private function validateConditionals(string $content): array
    {
        $errors = [];
        
        // Check for unmatched if blocks
        $ifCount = preg_match_all('/\{\{#if\s+[^}]+\}\}/', $content);
        $endifCount = preg_match_all('/\{\{\/if\}\}/', $content);
        
        if ($ifCount !== $endifCount) {
            $errors[] = 'Unmatched {{#if}} and {{/if}} blocks';
        }
        
        // Check for empty conditions
        if (preg_match('/\{\{#if\s*\}\}/', $content)) {
            $errors[] = 'Empty condition in {{#if}} block';
        }
        
        return $errors;
    }

    /**
     * Validate loop blocks.
     *
     * @param string $content
     * @return array
     */
    private function validateLoops(string $content): array
    {
        $errors = [];
        
        // Check for unmatched each blocks
        $eachCount = preg_match_all('/\{\{#each\s+[^}]+\}\}/', $content);
        $endeachCount = preg_match_all('/\{\{\/each\}\}/', $content);
        
        if ($eachCount !== $endeachCount) {
            $errors[] = 'Unmatched {{#each}} and {{/each}} blocks';
        }
        
        // Check for empty array references
        if (preg_match('/\{\{#each\s*\}\}/', $content)) {
            $errors[] = 'Empty array reference in {{#each}} block';
        }
        
        return $errors;
    }

    /**
     * Extract variables from template content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function extractVariablesFromTemplate(EmailTemplate $template): array
    {
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Extract simple variables {{variable}}
        preg_match_all('/\{\{([^}#\/]+)\}\}/', $content, $matches);
        
        return array_unique(array_map('trim', $matches[1] ?? []));
    }

    /**
     * Validate variable data against template requirements.
     *
     * @param EmailTemplate $template
     * @param array $variables
     * @return array
     */
    public function validateVariableData(EmailTemplate $template, array $variables): array
    {
        $errors = [];
        
        // Get template variables
        $templateVariables = $this->extractVariablesFromTemplate($template);
        
        foreach ($templateVariables as $variableKey) {
            $templateVar = EmailTemplateVariable::where('key', $variableKey)->first();
            
            if (!$templateVar) {
                continue; // Skip unknown variables (handled in template validation)
            }
            
            // Check if required variable is provided
            if ($templateVar->is_required && !$this->hasVariableValue($variables, $variableKey)) {
                $errors[] = "Required variable '{$variableKey}' is missing";
                continue;
            }
            
            // Validate data type if value is provided
            $value = $this->getVariableValue($variables, $variableKey);
            if ($value !== null) {
                $typeError = $this->validateVariableType($variableKey, $value, $templateVar->data_type);
                if ($typeError) {
                    $errors[] = $typeError;
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Check if variable value exists in provided data.
     *
     * @param array $variables
     * @param string $key
     * @return bool
     */
    private function hasVariableValue(array $variables, string $key): bool
    {
        $keys = explode('.', $key);
        $current = $variables;
        
        foreach ($keys as $k) {
            if (is_array($current) && array_key_exists($k, $current)) {
                $current = $current[$k];
            } else {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get variable value using dot notation.
     *
     * @param array $variables
     * @param string $key
     * @return mixed
     */
    private function getVariableValue(array $variables, string $key)
    {
        $keys = explode('.', $key);
        $current = $variables;
        
        foreach ($keys as $k) {
            if (is_array($current) && array_key_exists($k, $current)) {
                $current = $current[$k];
            } else {
                return null;
            }
        }
        
        return $current;
    }

    /**
     * Validate variable type.
     *
     * @param string $key
     * @param mixed $value
     * @param string $expectedType
     * @return string|null
     */
    private function validateVariableType(string $key, $value, string $expectedType): ?string
    {
        switch ($expectedType) {
            case 'string':
                if (!is_string($value)) {
                    return "Variable '{$key}' must be a string";
                }
                break;
                
            case 'number':
                if (!is_numeric($value)) {
                    return "Variable '{$key}' must be a number";
                }
                break;
                
            case 'boolean':
                if (!is_bool($value)) {
                    return "Variable '{$key}' must be a boolean";
                }
                break;
                
            case 'date':
                if (!$this->isValidDate($value)) {
                    return "Variable '{$key}' must be a valid date";
                }
                break;
                
            case 'array':
                if (!is_array($value)) {
                    return "Variable '{$key}' must be an array";
                }
                break;
        }
        
        return null;
    }

    /**
     * Check if value is a valid date.
     *
     * @param mixed $value
     * @return bool
     */
    private function isValidDate($value): bool
    {
        if (!is_string($value)) {
            return false;
        }
        
        return strtotime($value) !== false;
    }
}
