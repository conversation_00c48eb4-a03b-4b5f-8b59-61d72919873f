<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateVariable;
use Illuminate\Support\Facades\Validator;

class EmailTemplateValidationService
{
    /**
     * Validate template content and structure.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function validateTemplate(EmailTemplate $template): array
    {
        $errors = [];
        
        // Validate basic template structure
        $errors = array_merge($errors, $this->validateBasicStructure($template));
        
        // Validate HTML content
        $errors = array_merge($errors, $this->validateHtmlContent($template));
        
        // Validate variables
        $errors = array_merge($errors, $this->validateVariables($template));
        
        // Validate template syntax
        $errors = array_merge($errors, $this->validateTemplateSyntax($template));
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Validate basic template structure.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateBasicStructure(EmailTemplate $template): array
    {
        $errors = [];
        
        // Check required fields
        if (empty($template->name)) {
            $errors[] = 'Template name is required';
        }
        
        if (empty($template->subject)) {
            $errors[] = 'Template subject is required';
        }
        
        if (empty($template->body_html) && empty($template->body_text)) {
            $errors[] = 'Template must have either HTML or text body content';
        }
        
        // Check name length
        if (strlen($template->name) > 255) {
            $errors[] = 'Template name must not exceed 255 characters';
        }
        
        // Check subject length
        if (strlen($template->subject) > 500) {
            $errors[] = 'Template subject must not exceed 500 characters';
        }
        
        // Validate slug uniqueness (if provided)
        if ($template->slug) {
            $existingTemplate = EmailTemplate::where('slug', $template->slug)
                ->where('id', '!=', $template->id)
                ->first();
            
            if ($existingTemplate) {
                $errors[] = 'Template slug must be unique';
            }
        }
        
        return $errors;
    }

    /**
     * Validate HTML content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateHtmlContent(EmailTemplate $template): array
    {
        $errors = [];
        
        if (!empty($template->body_html)) {
            // Check for valid HTML structure
            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);
            
            if (!$dom->loadHTML('<?xml encoding="UTF-8">' . $template->body_html)) {
                $errors[] = 'Invalid HTML structure in template body';
            }
            
            libxml_clear_errors();
            
            // Check for potentially dangerous HTML elements
            $dangerousElements = ['script', 'iframe', 'object', 'embed', 'form'];
            foreach ($dangerousElements as $element) {
                if (stripos($template->body_html, "<$element") !== false) {
                    $errors[] = "Potentially dangerous HTML element detected: <$element>";
                }
            }
            
            // Check for inline JavaScript
            if (preg_match('/on\w+\s*=/', $template->body_html)) {
                $errors[] = 'Inline JavaScript event handlers are not allowed';
            }
        }
        
        return $errors;
    }

    /**
     * Validate template variables.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateVariables(EmailTemplate $template): array
    {
        $errors = [];
        
        // Extract variables from template content
        $usedVariables = $this->extractVariablesFromTemplate($template);
        
        // Get known variables from database
        $knownVariables = EmailTemplateVariable::pluck('key')->toArray();
        
        // Check for unknown variables
        foreach ($usedVariables as $variable) {
            if (!in_array($variable, $knownVariables)) {
                $errors[] = "Unknown variable: {{$variable}}";
            }
        }
        
        // Validate variable syntax
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Check for malformed variables
        if (preg_match_all('/\{[^{]|\}[^}]/', $content, $matches)) {
            $errors[] = 'Malformed variable syntax detected. Use {{variable}} format';
        }
        
        // Check for nested variables (not supported)
        if (preg_match('/\{\{[^}]*\{\{/', $content)) {
            $errors[] = 'Nested variables are not supported';
        }
        
        return $errors;
    }

    /**
     * Validate template syntax (conditionals, loops, etc.).
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function validateTemplateSyntax(EmailTemplate $template): array
    {
        $errors = [];
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Validate conditional blocks
        $errors = array_merge($errors, $this->validateConditionals($content));
        
        // Validate loop blocks
        $errors = array_merge($errors, $this->validateLoops($content));
        
        return $errors;
    }

    /**
     * Validate conditional blocks.
     *
     * @param string $content
     * @return array
     */
    private function validateConditionals(string $content): array
    {
        $errors = [];
        
        // Check for unmatched if blocks
        $ifCount = preg_match_all('/\{\{#if\s+[^}]+\}\}/', $content);
        $endifCount = preg_match_all('/\{\{\/if\}\}/', $content);
        
        if ($ifCount !== $endifCount) {
            $errors[] = 'Unmatched {{#if}} and {{/if}} blocks';
        }
        
        // Check for empty conditions
        if (preg_match('/\{\{#if\s*\}\}/', $content)) {
            $errors[] = 'Empty condition in {{#if}} block';
        }
        
        return $errors;
    }

    /**
     * Validate loop blocks.
     *
     * @param string $content
     * @return array
     */
    private function validateLoops(string $content): array
    {
        $errors = [];
        
        // Check for unmatched each blocks
        $eachCount = preg_match_all('/\{\{#each\s+[^}]+\}\}/', $content);
        $endeachCount = preg_match_all('/\{\{\/each\}\}/', $content);
        
        if ($eachCount !== $endeachCount) {
            $errors[] = 'Unmatched {{#each}} and {{/each}} blocks';
        }
        
        // Check for empty array references
        if (preg_match('/\{\{#each\s*\}\}/', $content)) {
            $errors[] = 'Empty array reference in {{#each}} block';
        }
        
        return $errors;
    }

    /**
     * Extract variables from template content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function extractVariablesFromTemplate(EmailTemplate $template): array
    {
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        
        // Extract simple variables {{variable}}
        preg_match_all('/\{\{([^}#\/]+)\}\}/', $content, $matches);
        
        return array_unique(array_map('trim', $matches[1] ?? []));
    }

    /**
     * Validate variable data against template requirements.
     *
     * @param EmailTemplate $template
     * @param array $variables
     * @return array
     */
    public function validateVariableData(EmailTemplate $template, array $variables): array
    {
        $errors = [];
        
        // Get template variables
        $templateVariables = $this->extractVariablesFromTemplate($template);
        
        foreach ($templateVariables as $variableKey) {
            $templateVar = EmailTemplateVariable::where('key', $variableKey)->first();
            
            if (!$templateVar) {
                continue; // Skip unknown variables (handled in template validation)
            }
            
            // Check if required variable is provided
            if ($templateVar->is_required && !$this->hasVariableValue($variables, $variableKey)) {
                $errors[] = "Required variable '{$variableKey}' is missing";
                continue;
            }
            
            // Validate data type if value is provided
            $value = $this->getVariableValue($variables, $variableKey);
            if ($value !== null) {
                $typeError = $this->validateVariableType($variableKey, $value, $templateVar->data_type);
                if ($typeError) {
                    $errors[] = $typeError;
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Check if variable value exists in provided data.
     *
     * @param array $variables
     * @param string $key
     * @return bool
     */
    private function hasVariableValue(array $variables, string $key): bool
    {
        $keys = explode('.', $key);
        $current = $variables;
        
        foreach ($keys as $k) {
            if (is_array($current) && array_key_exists($k, $current)) {
                $current = $current[$k];
            } else {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get variable value using dot notation.
     *
     * @param array $variables
     * @param string $key
     * @return mixed
     */
    private function getVariableValue(array $variables, string $key)
    {
        $keys = explode('.', $key);
        $current = $variables;
        
        foreach ($keys as $k) {
            if (is_array($current) && array_key_exists($k, $current)) {
                $current = $current[$k];
            } else {
                return null;
            }
        }
        
        return $current;
    }

    /**
     * Validate variable type.
     *
     * @param string $key
     * @param mixed $value
     * @param string $expectedType
     * @return string|null
     */
    private function validateVariableType(string $key, $value, string $expectedType): ?string
    {
        switch ($expectedType) {
            case 'string':
                if (!is_string($value)) {
                    return "Variable '{$key}' must be a string";
                }
                break;
                
            case 'number':
                if (!is_numeric($value)) {
                    return "Variable '{$key}' must be a number";
                }
                break;
                
            case 'boolean':
                if (!is_bool($value)) {
                    return "Variable '{$key}' must be a boolean";
                }
                break;
                
            case 'date':
                if (!$this->isValidDate($value)) {
                    return "Variable '{$key}' must be a valid date";
                }
                break;
                
            case 'array':
                if (!is_array($value)) {
                    return "Variable '{$key}' must be an array";
                }
                break;
        }
        
        return null;
    }

    /**
     * Check if value is a valid date.
     *
     * @param mixed $value
     * @return bool
     */
    private function isValidDate($value): bool
    {
        if (!is_string($value)) {
            return false;
        }
        
        return strtotime($value) !== false;
    }

    /**
     * Validate variable syntax.
     *
     * @param string $variable
     * @return bool
     */
    public function validateVariableSyntax(string $variable): bool
    {
        // Check if variable has proper {{}} syntax
        if (!preg_match('/^\{\{.+\}\}$/', $variable)) {
            return false;
        }

        // Extract variable name
        $variableName = trim(str_replace(['{{', '}}'], '', $variable));

        // Check if variable name is not empty
        if (empty($variableName)) {
            return false;
        }

        // Check for valid characters (letters, numbers, dots, underscores)
        if (!preg_match('/^[a-zA-Z0-9._]+$/', $variableName)) {
            return false;
        }

        return true;
    }

    /**
     * Validate required variables are present.
     *
     * @param EmailTemplate $template
     * @param array $providedVariables
     * @return array
     */
    public function validateRequiredVariables(EmailTemplate $template, array $providedVariables): array
    {
        $errors = [];
        $templateVariables = $template->variables ?? [];

        foreach ($templateVariables as $variableKey) {
            $variable = EmailTemplateVariable::where('key', $variableKey)->first();

            if ($variable && $variable->is_required && !isset($providedVariables[$variableKey])) {
                $errors[] = "Required variable '{$variableKey}' is missing";
            }
        }

        return $errors;
    }

    /**
     * Validate variable data types.
     *
     * @param array $variables
     * @return array
     */
    public function validateVariableTypes(array $variables): array
    {
        $errors = [];

        foreach ($variables as $key => $value) {
            $variable = EmailTemplateVariable::where('key', $key)->first();

            if ($variable) {
                $isValid = match ($variable->data_type) {
                    'string' => is_string($value),
                    'number' => is_numeric($value),
                    'boolean' => is_bool($value),
                    'date' => $this->isValidDateValue($value),
                    'array' => is_array($value),
                    default => true,
                };

                if (!$isValid) {
                    $errors[] = "Variable '{$key}' should be of type '{$variable->data_type}', got " . gettype($value);
                }
            }
        }

        return $errors;
    }

    /**
     * Extract variables from template content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function extractTemplateVariables(EmailTemplate $template): array
    {
        return $this->extractVariablesFromTemplate($template);
    }

    /**
     * Validate HTML structure.
     *
     * @param string $html
     * @return array
     */
    public function validateHtmlStructure(string $html): array
    {
        $errors = [];

        if (empty($html)) {
            return $errors;
        }

        // Use DOMDocument to validate HTML structure
        $dom = new \DOMDocument();
        $previousSetting = libxml_use_internal_errors(true);
        libxml_clear_errors();

        $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $libxmlErrors = libxml_get_errors();
        foreach ($libxmlErrors as $error) {
            $errors[] = "HTML structure error: " . trim($error->message);
        }

        libxml_use_internal_errors($previousSetting);
        libxml_clear_errors();

        return $errors;
    }

    /**
     * Validate HTML security (check for dangerous content).
     *
     * @param string $html
     * @return array
     */
    public function validateHtmlSecurity(string $html): array
    {
        $errors = [];
        $dangerousTags = ['script', 'iframe', 'object', 'embed', 'form', 'input'];
        $dangerousAttributes = ['onclick', 'onload', 'onerror', 'onmouseover', 'javascript:'];

        foreach ($dangerousTags as $tag) {
            if (preg_match("/<$tag\b/i", $html)) {
                $errors[] = "Dangerous HTML tag detected: <$tag>";
            }
        }

        foreach ($dangerousAttributes as $attr) {
            if (stripos($html, $attr) !== false) {
                $errors[] = "Dangerous HTML attribute detected: $attr";
            }
        }

        return $errors;
    }

    /**
     * Check if a value is a valid date (different name to avoid conflict).
     *
     * @param mixed $value
     * @return bool
     */
    private function isValidDateValue($value): bool
    {
        if (!is_string($value)) {
            return false;
        }

        $date = \DateTime::createFromFormat('Y-m-d', $value);
        return $date && $date->format('Y-m-d') === $value;
    }

    /**
     * Validate template completeness.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function validateTemplateCompleteness(EmailTemplate $template): array
    {
        $errors = [];

        if (empty($template->name)) {
            $errors[] = "Template name is required";
        }

        if (empty($template->subject)) {
            $errors[] = "Template subject is required";
        }

        if (empty($template->body_html) && empty($template->body_text)) {
            $errors[] = "Template must have either HTML or text body content";
        }

        return $errors;
    }

    /**
     * Validate variable consistency between template content and variables array.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function validateVariableConsistency(EmailTemplate $template): array
    {
        $errors = [];

        $extractedVariables = $this->extractVariablesFromTemplate($template);
        $declaredVariables = $template->variables ?? [];

        // Find variables used in template but not declared
        $undeclaredVariables = array_diff($extractedVariables, $declaredVariables);
        foreach ($undeclaredVariables as $variable) {
            $errors[] = "Variable '{$variable}' is used in template but not declared in variables array";
        }

        // Find declared variables not used in template
        $unusedVariables = array_diff($declaredVariables, $extractedVariables);
        foreach ($unusedVariables as $variable) {
            $errors[] = "Variable '{$variable}' is declared but not used in template";
        }

        return $errors;
    }

    /**
     * Get comprehensive validation report.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function getValidationReport(EmailTemplate $template): array
    {
        $errors = $this->validateTemplate($template);
        $warnings = [];
        $variablesFound = $this->extractVariablesFromTemplate($template);

        // Add warnings for potential issues
        if (empty($template->body_text) && !empty($template->body_html)) {
            $warnings[] = "Template has HTML content but no text alternative";
        }

        if (count($variablesFound) === 0) {
            $warnings[] = "Template contains no dynamic variables";
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'variables_found' => $variablesFound,
        ];
    }

    /**
     * Validate conditional syntax.
     *
     * @param string $content
     * @return array
     */
    public function validateConditionalSyntax(string $content): array
    {
        $errors = [];

        // Check for balanced if/endif pairs
        $ifCount = preg_match_all('/\{\{#if\s+[^}]+\}\}/', $content);
        $endifCount = preg_match_all('/\{\{\/if\}\}/', $content);

        if ($ifCount !== $endifCount) {
            $errors[] = "Unbalanced conditional blocks: found {$ifCount} opening {{#if}} but {$endifCount} closing {{/if}}";
        }

        // Check for proper syntax
        if (preg_match('/\{\{#if\s+[^}]+\}\}.*?\{\{\/if\}\}/s', $content) === false && strpos($content, '{{#if') !== false) {
            $errors[] = "Invalid conditional syntax detected";
        }

        return $errors;
    }

    /**
     * Validate loop syntax.
     *
     * @param string $content
     * @return array
     */
    public function validateLoopSyntax(string $content): array
    {
        $errors = [];

        // Check for balanced each/endeach pairs
        $eachCount = preg_match_all('/\{\{#each\s+[^}]+\}\}/', $content);
        $endeachCount = preg_match_all('/\{\{\/each\}\}/', $content);

        if ($eachCount !== $endeachCount) {
            $errors[] = "Unbalanced loop blocks: found {$eachCount} opening {{#each}} but {$endeachCount} closing {{/each}}";
        }

        // Check for proper syntax
        if (preg_match('/\{\{#each\s+[^}]+\}\}.*?\{\{\/each\}\}/s', $content) === false && strpos($content, '{{#each') !== false) {
            $errors[] = "Invalid loop syntax detected";
        }

        return $errors;
    }
}
