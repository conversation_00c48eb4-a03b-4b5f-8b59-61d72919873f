<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductClass;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ProductClassService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = ProductClass::query();
        $query->with(['category:id,name_en,code', 'subCategory:id,name_en,code', 'parent:id,name_en,code']);

        // Select specific columns
        $query->select(['*']);

        // Apply filters
        $this->applyProductClassFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareProductClassData($request);

        return ProductClass::create($data);
    }

    private function prepareProductClassData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new ProductClass())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'productClass');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'productClass');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ProductClass
    {
        return ProductClass::with(['category:id,name_en,code', 'subCategory:id,name_en,code'])
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $productClass = ProductClass::findOrFail($id);
        $updateData = $this->prepareProductClassData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $productClass->update($updateData);

        return $productClass;
    }

    public function destroy(int $id): bool
    {
        $productClass = ProductClass::findOrFail($id);
        return $productClass->delete();
    }

    public function getClassByCategory(int $categoryId): Collection
    {
        return ProductClass::where('category_id', $categoryId)
            ->where('status', 'active')
            ->whereNull('parent_id') // Only get top-level classes
            ->select(['id', 'name_en', 'status', 'code', 'parent_id', 'slug', 'is_popular'])
            ->orderBy('name_en', 'asc')
            ->get();
    }

    public function getSubClasses(int $classId): Collection
    {
        return ProductClass::where('parent_id', $classId)
            ->where('status', 'active')
            ->select(['id', 'name_en', 'status', 'code', 'slug', 'parent_id', 'is_popular'])
            ->orderBy('name_en', 'asc')
            ->get();
    }

    public function classListByActive($request): Collection
    {
        $query = ProductClass::where('status', 'active')
            ->whereNull('parent_id')
            ->select(['id', 'name_en', 'status', 'code', 'parent_id', 'is_popular']);

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('sub_category_id')) {
            $query->where('sub_category_id', $request->sub_category_id);
        }

        return $query->orderBy('name_en', 'asc')->get();
    }

    /**
     * Apply product class specific filters
     */
    private function applyProductClassFilters($query, Request $request): void
    {
        // Filter by parent classes (is_parent = true means parent_id is null)
        if ($request->has('is_parent')) {
            $isParent = filter_var($request->input('is_parent'), FILTER_VALIDATE_BOOLEAN);
            if ($isParent) {
                // Parent classes (parent_id is null)
                $query->whereNull('parent_id');
            } else {
                // Child classes (parent_id is not null)
                $query->whereNotNull('parent_id');
            }
        }

        // Filter by parent class ID
        if ($request->filled('parent_class')) {
            $query->where('parent_id', $request->input('parent_class'));
        }

        // Filter by category ID
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // Filter by sub category ID
        if ($request->filled('sub_category_id')) {
            $query->where('sub_category_id', $request->input('sub_category_id'));
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // Filter by is_popular
        if ($request->has('is_popular')) {
            $isPopular = filter_var($request->input('is_popular'), FILTER_VALIDATE_BOOLEAN);
            $query->where('is_popular', $isPopular);
        }
    }



    public function getClassBySlug(string $slug): array
    {

        $class = $this->findClassBySlug($slug);

        return [
            'class' => $this->buildClassDetails($class),
            'breadcrumb' => $this->buildBreadcrumb($class),
 
        ];
    }

    public function getSubclassesBySlug(string $slug): array
    {
        $class = $this->findSubClassBySlug($slug);

        return [
            'class' => $this->buildSubClassDetails($class),
            'breadcrumb' => $this->buildBreadcrumb($class),
        ];
    }

    public function findClassBySlug(string $slug): ProductClass
    {
        $class = ProductClass::where('slug', $slug)
            ->with('category:id,name_en,code', 'subCategory:id,name_en,code','subClasses:id,name_en,code')
            ->where('status', 'active')
            ->whereNull('parent_id') // Only parent categories
            ->first();

        if (!$class) {
            throw new ModelNotFoundException('Class not found');
        }

        return $class;
    }

    public function findSubClassBySlug(string $slug): ProductClass
    {
        $class = ProductClass::where('slug', $slug)
            ->with('category:id,name_en,code', 'subCategory:id,name_en,code')
            ->where('status', 'active')
            ->whereNotNull('parent_id') // Only parent categories
            ->first();

        if (!$class) {
            throw new ModelNotFoundException('Class not found');
        }

        return $class;
    }

    public function buildClassDetails(ProductClass $class)
    {
        $details = [
            'id' => $class->id,
            'name_en' => $class->name_en,
            'name_ar' => $class->name_ar,
            'slug' => $class->slug,
            'icon_url' => $class->icon_url,
            'description_en' => $class->description_en ?? null,
            'description_ar' => $class->description_ar ?? null,
            'sub_classes' => $class->subclasses->map(function ($subclass) {
                return [
                    'id' => $subclass->id,
                    'name_en' => $subclass->name_en,
                    'name_ar' => $subclass->name_ar,
                    'slug' => $subclass->slug,
                ];
            })->toArray(),
        ];


        return $details;
    }
    public function buildSubClassDetails(ProductClass $class)
    {
        $details = [
            'id' => $class->id,
            'name_en' => $class->name_en,
            'name_ar' => $class->name_ar,
            'slug' => $class->slug,
            'description_en' => $class->description_en ?? null,
            'description_ar' => $class->description_ar ?? null
         
        ];


        return $details;
    }

    public function buildBreadcrumb(ProductClass $productClass, ?int $subCategoryId = null): array
    {
        $breadcrumb = [
            [
                'name_en' => 'Home',
                'name_ar' => 'الرئيسية',
                'url' => '/',
            ],
            [
                'name_en' => $productClass->category->name_en,
                'name_ar' => $productClass->category->name_ar,
                'url' => 'categories/' . $productClass->category->slug,
            ],
            [
                'name_en' => $productClass->name_en,
                'name_ar' => $productClass->name_ar,
                'url' => 'categories/' . $productClass->slug,
            ],
        ];

        return $breadcrumb;
    }

    public function productListByVendor(int $vendorId)
    {
        $products = Product::where('vendor_id', $vendorId)
            ->where('is_active', 1)
            ->where('is_approved', 1)
            ->select(['id', 'title_en', 'title_ar', 'vendor_id','status','is_active','is_approved'])
            ->get();
            return $products;
    }

    
};
