<?php

namespace App\Services;

use App\Models\BannerItem;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BannerItemService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = BannerItem::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['is_active' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['title_en', 'title_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareBannerItemData($request);

        return BannerItem::create($data);
    }

    private function prepareBannerItemData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new BannerItem())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));
        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): BannerItem
    {
        return BannerItem::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $bannerItem = BannerItem::findOrFail($id);
        $updateData = $this->prepareBannerItemData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $bannerItem->update($updateData);

        return $bannerItem;
    }

    public function destroy(int $id): bool
    {
        $bannerItem = BannerItem::findOrFail($id);
        return $bannerItem->delete();
    }
}
