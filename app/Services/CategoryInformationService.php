<?php

namespace App\Services;

use App\Models\Category;
use App\Traits\HelperTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class CategoryInformationService
{
    use HelperTrait;

    /**
     * Get category information by slug
     */
    public function getCategoryBySlug(string $slug): array
    {
        $category = $this->findCategoryBySlug($slug);

        return [
            'category' => $this->buildCategoryDetails($category),
            'breadcrumb' => $this->buildBreadcrumb($category),
            'subcategories' => $this->getSubcategories($category),
        ];
    }

    /**
     * Get subcategory information by slug
     */
    public function getSubcategoryBySlug(string $slug): array
    {
        $subcategory = $this->findSubcategoryBySlug($slug);

        return [
            'category' => $this->buildSubcategoryDetails($subcategory),
            'breadcrumb' => $this->buildSubcategoryBreadcrumb($subcategory),
        ];
    }

    /**
     * Find category by slug
     */
    public function findCategoryBySlug(string $slug): Category
    {
        $category = Category::where('slug', $slug)
            ->where('status', 'active')
            ->whereNull('parent_id') // Only parent categories
            ->with(['banner.items' => function ($query) {
                $query->where('is_active', true)->orderBy('position');
            }])
            ->first();

        if (!$category) {
            throw new ModelNotFoundException('Category not found');
        }

        return $category;
    }

    /**
     * Find subcategory by slug
     */
    public function findSubcategoryBySlug(string $slug): Category
    {
        $subcategory = Category::where('slug', $slug)
            ->where('status', 'active')
            ->whereNotNull('parent_id') // Only subcategories
            ->with(['banner.items' => function ($query) {
                $query->where('is_active', true)->orderBy('position');
            }])
            ->first();

        if (!$subcategory) {
            throw new ModelNotFoundException('Subcategory not found');
        }

        return $subcategory;
    }

    /**
     * Build category details with multilingual support
     */
    public function buildCategoryDetails(Category $category): array
    {
        $details = [
            'id' => $category->id,
            'name_en' => $category->name_en,
            'name_ar' => $category->name_ar,
            'slug' => $category->slug,
            'icon_url' => $category->icon_url,
            'description_en' => $category->description_en ?? null,
            'description_ar' => $category->description_ar ?? null,
        ];

        // Add banner information if present
        if ($category->banner) {
            $details['banner'] = [
                'id' => $category->banner->id,
                'title' => $category->banner->title,
                'description' => $category->banner->description,
                'type' => $category->banner->type,
                'is_active' => $category->banner->is_active,
                'items' => $category->banner->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title_en' => $item->title_en,
                        'title_ar' => $item->title_ar,
                        'media_path_url' => $item->media_path_url,
                        'link_url' => $item->link_url,
                        'target' => $item->target,
                        'alt_text' => $item->alt_text,
                        'position' => $item->position,
                        'is_active' => $item->is_active,
                    ];
                })->toArray(),
            ];
        } else {
            $details['banner'] = null;
        }

        return $details;
    }

    /**
     * Build subcategory details with parent category information
     */
    public function buildSubcategoryDetails(Category $subcategory): array
    {

        return  [
            'id' => $subcategory->id,
            'name_en' => $subcategory->name_en,
            'name_ar' => $subcategory->name_ar,
            'slug' => $subcategory->slug,
            'icon_url' => $subcategory->icon_url,
            'description_en' => $subcategory->description_en ?? null,
            'description_ar' => $subcategory->description_ar ?? null,
            'classes' => $subcategory->classes->map(function ($class) {
                return [
                    'id' => $class->id,
                    'name_en' => $class->name_en,
                    'name_ar' => $class->name_ar,
                    'slug' => $class->slug,
                    'icon_url' => $class->icon_url,
                ];
            })->toArray(),
        ];
    }

    /**
     * Get subcategories with product counts
     */
    public function getSubcategories(Category $category): array
    {
        return $category->children()
            ->where('status', 'active')
            ->select(['id', 'name_en', 'name_ar', 'slug', 'icon'])
            ->withCount(['products' => function ($query) {
                $query->where('is_active', true)
                    ->where('is_approved', true);
            }])
            ->orderBy('name_en')
            ->get()
            ->map(function ($subcategory) {
                return [
                    'id' => $subcategory->id,
                    'name_en' => $subcategory->name_en,
                    'name_ar' => $subcategory->name_ar,
                    'slug' => $subcategory->slug,
                    'icon_url' => $subcategory->icon_url,
                    'product_count' => $subcategory->products_count,
                ];
            })
            ->toArray();
    }

    /**
     * Build breadcrumb navigation for category
     */
    public function buildBreadcrumb(Category $category, ?int $subCategoryId = null): array
    {
        $breadcrumb = [
            [
                'name_en' => 'Home',
                'name_ar' => 'الرئيسية',
                'url' => '/',
            ],
            [
                'name_en' => $category->name_en,
                'name_ar' => $category->name_ar,
                'url' => '/category/' . $category->slug,
            ],
        ];

        // Add subcategory to breadcrumb if filtering by subcategory
        if ($subCategoryId) {
            $subcategory = Category::find($subCategoryId);
            if ($subcategory) {
                $breadcrumb[] = [
                    'name_en' => $subcategory->name_en,
                    'name_ar' => $subcategory->name_ar,
                    'url' => '/category/' . $category->slug . '/' . $subcategory->slug,
                ];
            }
        }

        return $breadcrumb;
    }

    /**
     * Build breadcrumb navigation for subcategory
     */
    public function buildSubcategoryBreadcrumb(Category $subcategory): array
    {
        $subcategory->load('parent');

        $breadcrumb = [
            [
                'name_en' => 'Home',
                'name_ar' => 'الرئيسية',
                'url' => '/',
            ],
        ];

        // Add parent category if exists
        if ($subcategory->parent) {
            $breadcrumb[] = [
                'name_en' => $subcategory->parent->name_en,
                'name_ar' => $subcategory->parent->name_ar,
                'url' => '/web/categories/' . $subcategory->parent->slug,
            ];
        }

        // Add current subcategory
        $breadcrumb[] = [
            'name_en' => $subcategory->name_en,
            'name_ar' => $subcategory->name_ar,
            'url' => '/web/subcategories/' . $subcategory->slug,
        ];

        return $breadcrumb;
    }

    /**
     * Get category by ID (for internal use)
     */
    public function getCategoryById(int $categoryId): ?Category
    {
        return Category::where('id', $categoryId)
            ->where('status', 'active')
            ->first();
    }

    /**
     * Get subcategory by ID (for internal use)
     */
    public function getSubcategoryById(int $subcategoryId): ?Category
    {
        return Category::where('id', $subcategoryId)
            ->where('status', 'active')
            ->whereNotNull('parent_id')
            ->first();
    }

    /**
     * Get all active categories for navigation/dropdown purposes
     */
    public function getAllActiveCategories(): array
    {
        return Category::where('status', 'active')
            ->whereNull('parent_id')
            ->select(['id', 'name_en', 'name_ar', 'slug', 'icon'])
            ->orderBy('name_en')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name_en' => $category->name_en,
                    'name_ar' => $category->name_ar,
                    'slug' => $category->slug,
                    'icon_url' => $category->icon_url,
                ];
            })
            ->toArray();
    }

    /**
     * Get subcategories by parent category ID
     */
    public function getSubcategoriesByParentId(int $parentId): array
    {
        return Category::where('parent_id', $parentId)
            ->where('status', 'active')
            ->select(['id', 'name_en', 'name_ar', 'slug', 'icon'])
            ->orderBy('name_en')
            ->get()
            ->map(function ($subcategory) {
                return [
                    'id' => $subcategory->id,
                    'name_en' => $subcategory->name_en,
                    'name_ar' => $subcategory->name_ar,
                    'slug' => $subcategory->slug,
                    'icon_url' => $subcategory->icon_url,
                ];
            })
            ->toArray();
    }
}
