<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Brand;
use App\Models\Dropdown;
use App\Models\ProductClass;
use App\Models\Fulfilment;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\Warehouse;
use App\Traits\HelperTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class CommonService
{
    use HelperTrait;


    public function getMenuData($request)
    {
        try {
            $categories = Category::select('id', 'parent_id', 'name_en', 'name_ar', 'slug')
                ->whereNull('parent_id')
                ->with('subCategory:id,parent_id,name_en,name_ar,slug')
                ->get();

            // Generate slugs for categories that don't have them
            $categories->each(function ($category) {
                if (empty($category->slug) && !empty($category->name_en)) {
                    $category->slug = $this->generateSlugFromName($category->name_en);
                }

                // Generate slugs for subcategories
                $category->subCategory->each(function ($subCategory) {
                    if (empty($subCategory->slug) && !empty($subCategory->name_en)) {
                        $subCategory->slug = $this->generateSlugFromName($subCategory->name_en);
                    }
                });
            });

            return $categories;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    /**
     * Get consolidated dropdown data for admin panel
     * Caches the result for 30 minutes to improve performance
     */
    public function getConsolidatedDropdownData($request)
    {
        $cacheKey = 'admin_dropdown_data';
        $cacheDuration = 30 * 60; // 30 minutes

        return Cache::remember($cacheKey, $cacheDuration, function () use ($cacheDuration) {
            $data = [];
            $errors = [];

            // Safely fetch each dropdown type with error handling
            try {
                $data['categories'] = $this->getActiveCategories();
            } catch (\Throwable $e) {
                $errors['categories'] = $e->getMessage();
                $data['categories'] = [];
            }

            try {
                $data['brands'] = $this->getActiveBrands();
            } catch (\Throwable $e) {
                $errors['brands'] = $e->getMessage();
                $data['brands'] = [];
            }

            try {
                $data['dropdowns'] = $this->getActiveDropdowns();
            } catch (\Throwable $e) {
                $errors['dropdowns'] = $e->getMessage();
                $data['dropdowns'] = [];
            }

            try {
                $data['product_classes'] = $this->getActiveProductClasses();
            } catch (\Throwable $e) {
                $errors['product_classes'] = $e->getMessage();
                $data['product_classes'] = [];
            }

            try {
                $data['fulfilments'] = $this->getActiveFulfilments();
            } catch (\Throwable $e) {
                $errors['fulfilments'] = $e->getMessage();
                $data['fulfilments'] = [];
            }

            try {
                $data['support_categories'] = $this->getActiveSupportCategories();
            } catch (\Throwable $e) {
                $errors['support_categories'] = $e->getMessage();
                $data['support_categories'] = [];
            }

            try {
                $data['support_topics'] = $this->getActiveSupportTopics();
            } catch (\Throwable $e) {
                $errors['support_topics'] = $e->getMessage();
                $data['support_topics'] = [];
            }

            try {
                $data['warehouses'] = $this->getActiveWarehouses();
            } catch (\Throwable $e) {
                $errors['warehouses'] = $e->getMessage();
                $data['warehouses'] = [];
            }

            $data['meta'] = [
                'cached_at' => now()->toISOString(),
                'cache_duration' => $cacheDuration,
                'errors' => $errors,
                'has_errors' => !empty($errors),
            ];

            return $data;
        });
    }

    /**
     * Clear the consolidated dropdown data cache
     */
    public function clearDropdownDataCache()
    {
        Cache::forget('admin_dropdown_data');
    }

    private function getActiveCategories()
    {
        return Category::select(['id', 'name_en', 'name_ar', 'parent_id'])
            ->where('status', 'active')
            ->orderBy('name_en', 'asc')
            ->get()
            ->groupBy('parent_id');
    }

    private function getActiveBrands()
    {
        return Brand::select(['id', 'name_en', 'name_ar', 'slug'])
            ->where('is_active', true)
            ->where('status', 'approved')
            ->orderBy('name_en', 'asc')
            ->get();
    }

    private function getActiveDropdowns()
    {
        return Dropdown::with(['options' => function ($query) {
            $query->select(['id', 'dropdown_id', 'value_en', 'value_ar'])
                ->orderBy('sort_order', 'asc')
                ->orderBy('value_en', 'asc');
        }])
            ->select(['id', 'name_en', 'name_ar', 'slug'])
            ->orderBy('name_en', 'asc')
            ->get();
    }

    private function getActiveProductClasses()
    {
        return ProductClass::select(['id', 'name_en', 'name_ar', 'category_id', 'sub_category_id'])
            ->where('status', 'active')
            ->orderBy('name_en', 'asc')
            ->get()
            ->groupBy(['category_id', 'sub_category_id']);
    }

    private function getActiveFulfilments()
    {
        return Fulfilment::select(['id', 'name_en', 'name_ar'])
            ->where('is_active', true)
            ->orderBy('name_en', 'asc')
            ->get();
    }

    private function getActiveSupportCategories()
    {
        return SupportCategory::select(['id', 'name_en', 'name_ar'])
            ->where('status', 'active')
            ->orderBy('name_en', 'asc')
            ->get();
    }

    private function getActiveSupportTopics()
    {
        return SupportTopic::select(['id', 'name_en', 'name_ar', 'category_id'])
            ->where('status', 'active')
            ->orderBy('name_en', 'asc')
            ->get()
            ->groupBy('category_id');
    }

    private function getActiveWarehouses()
    {
        return Warehouse::select(['id', 'name_en', 'name_ar', 'location'])
            ->where('status', 'active')
            ->where('is_active', true)
            ->orderBy('name_en', 'asc')
            ->get();
    }

    /**
     * Generate SEO-friendly slug from name
     *
     * @param string $name
     * @return string
     */
    private function generateSlugFromName(string $name): string
    {
        return Str::slug($name);
    }
}
