<?php

namespace App\Services;

use App\Models\Category;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class StatusService
{
    use HelperTrait;

    public function statusUpdate($request, int $id, $modelClass)
    {
        $model = $modelClass::findOrFail($id);
        $status = $request->status;
        $model->status = $status;
        $model->save();
        return $model;
    }

    public function IsActiveUpdate($request, int $id, $modelClass)
    {
        $model = $modelClass::findOrFail($id);
        $status = $request->is_active;
        $model->is_active = $status;
        $model->save();
        return $model;
    }
}
