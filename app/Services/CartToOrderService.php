<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderCoupon;
use App\Models\ShoppingCart;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CartToOrderService
{
    protected OrderCalculationService $calculationService;
    protected OrderPricingService $pricingService;
    protected MemberPricingService $memberPricingService;

    public function __construct(
        OrderCalculationService $calculationService,
        OrderPricingService $pricingService,
        MemberPricingService $memberPricingService
    ) {
        $this->calculationService = $calculationService;
        $this->pricingService = $pricingService;
        $this->memberPricingService = $memberPricingService;
    }

    /**
     * Convert cart to order
     */
    public function convert(ShoppingCart $cart, array $orderData): Order
    {
        return DB::transaction(function () use ($cart, $orderData) {
            // Create the order
            $order = $this->createOrderFromCart($cart, $orderData);

            // Convert cart items to order items
            $this->convertCartItems($cart, $order);

            // Convert applied coupons
            $this->convertAppliedCoupons($cart, $order);

            // Calculate final totals
            $this->calculationService->calculateOrderTotals($order);

            // Mark cart as converted
            $cart->update(['status' => 'converted']);

            return $order;
        });
    }

    /**
     * Convert cart for multi-vendor orders
     */
    public function convertMultiVendor(ShoppingCart $cart, array $orderData): array
    {
        return DB::transaction(function () use ($cart, $orderData) {
            $orders = [];
            
            // Group cart items by vendor
            $vendorGroups = $cart->items->groupBy('vendor_id');

            foreach ($vendorGroups as $vendorId => $items) {
                // Create order for this vendor
                $vendorOrderData = array_merge($orderData, ['vendor_id' => $vendorId]);
                $order = $this->createOrderFromCart($cart, $vendorOrderData);

                // Convert items for this vendor
                $this->convertCartItemsForVendor($items, $order);

                // Convert vendor-specific coupons
                $this->convertVendorCoupons($cart, $order, $vendorId);

                // Calculate totals for this order
                $this->calculationService->calculateOrderTotals($order);

                $orders[] = $order;
            }

            // Mark cart as converted
            $cart->update(['status' => 'converted']);

            return $orders;
        });
    }

    /**
     * Create order from cart data
     */
    protected function createOrderFromCart(ShoppingCart $cart, array $orderData): Order
    {
        $customer = $cart->user?->customer;

        return Order::create([
            'uuid' => Str::uuid(),
            'user_id' => $cart->user_id,
            'vendor_id' => $orderData['vendor_id'] ?? null,
            'cart_id' => $cart->id,
            'currency' => $cart->currency,
            'payment_method' => $orderData['payment_method'] ?? null,
            'customer_note' => $orderData['customer_note'] ?? null,
            'admin_note' => $orderData['admin_note'] ?? null,
            'applied_coupons' => $cart->applied_coupons,
            'metadata' => array_merge(
                $cart->metadata ?? [],
                $orderData['metadata'] ?? [],
                [
                    'converted_from_cart' => $cart->uuid,
                    'conversion_timestamp' => now()->toISOString(),
                    'customer_tier' => $customer?->pricingTier?->code,
                ]
            ),
        ]);
    }

    /**
     * Convert cart items to order items
     */
    protected function convertCartItems(ShoppingCart $cart, Order $order): void
    {
        $customer = $cart->user?->customer;

        foreach ($cart->items as $cartItem) {
            $this->convertSingleCartItem($cartItem, $order, $customer);
        }
    }

    /**
     * Convert cart items for specific vendor
     */
    protected function convertCartItemsForVendor($cartItems, Order $order): void
    {
        $customer = $order->user?->customer;

        foreach ($cartItems as $cartItem) {
            $this->convertSingleCartItem($cartItem, $order, $customer);
        }
    }

    /**
     * Convert single cart item to order item
     */
    protected function convertSingleCartItem($cartItem, Order $order, ?Customer $customer): OrderItem
    {
        // Recalculate pricing to ensure accuracy at order time
        $pricingData = $this->calculationService->calculateItemPricing(
            $cartItem->product,
            $cartItem->quantity,
            $customer,
            $cartItem->variant_id
        );

        // Create comprehensive product snapshot
        $productSnapshot = $this->createProductSnapshot($cartItem);

        return OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $cartItem->product_id,
            'product_variant_id' => $cartItem->variant_id,
            'vendor_id' => $cartItem->vendor_id,
            'product_title' => $cartItem->product_title ?? $cartItem->product->name,
            'sku' => $cartItem->product->sku,
            'barcode' => $cartItem->product->barcode,
            'quantity' => $cartItem->quantity,
            'price' => $pricingData['unit_price'],
            'total' => $pricingData['total_price'],
            'discount' => $pricingData['discount_amount'],
            'tax' => $this->calculationService->calculateItemTax($cartItem),
            'base_price' => $pricingData['base_price'],
            'promotional_price' => $pricingData['promotional_price'],
            'member_price' => $pricingData['member_price'],
            'product_snapshot' => $productSnapshot,
            'applied_discounts' => $cartItem->applied_discounts,
            'customizations' => $cartItem->customizations,
            'special_instructions' => $cartItem->special_instructions,
            'metadata' => array_merge(
                $cartItem->metadata ?? [],
                [
                    'cart_item_id' => $cartItem->id,
                    'pricing_breakdown' => $pricingData['pricing_breakdown'],
                    'conversion_timestamp' => now()->toISOString(),
                ]
            ),
        ]);
    }

    /**
     * Convert applied coupons from cart
     */
    protected function convertAppliedCoupons(ShoppingCart $cart, Order $order): void
    {
        if (!$cart->applied_coupons) {
            return;
        }

        foreach ($cart->applied_coupons as $appliedCoupon) {
            $this->createOrderCouponFromCart($order, $appliedCoupon);
        }
    }

    /**
     * Convert vendor-specific coupons
     */
    protected function convertVendorCoupons(ShoppingCart $cart, Order $order, int $vendorId): void
    {
        if (!$cart->applied_coupons) {
            return;
        }

        foreach ($cart->applied_coupons as $appliedCoupon) {
            // Only convert coupons that apply to this vendor
            if (isset($appliedCoupon['vendor_id']) && $appliedCoupon['vendor_id'] == $vendorId) {
                $this->createOrderCouponFromCart($order, $appliedCoupon);
            } elseif (!isset($appliedCoupon['vendor_id'])) {
                // Global coupons apply to all vendors
                $this->createOrderCouponFromCart($order, $appliedCoupon);
            }
        }
    }

    /**
     * Create order coupon from cart coupon data
     */
    protected function createOrderCouponFromCart(Order $order, array $couponData): OrderCoupon
    {
        return OrderCoupon::create([
            'order_id' => $order->id,
            'coupon_id' => $couponData['coupon_id'] ?? null,
            'vendor_id' => $couponData['vendor_id'] ?? null,
            'coupon_code' => $couponData['code'],
            'coupon_title' => $couponData['title'],
            'coupon_type' => $couponData['type'],
            'coupon_value' => $couponData['value'],
            'min_order_value' => $couponData['min_order_value'] ?? null,
            'discount_amount' => $couponData['discount_amount'],
            'order_subtotal_at_application' => $couponData['order_subtotal'] ?? $order->subtotal,
            'applied_at' => now(),
            'metadata' => [
                'applied_in_cart' => true,
                'cart_application_data' => $couponData,
            ],
        ]);
    }

    /**
     * Create comprehensive product snapshot
     */
    protected function createProductSnapshot($cartItem): array
    {
        $product = $cartItem->product;
        $variant = $cartItem->variant;

        $snapshot = [
            'id' => $product->id,
            'name' => $product->name,
            'description' => $product->description,
            'sku' => $product->sku,
            'barcode' => $product->barcode,
            'regular_price' => $product->regular_price,
            'offer_price' => $product->offer_price,
            'member_price' => $product->member_price,
            'wholesale_price' => $product->wholesale_price,
            'vip_price' => $product->vip_price,
            'weight' => $product->weight,
            'dimensions' => $product->dimensions,
            'category_id' => $product->category_id,
            'brand_id' => $product->brand_id,
            'vendor_id' => $product->vendor_id,
            'images' => $product->images,
            'attributes' => $product->attributes,
            'metadata' => $product->metadata,
            'snapshot_timestamp' => now()->toISOString(),
        ];

        // Add variant data if applicable
        if ($variant) {
            $snapshot['variant'] = [
                'id' => $variant->id,
                'name' => $variant->name,
                'sku' => $variant->sku,
                'price' => $variant->price,
                'weight' => $variant->weight,
                'dimensions' => $variant->dimensions,
                'attributes' => $variant->attributes,
                'images' => $variant->images,
            ];
        }

        return $snapshot;
    }

    /**
     * Restore cart from order (for failed orders)
     */
    public function restoreCartFromOrder(Order $order): ShoppingCart
    {
        return DB::transaction(function () use ($order) {
            $cart = $order->cart;

            if (!$cart) {
                throw new \Exception('Original cart not found for order restoration.');
            }

            // Restore cart status
            $cart->update(['status' => 'active']);

            // Restore cart items if they were cleared
            if ($cart->items()->count() === 0) {
                foreach ($order->items as $orderItem) {
                    $cart->items()->create([
                        'product_id' => $orderItem->product_id,
                        'vendor_id' => $orderItem->vendor_id,
                        'variant_id' => $orderItem->product_variant_id,
                        'quantity' => $orderItem->quantity,
                        'unit_price' => $orderItem->price,
                        'total_price' => $orderItem->total,
                        'discount_amount' => $orderItem->discount,
                        'product_snapshot' => $orderItem->product_snapshot,
                        'customizations' => $orderItem->customizations,
                        'special_instructions' => $orderItem->special_instructions,
                        'metadata' => [
                            'restored_from_order' => $order->id,
                            'restoration_timestamp' => now()->toISOString(),
                        ],
                    ]);
                }
            }

            return $cart;
        });
    }
}
