<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderAddress;
use App\Models\OrderItem;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderService
{
    protected OrderCalculationService $calculationService;
    protected OrderValidationService $validationService;
    protected CartToOrderService $cartToOrderService;
    protected OrderStatusService $statusService;

    public function __construct(
        OrderCalculationService $calculationService,
        OrderValidationService $validationService,
        CartToOrderService $cartToOrderService,
        OrderStatusService $statusService
    ) {
        $this->calculationService = $calculationService;
        $this->validationService = $validationService;
        $this->cartToOrderService = $cartToOrderService;
        $this->statusService = $statusService;
    }

    /**
     * Create a new order from cart
     */
    public function createFromCart(ShoppingCart $cart, array $orderData): Order
    {
        return DB::transaction(function () use ($cart, $orderData) {
            // Validate cart and order data
            $this->validationService->validateCartForOrder($cart);
            $this->validationService->validateOrderData($orderData);

            // Convert cart to order
            $order = $this->cartToOrderService->convert($cart, $orderData);

            // Create addresses if provided
            if (isset($orderData['shipping_address'])) {
                $this->createOrderAddress($order, 'shipping', $orderData['shipping_address']);
            }

            if (isset($orderData['billing_address'])) {
                $this->createOrderAddress($order, 'billing', $orderData['billing_address']);
            }

            // Initialize order status
            $this->statusService->initializeOrderStatus($order);

            return $order;
        });
    }

    /**
     * Create a manual order (admin/vendor)
     */
    public function createManualOrder(array $orderData): Order
    {
        return DB::transaction(function () use ($orderData) {
            // Validate order data
            $this->validationService->validateOrderData($orderData);

            // Create order
            $order = Order::create([
                'uuid' => Str::uuid(),
                'user_id' => $orderData['user_id'],
                'vendor_id' => $orderData['vendor_id'] ?? null,
                'currency' => $orderData['currency'] ?? 'AED',
                'payment_method' => $orderData['payment_method'] ?? null,
                'customer_note' => $orderData['customer_note'] ?? null,
                'admin_note' => $orderData['admin_note'] ?? null,
                'metadata' => $orderData['metadata'] ?? null,
            ]);

            // Create order items
            if (isset($orderData['items'])) {
                foreach ($orderData['items'] as $itemData) {
                    $this->createOrderItem($order, $itemData);
                }
            }

            // Create addresses
            if (isset($orderData['shipping_address'])) {
                $this->createOrderAddress($order, 'shipping', $orderData['shipping_address']);
            }

            if (isset($orderData['billing_address'])) {
                $this->createOrderAddress($order, 'billing', $orderData['billing_address']);
            }

            // Calculate totals
            $this->calculationService->calculateOrderTotals($order);

            // Initialize status
            $this->statusService->initializeOrderStatus($order);

            return $order;
        });
    }

    /**
     * Update order
     */
    public function updateOrder(Order $order, array $updateData): Order
    {
        return DB::transaction(function () use ($order, $updateData) {
            // Validate update data
            $this->validationService->validateOrderUpdate($order, $updateData);

            // Update order fields
            $order->update($updateData);

            // Recalculate totals if items were modified
            if (isset($updateData['items'])) {
                $this->calculationService->calculateOrderTotals($order);
            }

            return $order->fresh();
        });
    }

    /**
     * Cancel order
     */
    public function cancelOrder(Order $order, ?string $reason = null, ?array $metadata = null): Order
    {
        return DB::transaction(function () use ($order, $reason, $metadata) {
            // Validate cancellation
            $this->validationService->validateOrderCancellation($order);

            // Update status
            $this->statusService->updateOrderStatus($order, 'cancelled', $reason, $metadata);

            // Handle refund if payment was made
            if ($order->payment_status === 'paid') {
                $this->statusService->updatePaymentStatus($order, 'refunded', 'Order cancelled');
            }

            return $order->fresh();
        });
    }

    /**
     * Get orders with filters and pagination
     */
    public function getOrders(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Order::with(['user', 'vendor', 'items.product', 'statusHistories'])
            ->active();

        // Apply filters
        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['vendor_id'])) {
            $query->where('vendor_id', $filters['vendor_id']);
        }

        if (isset($filters['status'])) {
            $query->where('fulfillment_status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get order by UUID
     */
    public function getOrderByUuid(string $uuid): ?Order
    {
        return Order::with([
            'user',
            'vendor',
            'items.product',
            'items.vendor',
            'statusHistories.user',
            'addresses',
            'coupons'
        ])->where('uuid', $uuid)->first();
    }

    /**
     * Get vendor orders
     */
    public function getVendorOrders(int $vendorId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $filters['vendor_id'] = $vendorId;
        return $this->getOrders($filters, $perPage);
    }

    /**
     * Get customer orders
     */
    public function getCustomerOrders(int $userId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $filters['user_id'] = $userId;
        return $this->getOrders($filters, $perPage);
    }

    /**
     * Create order address
     */
    protected function createOrderAddress(Order $order, string $type, array $addressData): OrderAddress
    {
        return OrderAddress::create([
            'order_id' => $order->id,
            'type' => $type,
            'first_name' => $addressData['first_name'],
            'last_name' => $addressData['last_name'],
            'company' => $addressData['company'] ?? null,
            'address_line_1' => $addressData['address_line_1'],
            'address_line_2' => $addressData['address_line_2'] ?? null,
            'city' => $addressData['city'],
            'state' => $addressData['state'] ?? null,
            'postal_code' => $addressData['postal_code'] ?? null,
            'country' => $addressData['country'],
            'phone' => $addressData['phone'] ?? null,
            'email' => $addressData['email'] ?? null,
            'special_instructions' => $addressData['special_instructions'] ?? null,
            'metadata' => $addressData['metadata'] ?? null,
        ]);
    }

    /**
     * Create order item
     */
    protected function createOrderItem(Order $order, array $itemData): OrderItem
    {
        return OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $itemData['product_id'],
            'product_variant_id' => $itemData['product_variant_id'] ?? null,
            'vendor_id' => $itemData['vendor_id'],
            'product_title' => $itemData['product_title'],
            'sku' => $itemData['sku'] ?? null,
            'barcode' => $itemData['barcode'] ?? null,
            'quantity' => $itemData['quantity'],
            'price' => $itemData['price'],
            'base_price' => $itemData['base_price'],
            'promotional_price' => $itemData['promotional_price'] ?? null,
            'member_price' => $itemData['member_price'] ?? null,
            'discount' => $itemData['discount'] ?? 0,
            'tax' => $itemData['tax'] ?? 0,
            'product_snapshot' => $itemData['product_snapshot'],
            'applied_discounts' => $itemData['applied_discounts'] ?? null,
            'customizations' => $itemData['customizations'] ?? null,
            'special_instructions' => $itemData['special_instructions'] ?? null,
            'metadata' => $itemData['metadata'] ?? null,
        ]);
    }

    /**
     * Get order analytics
     */
    public function getOrderAnalytics(array $filters = []): array
    {
        $query = Order::active();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['vendor_id'])) {
            $query->where('vendor_id', $filters['vendor_id']);
        }

        return [
            'total_orders' => $query->count(),
            'total_revenue' => $query->where('payment_status', 'paid')->sum('total'),
            'pending_orders' => $query->where('fulfillment_status', 'pending')->count(),
            'processing_orders' => $query->where('fulfillment_status', 'processing')->count(),
            'shipped_orders' => $query->where('fulfillment_status', 'shipped')->count(),
            'delivered_orders' => $query->where('fulfillment_status', 'delivered')->count(),
            'cancelled_orders' => $query->where('fulfillment_status', 'cancelled')->count(),
            'average_order_value' => $query->where('payment_status', 'paid')->avg('total'),
        ];
    }
}
