<?php

namespace App\Services;

use App\Models\CartItem;
use App\Models\CartSession;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CartService
{
    protected CartCalculationService $calculationService;
    protected CartValidationService $validationService;

    public function __construct(
        CartCalculationService $calculationService,
        CartValidationService $validationService
    ) {
        $this->calculationService = $calculationService;
        $this->validationService = $validationService;
    }

    /**
     * Create a new shopping cart.
     */
    public function createCart(array $data): ShoppingCart
    {
        $cartData = array_merge([
            'uuid' => Str::uuid(),
            'currency' => 'AED',
            'status' => 'active',
            'expires_at' => auth()->check() ? now()->addDays(30) : now()->addDays(7),
        ], $data);

        return ShoppingCart::create($cartData);
    }

    /**
     * Get cart by UUID or create new one.
     */
    public function getOrCreateCart(?string $cartUuid = null, ?string $sessionId = null): ShoppingCart
    {
        // For authenticated users
        if (auth()->check()) {
            $cart = ShoppingCart::where('user_id', auth()->id())
                ->where('status', 'active')
                ->first();

            if (!$cart) {
                $cart = $this->createCart([
                    'user_id' => auth()->id(),
                    'expires_at' => now()->addDays(30),
                ]);
            }

            return $cart;
        }

        // For guest users
        $sessionId = $sessionId ?? session()->getId();

        if ($cartUuid) {
            $cart = ShoppingCart::where('uuid', $cartUuid)
                ->where('session_id', $sessionId)
                ->where('status', 'active')
                ->first();

            if ($cart && !$cart->hasExpired()) {
                return $cart;
            }
        }

        // Create new guest cart
        return $this->createCart([
            'session_id' => $sessionId,
            'expires_at' => now()->addDays(7),
        ]);
    }

    /**
     * Add item to cart.
     */
    public function addItem(ShoppingCart $cart, array $itemData): CartItem
    {
        return DB::transaction(function () use ($cart, $itemData) {
            // Validate the item can be added
            $this->validationService->validateAddToCart($cart, $itemData);

            // Get product and variant information
            $product = Product::findOrFail($itemData['product_id']);
            $variant = isset($itemData['variant_id']) ? 
                ProductVariant::findOrFail($itemData['variant_id']) : null;

            // Check if item already exists in cart
            $existingItem = $cart->items()
                ->where('product_id', $itemData['product_id'])
                ->where('variant_id', $itemData['variant_id'] ?? null)
                ->first();

            if ($existingItem) {
                // Update existing item quantity
                $newQuantity = $existingItem->quantity + $itemData['quantity'];
                return $this->updateItemQuantity($existingItem, $newQuantity);
            }

            // Create new cart item
            $cartItemData = array_merge($itemData, [
                'cart_id' => $cart->id,
                'vendor_id' => $product->vendor_id,
                'unit_price' => $variant ? $variant->price : $product->price,
                'base_price' => $variant ? 
                    ($variant->original_price ?? $variant->price) : 
                    ($product->original_price ?? $product->price),
                'total_price' => ($variant ? $variant->price : $product->price) * $itemData['quantity'],
                'product_snapshot' => $this->createProductSnapshot($product, $variant),
            ]);

            $cartItem = CartItem::create($cartItemData);

            // Recalculate cart totals
            $this->calculationService->recalculateCart($cart);

            return $cartItem;
        });
    }

    /**
     * Update cart item.
     */
    public function updateItem(CartItem $item, array $data): CartItem
    {
        return DB::transaction(function () use ($item, $data) {
            // If quantity is 0, remove the item
            if (isset($data['quantity']) && $data['quantity'] === 0) {
                $this->removeItem($item);
                return $item;
            }

            // Validate the update
            if (isset($data['quantity'])) {
                $this->validationService->validateQuantityUpdate($item, $data['quantity']);
            }

            // Update the item
            $item->update($data);

            // Recalculate cart totals
            $this->calculationService->recalculateCart($item->cart);

            return $item->fresh();
        });
    }

    /**
     * Update item quantity specifically.
     */
    public function updateItemQuantity(CartItem $item, int $quantity): CartItem
    {
        return $this->updateItem($item, ['quantity' => $quantity]);
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(CartItem $item): bool
    {
        return DB::transaction(function () use ($item) {
            $cart = $item->cart;
            $deleted = $item->delete();

            if ($deleted) {
                // Recalculate cart totals
                $this->calculationService->recalculateCart($cart);
            }

            return $deleted;
        });
    }

    /**
     * Bulk update cart items.
     */
    public function bulkUpdateItems(ShoppingCart $cart, array $updates): Collection
    {
        return DB::transaction(function () use ($cart, $updates) {
            $updatedItems = collect();

            foreach ($updates as $update) {
                $item = CartItem::where('id', $update['id'])
                    ->where('cart_id', $cart->id)
                    ->first();

                if ($item) {
                    if ($update['quantity'] === 0) {
                        $this->removeItem($item);
                    } else {
                        $updatedItems->push($this->updateItem($item, $update));
                    }
                }
            }

            return $updatedItems;
        });
    }

    /**
     * Clear all items from cart.
     */
    public function clearCart(ShoppingCart $cart): bool
    {
        return DB::transaction(function () use ($cart) {
            $cart->items()->delete();
            $cart->update([
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'shipping_amount' => 0,
                'total_amount' => 0,
                'applied_coupons' => null,
                'applied_discounts' => null,
            ]);

            return true;
        });
    }

    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(ShoppingCart $cart, string $couponCode): array
    {
        return DB::transaction(function () use ($cart, $couponCode) {
            $discount = $this->calculationService->applyCoupon($cart, $couponCode);
            $this->calculationService->recalculateCart($cart);

            return [
                'coupon_code' => $couponCode,
                'discount_amount' => $discount,
                'cart_total' => $cart->total_amount,
            ];
        });
    }

    /**
     * Remove coupon from cart.
     */
    public function removeCoupon(ShoppingCart $cart, string $couponCode): bool
    {
        return DB::transaction(function () use ($cart, $couponCode) {
            $appliedCoupons = $cart->applied_coupons ?? [];
            $appliedCoupons = array_filter($appliedCoupons, function ($coupon) use ($couponCode) {
                return $coupon['code'] !== $couponCode;
            });

            $cart->update(['applied_coupons' => array_values($appliedCoupons)]);
            $this->calculationService->recalculateCart($cart);

            return true;
        });
    }

    /**
     * Merge two carts.
     */
    public function mergeCarts(ShoppingCart $targetCart, ShoppingCart $sourceCart): ShoppingCart
    {
        return DB::transaction(function () use ($targetCart, $sourceCart) {
            foreach ($sourceCart->items as $sourceItem) {
                $existingItem = $targetCart->items()
                    ->where('product_id', $sourceItem->product_id)
                    ->where('variant_id', $sourceItem->variant_id)
                    ->first();

                if ($existingItem) {
                    // Merge quantities
                    $newQuantity = $existingItem->quantity + $sourceItem->quantity;
                    $this->updateItemQuantity($existingItem, $newQuantity);
                } else {
                    // Move item to target cart
                    $sourceItem->update(['cart_id' => $targetCart->id]);
                }
            }

            // Mark source cart as converted
            $sourceCart->update(['status' => 'converted']);

            // Recalculate target cart
            $this->calculationService->recalculateCart($targetCart);

            return $targetCart->fresh();
        });
    }

    /**
     * Migrate guest cart to user account.
     */
    public function migrateGuestCart(User $user, string $guestSessionId, string $strategy = 'merge'): ?ShoppingCart
    {
        return DB::transaction(function () use ($user, $guestSessionId, $strategy) {
            // Try to find guest cart in cart_sessions table
            $cartSession = CartSession::where('session_id', $guestSessionId)
                ->where('is_migrated', false)
                ->first();

            if ($cartSession) {
                return $cartSession->migrateToUser($user);
            }

            // Try to find guest cart in shopping_carts table
            $guestCart = ShoppingCart::where('session_id', $guestSessionId)
                ->where('status', 'active')
                ->whereNull('user_id')
                ->first();

            if (!$guestCart || $guestCart->isEmpty()) {
                return null;
            }

            // Get or create user cart
            $userCart = ShoppingCart::firstOrCreate([
                'user_id' => $user->id,
                'status' => 'active',
            ], [
                'currency' => $guestCart->currency,
                'expires_at' => now()->addDays(30),
            ]);

            if ($strategy === 'replace' || $userCart->isEmpty()) {
                // Replace user cart with guest cart
                $userCart->items()->delete();
                $guestCart->items()->update(['cart_id' => $userCart->id]);
            } else {
                // Merge carts
                $this->mergeCarts($userCart, $guestCart);
            }

            // Mark guest cart as converted
            $guestCart->update(['status' => 'converted']);

            return $userCart->fresh();
        });
    }

    /**
     * Get cart vendor groups for multi-vendor checkout.
     */
    public function getVendorGroups(ShoppingCart $cart): Collection
    {
        return $cart->items()
            ->with(['vendor', 'product'])
            ->get()
            ->groupBy('vendor_id')
            ->map(function ($items, $vendorId) {
                $vendor = $items->first()->vendor;
                
                return [
                    'vendor_id' => $vendorId,
                    'vendor' => $vendor,
                    'items' => $items,
                    'subtotal' => $items->sum('total_price'),
                    'items_count' => $items->count(),
                    'total_quantity' => $items->sum('quantity'),
                    'shipping_required' => $this->vendorRequiresShipping($vendor, $items),
                    'min_order_met' => $this->checkMinOrderValue($vendor, $items->sum('total_price')),
                ];
            });
    }

    /**
     * Create product snapshot for cart item.
     */
    protected function createProductSnapshot(Product $product, ?ProductVariant $variant = null): array
    {
        return [
            'id' => $product->id,
            'name' => $product->name_en,
            'name_ar' => $product->name_ar,
            'sku' => $product->sku,
            'image' => $product->featured_image,
            'description' => $product->description_en,
            'variant' => $variant ? [
                'id' => $variant->id,
                'sku' => $variant->sku,
                'name' => $variant->name,
                'attributes' => $variant->attributes ?? [],
            ] : null,
            'vendor' => [
                'id' => $product->vendor_id,
                'name' => $product->vendor->name_tl_en ?? 'Unknown',
                'display_name' => $product->vendor->vendor_display_name_en ?? 'Unknown',
            ],
            'captured_at' => now()->toISOString(),
        ];
    }

    /**
     * Check if vendor requires shipping.
     */
    protected function vendorRequiresShipping($vendor, Collection $items): bool
    {
        // Check if any items require shipping
        return $items->contains(function ($item) {
            return $item->product->requires_shipping ?? true;
        });
    }

    /**
     * Check if minimum order value is met for vendor.
     */
    protected function checkMinOrderValue($vendor, float $subtotal): bool
    {
        return !$vendor->min_order_value || $subtotal >= $vendor->min_order_value;
    }
}
