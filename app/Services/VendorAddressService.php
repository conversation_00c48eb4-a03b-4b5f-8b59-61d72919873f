<?php

namespace App\Services;

use App\Models\VendorAddress;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorAddressService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorAddress::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['address']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store($request)
    {
        $data = $this->prepareVendorAddressData($request);

        return VendorAddress::create($data);
    }

    private function prepareVendorAddressData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorAddress())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorAddress')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorAddress');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorAddress');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorAddress
    {
        return VendorAddress::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorAddress = VendorAddress::findOrFail($id);
        $updateData = $this->prepareVendorAddressData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorAddress->update($updateData);

        return $vendorAddress;
    }

    public function destroy(int $id): bool
    {
        $vendorAddress = VendorAddress::findOrFail($id);
        return $vendorAddress->delete();
    }
}
