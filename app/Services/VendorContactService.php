<?php

namespace App\Services;

use App\Models\VendorContact;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorContactService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorContact::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['full_name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorContactData($request);

        return VendorContact::create($data);
    }

    private function prepareVendorContactData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorContact())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorContact')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorContact');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorContact');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorContact
    {
        return VendorContact::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorContact = VendorContact::findOrFail($id);
        $updateData = $this->prepareVendorContactData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorContact->update($updateData);

        return $vendorContact;
    }

    public function destroy(int $id): bool
    {
        $vendorContact = VendorContact::findOrFail($id);
        return $vendorContact->delete();
    }
}
