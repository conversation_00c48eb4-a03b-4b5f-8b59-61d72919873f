<?php

namespace App\Services;

use App\Models\Blog;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BlogService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Blog::query();

        $query->with('category');

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['blog_category_id' => '=','status' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);
        // Searching
        $searchKeys = ['title_en', 'title_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareBlogData($request);

        return Blog::create($data);
    }

    private function prepareBlogData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Blog())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Handle file uploads
        // $data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'blog')['path'] ?? null;        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'blog');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Blog
    {
        return Blog::with('category')->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $blog = Blog::findOrFail($id);
        $updateData = $this->prepareBlogData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $blog->update($updateData);

        return $blog;
    }

    public function destroy(int $id): bool
    {
        $blog = Blog::findOrFail($id);
        return $blog->delete();
    }
}
