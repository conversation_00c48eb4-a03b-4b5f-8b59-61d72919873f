<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateVariable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EmailTemplateService
{
    /**
     * Render a template with provided variables.
     *
     * @param EmailTemplate $template
     * @param array $variables
     * @return array
     */
    public function renderTemplate(EmailTemplate $template, array $variables = []): array
    {
        try {
            // Merge with default variables
            $mergedVariables = $this->mergeWithDefaults($variables);
            
            // Validate required variables
            $this->validateRequiredVariables($template, $mergedVariables);
            
            // Render subject
            $renderedSubject = $this->renderContent($template->subject, $mergedVariables);
            
            // Render HTML body
            $renderedHtml = $this->renderContent($template->body_html, $mergedVariables);
            
            // Render text body (if exists)
            $renderedText = $template->body_text 
                ? $this->renderContent($template->body_text, $mergedVariables)
                : null;
            
            return [
                'subject' => $renderedSubject,
                'body_html' => $renderedHtml,
                'body_text' => $renderedText,
            ];
            
        } catch (\Exception $e) {
            Log::error('Template rendering failed', [
                'template_id' => $template->id,
                'template_name' => $template->name,
                'error' => $e->getMessage(),
                'variables' => $variables,
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'subject' => null,
                'body_html' => null,
                'body_text' => null,
            ];
        }
    }

    /**
     * Render content by replacing variables.
     *
     * @param string $content
     * @param array $variables
     * @return string
     */
    private function renderContent(string $content, array $variables): string
    {
        // Replace simple variables like {{user.name}}
        $rendered = preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($variables) {
            $key = trim($matches[1]);
            return $this->getVariableValue($key, $variables);
        }, $content);

        // Handle conditional blocks like {{#if condition}}...{{/if}}
        $rendered = $this->processConditionals($rendered, $variables);
        
        // Handle loops like {{#each items}}...{{/each}}
        $rendered = $this->processLoops($rendered, $variables);
        
        return $rendered;
    }

    /**
     * Get variable value with dot notation support.
     *
     * @param string $key
     * @param array $variables
     * @return string
     */
    private function getVariableValue(string $key, array $variables): string
    {
        // First try direct key lookup (for flat structure like 'user.name' => 'John')
        if (isset($variables[$key])) {
            $value = $variables[$key];
        } else {
            // Handle dot notation (e.g., user.name, order.total)
            $keys = explode('.', $key);
            $value = $variables;

            foreach ($keys as $k) {
                if (is_array($value) && isset($value[$k])) {
                    $value = $value[$k];
                } elseif (is_object($value) && isset($value->$k)) {
                    $value = $value->$k;
                } else {
                    // Return placeholder if variable not found
                    return "{{$key}}";
                }
            }
        }

        // Handle different data types
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_null($value)) {
            return '';
        }

        // HTML escape the value for security
        return htmlspecialchars((string) $value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Process conditional blocks in template.
     *
     * @param string $content
     * @param array $variables
     * @return string
     */
    private function processConditionals(string $content, array $variables): string
    {
        return preg_replace_callback('/\{\{#if\s+([^}]+)\}\}(.*?)\{\{\/if\}\}/s', function ($matches) use ($variables) {
            $condition = trim($matches[1]);
            $block = $matches[2];
            
            $conditionValue = $this->evaluateCondition($condition, $variables);
            
            return $conditionValue ? $block : '';
        }, $content);
    }

    /**
     * Process loop blocks in template.
     *
     * @param string $content
     * @param array $variables
     * @return string
     */
    private function processLoops(string $content, array $variables): string
    {
        return preg_replace_callback('/\{\{#each\s+([^}]+)\}\}(.*?)\{\{\/each\}\}/s', function ($matches) use ($variables) {
            $arrayKey = trim($matches[1]);
            $block = $matches[2];
            
            $arrayValue = $this->getVariableValue($arrayKey, $variables);
            
            if (!is_array($arrayValue)) {
                return '';
            }
            
            $result = '';
            foreach ($arrayValue as $index => $item) {
                $itemVariables = array_merge($variables, [
                    'this' => $item,
                    '@index' => $index,
                    '@first' => $index === 0,
                    '@last' => $index === count($arrayValue) - 1,
                ]);
                
                $result .= $this->renderContent($block, $itemVariables);
            }
            
            return $result;
        }, $content);
    }

    /**
     * Evaluate a condition for conditional blocks.
     *
     * @param string $condition
     * @param array $variables
     * @return bool
     */
    private function evaluateCondition(string $condition, array $variables): bool
    {
        // Simple condition evaluation (can be extended)
        $value = $this->getVariableValue($condition, $variables);
        
        // Consider empty strings, null, false, 0 as falsy
        return !empty($value) && $value !== '0' && $value !== 0;
    }

    /**
     * Merge provided variables with system defaults.
     *
     * @param array $variables
     * @return array
     */
    private function mergeWithDefaults(array $variables): array
    {
        $defaults = [
            'site' => [
                'name' => config('app.name', 'Vitamins.ae'),
                'url' => config('app.url', 'https://vitamins.ae'),
                'support_email' => config('mail.support_address', '<EMAIL>'),
            ],
            'current' => [
                'date' => now()->format('Y-m-d'),
                'datetime' => now()->format('Y-m-d H:i:s'),
                'year' => now()->year,
            ],
        ];
        
        return array_merge_recursive($defaults, $variables);
    }

    /**
     * Validate that all required variables are provided.
     *
     * @param EmailTemplate $template
     * @param array $variables
     * @throws \InvalidArgumentException
     */
    private function validateRequiredVariables(EmailTemplate $template, array $variables): void
    {
        $requiredVariables = EmailTemplateVariable::where('is_required', true)->pluck('key')->toArray();
        $templateVariables = $this->extractVariablesFromContent($template);
        
        $missingVariables = [];
        
        foreach ($templateVariables as $variable) {
            if (in_array($variable, $requiredVariables)) {
                $value = $this->getVariableValue($variable, $variables);
                if ($value === "{{$variable}}") { // Variable not found
                    $missingVariables[] = $variable;
                }
            }
        }
        
        if (!empty($missingVariables)) {
            throw new \InvalidArgumentException(
                'Missing required variables: ' . implode(', ', $missingVariables)
            );
        }
    }

    /**
     * Extract all variables used in template content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function extractVariablesFromContent(EmailTemplate $template): array
    {
        $content = $template->subject . ' ' . $template->body_html . ' ' . ($template->body_text ?? '');
        preg_match_all('/\{\{([^}#\/]+)\}\}/', $content, $matches);
        
        return array_unique(array_map('trim', $matches[1] ?? []));
    }

    /**
     * Preview template with sample data.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function previewTemplate(EmailTemplate $template): array
    {
        $sampleData = $this->generateSampleData($template);
        return $this->renderTemplate($template, $sampleData);
    }

    /**
     * Generate preview with sample data (alias for previewTemplate).
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function generatePreview(EmailTemplate $template): array
    {
        return $this->previewTemplate($template);
    }

    /**
     * Extract variables from template content.
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function extractVariables(EmailTemplate $template): array
    {
        return $this->extractVariablesFromContent($template);
    }

    /**
     * Validate template syntax.
     *
     * @param EmailTemplate $template
     * @return bool
     */
    public function validateTemplateSyntax(EmailTemplate $template): bool
    {
        try {
            // Check for balanced braces
            $content = $template->subject . ' ' . $template->body_html . ' ' . $template->body_text;

            // Count opening and closing braces
            $openBraces = substr_count($content, '{{');
            $closeBraces = substr_count($content, '}}');

            if ($openBraces !== $closeBraces) {
                return false;
            }

            // Check for valid variable syntax
            preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
            foreach ($matches[1] as $variable) {
                $variable = trim($variable);
                if (empty($variable)) {
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate sample data for template preview.
     *
     * @param EmailTemplate $template
     * @return array
     */
    private function generateSampleData(EmailTemplate $template): array
    {
        $variables = $this->extractVariablesFromContent($template);
        $sampleData = [];
        
        foreach ($variables as $variable) {
            $templateVar = EmailTemplateVariable::where('key', $variable)->first();
            if ($templateVar && $templateVar->example_value) {
                $this->setNestedValue($sampleData, $variable, $templateVar->example_value);
            }
        }
        
        return $sampleData;
    }

    /**
     * Set nested array value using dot notation.
     *
     * @param array &$array
     * @param string $key
     * @param mixed $value
     */
    private function setNestedValue(array &$array, string $key, $value): void
    {
        $keys = explode('.', $key);
        $current = &$array;

        // Navigate to the parent of the final key
        for ($i = 0; $i < count($keys) - 1; $i++) {
            $k = $keys[$i];
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }

        // Set the final value
        $finalKey = end($keys);
        $current[$finalKey] = $value;
    }

    /**
     * Generate sample data for all available variables (public method).
     *
     * @return array
     */
    public function generateAllSampleData(): array
    {
        $variables = EmailTemplateVariable::all();
        $sampleData = [];

        foreach ($variables as $variable) {
            $sampleData[$variable->key] = $this->generateSampleValueForVariable($variable);
        }

        return $sampleData;
    }

    /**
     * Generate sample value for a specific variable.
     *
     * @param EmailTemplateVariable $variable
     * @return mixed
     */
    private function generateSampleValueForVariable(EmailTemplateVariable $variable)
    {
        if ($variable->example_value) {
            return $variable->example_value;
        }

        return match ($variable->data_type) {
            'string' => $this->generateSampleString($variable->category, $variable->key),
            'number' => $this->generateSampleNumber($variable->key),
            'boolean' => $this->generateSampleBoolean(),
            'date' => now()->format('Y-m-d'),
            'array' => $this->generateSampleArray($variable->key),
            default => 'Sample Value',
        };
    }

    /**
     * Generate sample string based on category and key.
     *
     * @param string $category
     * @param string $key
     * @return string
     */
    private function generateSampleString(string $category, string $key): string
    {
        return match ($category) {
            'user' => match (true) {
                str_contains($key, 'name') => 'Sample User',
                str_contains($key, 'email') => '<EMAIL>',
                str_contains($key, 'phone') => '+971501234567',
                default => 'Sample User Data',
            },
            'order' => match (true) {
                str_contains($key, 'number') => 'ORD-' . rand(100000, 999999),
                str_contains($key, 'status') => 'Processing',
                default => 'Sample Order Data',
            },
            'vendor' => match (true) {
                str_contains($key, 'name') => 'Sample Vendor',
                str_contains($key, 'email') => '<EMAIL>',
                default => 'Sample Vendor Data',
            },
            'site' => match (true) {
                str_contains($key, 'name') => 'Vitamins.ae',
                str_contains($key, 'url') => 'https://vitamins.ae',
                default => 'Sample Site Data',
            },
            default => 'Sample Data',
        };
    }

    /**
     * Generate sample number.
     *
     * @param string $key
     * @return float
     */
    private function generateSampleNumber(string $key): float
    {
        if (str_contains($key, 'price') || str_contains($key, 'total') || str_contains($key, 'amount')) {
            return round(rand(1000, 50000) / 100, 2); // Random price between 10.00 and 500.00
        }

        if (str_contains($key, 'quantity') || str_contains($key, 'count')) {
            return rand(1, 10);
        }

        return rand(1, 1000);
    }

    /**
     * Generate sample boolean.
     *
     * @return bool
     */
    private function generateSampleBoolean(): bool
    {
        return (bool) rand(0, 1);
    }

    /**
     * Generate sample array.
     *
     * @param string $key
     * @return array
     */
    private function generateSampleArray(string $key): array
    {
        if (str_contains($key, 'items') || str_contains($key, 'products')) {
            return [
                ['name' => 'Sample Product 1', 'price' => 29.99],
                ['name' => 'Sample Product 2', 'price' => 19.99],
            ];
        }

        return ['Sample', 'Array', 'Data'];
    }
}
