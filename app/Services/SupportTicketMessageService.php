<?php

namespace App\Services;

use App\Models\SupportTicketMessage;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportTicketMessageService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportTicketMessage::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function getThreadedMessages(int $ticketId, Request $request): Collection|LengthAwarePaginator|array
    {
        $query = SupportTicketMessage::where('ticket_id', $ticketId)
            ->with(['sender', 'attachments'])
            ->orderBy('created_at', 'asc');

        // Mark messages as read for current user
        SupportTicketMessage::where('ticket_id', $ticketId)
            ->where('sender_id', '!=', auth()->id())
            ->update(['is_read' => true]);

        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportTicketMessageData($request);

        $message = SupportTicketMessage::create($data);

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            $this->handleAttachments($request, $message);
        }

        // Update ticket status and notify relevant parties
        $this->updateTicketOnNewMessage($message);

        return $message->load(['attachments', 'sender', 'ticket']);
    }

    private function prepareSupportTicketMessageData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new SupportTicketMessage())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['sender_id'] = auth()->id();
            $data['sender_type'] = get_class(auth()->user());
            $data['created_at'] = now();
        }

        return $data;
    }

    private function handleAttachments(Request $request, SupportTicketMessage $message): void
    {
        foreach ($request->file('attachments') as $file) {
            $upload = $this->s3FileUpload($request, 'attachments', 'supportTicketMessage', $file);

            $message->attachments()->create([
                'file_path' => $upload['path'] ?? null,
                'file_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getClientMimeType(),
                'file_size' => $file->getSize(),
            ]);
        }
    }

    private function updateTicketOnNewMessage(SupportTicketMessage $message): void
    {
        $ticket = $message->ticket;

        // Update ticket status if it was closed/resolved
        if (in_array($ticket->status, ['resolved', 'closed'])) {
            $ticket->update(['status' => 'in_progress']);
        }

        // Mark other messages as read for the sender
        SupportTicketMessage::where('ticket_id', $ticket->id)
            ->where('sender_id', '!=', $message->sender_id)
            ->where('sender_type', '!=', $message->sender_type)
            ->update(['is_read' => true]);

        // Send notifications to relevant parties
        $this->sendMessageNotifications($message);
    }

    private function sendMessageNotifications(SupportTicketMessage $message): void
    {
        $ticket = $message->ticket;
        $sender = $message->sender;

        // Notify assigned user if different from sender
        if ($ticket->assigned_to && $ticket->assigned_to != $message->sender_id) {
            \App\Models\Notification::create([
                'type' => 'support_message_reply',
                'notifiable_type' => 'App\Models\User',
                'notifiable_id' => $ticket->assigned_to,
                'data' => [
                    'ticket_id' => $ticket->id,
                    'ticket_code' => $ticket->code,
                    'message_id' => $message->id,
                    'sender_name' => $sender->name ?? 'Unknown',
                    'message_preview' => substr($message->message, 0, 100)
                ]
            ]);
        }

        // Notify ticket creator if different from sender and assigned user
        if ($ticket->user_id != $message->sender_id && $ticket->user_id != $ticket->assigned_to) {
            \App\Models\Notification::create([
                'type' => 'support_message_reply',
                'notifiable_type' => 'App\Models\User',
                'notifiable_id' => $ticket->user_id,
                'data' => [
                    'ticket_id' => $ticket->id,
                    'ticket_code' => $ticket->code,
                    'message_id' => $message->id,
                    'sender_name' => $sender->name ?? 'Unknown',
                    'message_preview' => substr($message->message, 0, 100)
                ]
            ]);
        }
    }

    public function show(int $id): SupportTicketMessage
    {
        return SupportTicketMessage::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $supportTicketMessage = SupportTicketMessage::findOrFail($id);
        $updateData = $this->prepareSupportTicketMessageData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportTicketMessage->update($updateData);

        if ($request->hasFile('attachments')) {
            // Delete old attachments
            foreach ($supportTicketMessage->attachments as $attachment) {
                Storage::disk('public')->delete($attachment->file_path);
                $attachment->delete();
            }

            // Upload new attachments
            foreach ($request->file('attachments') as $file) {
                $upload = $this->s3FileUpload($request, 'attachments', 'supportTicketMessage', $file);

                $supportTicketMessage->attachments()->create([
                    'file_path' => $upload['path'] ?? null,
                    'file_name' => $file->getClientOriginalName(),
                    'mime_type' => $file->getClientMimeType(),
                    'file_size' => $file->getSize(),
                ]);
            }
        }

        return $supportTicketMessage->load('attachments');
    }

    public function destroy(int $id): bool
    {
        $supportTicketMessage = SupportTicketMessage::findOrFail($id);
        return $supportTicketMessage->delete();
    }
}
