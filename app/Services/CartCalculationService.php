<?php

namespace App\Services;

use App\Models\Coupon;
use App\Models\ShoppingCart;
use Illuminate\Support\Collection;

class CartCalculationService
{
    /**
     * Recalculate all cart totals.
     */
    public function recalculateCart(ShoppingCart $cart): ShoppingCart
    {
        $cart->load('items.product', 'items.vendor');

        // Calculate subtotal
        $subtotal = $this->calculateSubtotal($cart->items);

        // Calculate tax
        $taxAmount = $this->calculateTax($cart);

        // Calculate shipping
        $shippingAmount = $this->calculateShipping($cart);

        // Calculate discounts
        $discountAmount = $this->calculateDiscounts($cart);

        // Calculate total
        $totalAmount = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

        // Update cart
        $cart->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => max(0, $totalAmount), // Ensure total is never negative
        ]);

        return $cart->fresh();
    }

    /**
     * Calculate subtotal from cart items.
     */
    public function calculateSubtotal(Collection $items): float
    {
        return $items->sum('total_price');
    }

    /**
     * Calculate tax for the cart.
     */
    public function calculateTax(ShoppingCart $cart): float
    {
        // UAE VAT is 5% on most items
        $taxRate = 0.05;
        $taxableAmount = 0;

        foreach ($cart->items as $item) {
            // Check if product is taxable
            $isTaxable = $item->product->is_taxable ?? true;
            
            if ($isTaxable) {
                $taxableAmount += $item->total_price - $item->discount_amount;
            }
        }

        return round($taxableAmount * $taxRate, 2);
    }

    /**
     * Calculate shipping for the cart.
     */
    public function calculateShipping(ShoppingCart $cart): float
    {
        $vendorGroups = $cart->items->groupBy('vendor_id');
        $totalShipping = 0;

        foreach ($vendorGroups as $vendorId => $items) {
            $vendor = $items->first()->vendor;
            $vendorSubtotal = $items->sum('total_price');

            // Check if free shipping threshold is met
            if ($vendor->free_shipping_threshold && $vendorSubtotal >= $vendor->free_shipping_threshold) {
                continue; // Free shipping for this vendor
            }

            // Calculate shipping for this vendor
            $vendorShipping = $this->calculateVendorShipping($vendor, $items, $vendorSubtotal);
            $totalShipping += $vendorShipping;
        }

        return $totalShipping;
    }

    /**
     * Calculate shipping for a specific vendor.
     */
    protected function calculateVendorShipping($vendor, Collection $items, float $subtotal): float
    {
        // Basic shipping calculation - can be enhanced based on business rules
        $baseShippingRate = 25.00; // AED 25 base shipping
        $weightBasedRate = 0; // Additional weight-based shipping

        // Calculate weight-based shipping if products have weight
        foreach ($items as $item) {
            $weight = $item->product->weight ?? 0;
            $weightBasedRate += $weight * $item->quantity * 0.5; // AED 0.5 per kg
        }

        return $baseShippingRate + $weightBasedRate;
    }

    /**
     * Calculate total discounts for the cart.
     */
    public function calculateDiscounts(ShoppingCart $cart): float
    {
        $totalDiscount = 0;

        // Item-level discounts
        $totalDiscount += $cart->items->sum('discount_amount');

        // Coupon discounts
        $appliedCoupons = $cart->applied_coupons ?? [];
        foreach ($appliedCoupons as $couponData) {
            $totalDiscount += $couponData['discount_amount'] ?? 0;
        }

        return $totalDiscount;
    }

    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(ShoppingCart $cart, string $couponCode): float
    {
        $coupon = Coupon::where('code', $couponCode)
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();

        if (!$coupon) {
            throw new \Exception('Invalid coupon code.');
        }

        // Check if coupon is already applied
        $appliedCoupons = $cart->applied_coupons ?? [];
        if (collect($appliedCoupons)->contains('code', $couponCode)) {
            throw new \Exception('Coupon is already applied.');
        }

        // Calculate discount amount
        $discountAmount = $this->calculateCouponDiscount($cart, $coupon);

        // Add coupon to applied coupons
        $appliedCoupons[] = [
            'code' => $couponCode,
            'type' => $coupon->type,
            'value' => $coupon->value,
            'discount_amount' => $discountAmount,
            'applied_at' => now()->toISOString(),
        ];

        $cart->update(['applied_coupons' => $appliedCoupons]);

        return $discountAmount;
    }

    /**
     * Calculate discount amount for a specific coupon.
     */
    protected function calculateCouponDiscount(ShoppingCart $cart, Coupon $coupon): float
    {
        $applicableAmount = $cart->subtotal;

        // If vendor-specific coupon, only apply to vendor items
        if ($coupon->vendor_id) {
            $applicableAmount = $cart->items()
                ->where('vendor_id', $coupon->vendor_id)
                ->sum('total_price');
        }

        // Check minimum order value
        if ($coupon->min_order_value && $applicableAmount < $coupon->min_order_value) {
            throw new \Exception("Minimum order value of {$coupon->min_order_value} {$cart->currency} required.");
        }

        // Calculate discount based on type
        if ($coupon->type === 'percentage') {
            $discount = ($applicableAmount * $coupon->value) / 100;
            
            // Apply maximum discount limit if set
            if ($coupon->max_discount_amount) {
                $discount = min($discount, $coupon->max_discount_amount);
            }
            
            return round($discount, 2);
        } elseif ($coupon->type === 'fixed') {
            return min($coupon->value, $applicableAmount);
        }

        return 0;
    }

    /**
     * Calculate quantity-based discounts.
     */
    public function calculateQuantityDiscounts(ShoppingCart $cart): float
    {
        $totalDiscount = 0;

        foreach ($cart->items as $item) {
            $product = $item->product;
            $quantity = $item->quantity;

            // Check for quantity-based pricing tiers
            $discountTiers = $product->quantity_discounts ?? [];
            
            foreach ($discountTiers as $tier) {
                if ($quantity >= $tier['min_quantity']) {
                    if ($tier['type'] === 'percentage') {
                        $discount = ($item->total_price * $tier['value']) / 100;
                    } else {
                        $discount = $tier['value'] * $quantity;
                    }
                    
                    $totalDiscount += $discount;
                    break; // Apply only the first matching tier
                }
            }
        }

        return $totalDiscount;
    }

    /**
     * Calculate loyalty points discount.
     */
    public function calculateLoyaltyDiscount(ShoppingCart $cart, int $pointsToRedeem): float
    {
        if (!auth()->check()) {
            return 0;
        }

        $customer = auth()->user()->customer;
        if (!$customer || $customer->loyalty_points < $pointsToRedeem) {
            throw new \Exception('Insufficient loyalty points.');
        }

        // 1 point = 0.01 AED (configurable)
        $pointValue = 0.01;
        $discountAmount = $pointsToRedeem * $pointValue;

        // Cannot exceed cart subtotal
        return min($discountAmount, $cart->subtotal);
    }

    /**
     * Get cart pricing breakdown.
     */
    public function getPricingBreakdown(ShoppingCart $cart): array
    {
        return [
            'subtotal' => $cart->subtotal,
            'tax_breakdown' => $this->getTaxBreakdown($cart),
            'shipping_breakdown' => $this->getShippingBreakdown($cart),
            'discount_breakdown' => $this->getDiscountBreakdown($cart),
            'total_amount' => $cart->total_amount,
            'savings_total' => $this->calculateTotalSavings($cart),
        ];
    }

    /**
     * Get tax breakdown by category.
     */
    protected function getTaxBreakdown(ShoppingCart $cart): array
    {
        $breakdown = [
            'vat_5_percent' => 0,
            'tax_exempt' => 0,
        ];

        foreach ($cart->items as $item) {
            $isTaxable = $item->product->is_taxable ?? true;
            $itemTotal = $item->total_price - $item->discount_amount;

            if ($isTaxable) {
                $breakdown['vat_5_percent'] += $itemTotal * 0.05;
            } else {
                $breakdown['tax_exempt'] += $itemTotal;
            }
        }

        return $breakdown;
    }

    /**
     * Get shipping breakdown by vendor.
     */
    protected function getShippingBreakdown(ShoppingCart $cart): array
    {
        $vendorGroups = $cart->items->groupBy('vendor_id');
        $breakdown = [];

        foreach ($vendorGroups as $vendorId => $items) {
            $vendor = $items->first()->vendor;
            $vendorSubtotal = $items->sum('total_price');
            $shipping = 0;

            if (!$vendor->free_shipping_threshold || $vendorSubtotal < $vendor->free_shipping_threshold) {
                $shipping = $this->calculateVendorShipping($vendor, $items, $vendorSubtotal);
            }

            $breakdown[] = [
                'vendor_id' => $vendorId,
                'vendor_name' => $vendor->vendor_display_name_en ?? $vendor->name_tl_en,
                'subtotal' => $vendorSubtotal,
                'shipping_amount' => $shipping,
                'free_shipping_threshold' => $vendor->free_shipping_threshold,
                'qualifies_for_free_shipping' => $vendor->free_shipping_threshold && $vendorSubtotal >= $vendor->free_shipping_threshold,
            ];
        }

        return $breakdown;
    }

    /**
     * Get discount breakdown.
     */
    protected function getDiscountBreakdown(ShoppingCart $cart): array
    {
        $breakdown = [
            'item_discounts' => $cart->items->sum('discount_amount'),
            'coupon_discounts' => [],
            'loyalty_discounts' => 0,
            'total_discounts' => $cart->discount_amount,
        ];

        $appliedCoupons = $cart->applied_coupons ?? [];
        foreach ($appliedCoupons as $coupon) {
            $breakdown['coupon_discounts'][] = [
                'code' => $coupon['code'],
                'type' => $coupon['type'],
                'value' => $coupon['value'],
                'discount_amount' => $coupon['discount_amount'],
            ];
        }

        return $breakdown;
    }

    /**
     * Calculate total savings.
     */
    protected function calculateTotalSavings(ShoppingCart $cart): float
    {
        $totalSavings = 0;

        // Item-level savings (original price vs current price)
        foreach ($cart->items as $item) {
            if ($item->base_price > $item->unit_price) {
                $savings = ($item->base_price - $item->unit_price) * $item->quantity;
                $totalSavings += $savings;
            }
        }

        // Add discount amounts
        $totalSavings += $cart->discount_amount;

        return $totalSavings;
    }
}
