<?php

namespace App\Services;

use App\Models\BlogCategory;
use App\Models\Category;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BlogCategoryService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = BlogCategory::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

            // Filtering
        $filters = ['status' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = [ 'title_en', 'title_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareBlogCategoryData($request);

        return BlogCategory::create($data);
    }

    private function prepareBlogCategoryData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new BlogCategory())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'blogCategory');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'blogCategory');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): BlogCategory
    {
        return BlogCategory::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $blogCategory = BlogCategory::findOrFail($id);
        $updateData = $this->prepareBlogCategoryData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $blogCategory->update($updateData);

        return $blogCategory;
    }

    public function destroy(int $id): bool
    {
        $blogCategory = BlogCategory::findOrFail($id);
        return $blogCategory->delete();
    }


    public function categoryListByActive($request)
    {
        return BlogCategory::where('status', 'active')
            ->where('parent_id', null)
            ->select(['id',  'title_en', 'title_ar','slug'])
            ->get();
    }
}
