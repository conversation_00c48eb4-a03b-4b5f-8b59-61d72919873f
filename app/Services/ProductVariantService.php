<?php

namespace App\Services;

use App\Models\ProductVariant;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductVariantService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = ProductVariant::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = $this->prepareProductVariantData($request);
            $productVariant = ProductVariant::create($data);
            if ($request->has('path')) {
                $productVariant->media()->create([
                    'product_id' => $data['product_id'],
                    'product_variant_id' => $productVariant->id,
                    'type' => 'image',
                    'path' => $request->input('path'),
                ]);
            }
            $productVariant->productVariantAttribute()->create([
                'product_attribute_id' => $request->input('attribute_id'),
                'product_attribute_value_id' => $request->input('attribute_value_id'),
            ]);

            DB::commit();
            return $productVariant;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareProductVariantData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new ProductVariant())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ProductVariant
    {
        return ProductVariant::with(['product:id,title_en,title_ar', 'productVariantAttribute:id,product_variant_id,product_attribute_id,product_attribute_value_id', 'productVariantAttribute.attribute:id,name,name_ar', 'productVariantAttribute.attributeValue:id,product_attribute_id,value,value_ar'])
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {

        DB::beginTransaction();
        try {
            $productVariant = ProductVariant::findOrFail($id);
            $updateData = $this->prepareProductVariantData($request, false);

            $updateData = array_filter($updateData, function ($value) {
                return !is_null($value);
            });

            $productVariant->update($updateData);
            if (isset($request->path)) {
                $productVariant->media()->update([
                    'path' => $request->path,
                ]);
            }
            if ($request->has(['attribute_id', 'attribute_value_id'])) {
                $productVariant->productVariantAttribute()->updateOrCreate(
                    ['product_variant_id' => $id],
                    [
                        'product_attribute_id' => $request->input('attribute_id'),
                        'product_attribute_value_id' => $request->input('attribute_value_id'),
                    ]
                );
            }

            DB::commit();
            return $productVariant;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $productVariant = ProductVariant::findOrFail($id);
        return $productVariant->delete();
    }
    
}
