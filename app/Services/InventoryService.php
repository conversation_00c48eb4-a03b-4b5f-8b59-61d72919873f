<?php

namespace App\Services;

use App\Models\Inventory;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class InventoryService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Inventory::query();
        $query->with(['product:id,title_en,title_ar', 'productVariant:id,product_id', 'productVariant.productVariantAttribute:id,product_variant_id,product_attribute_id,product_attribute_value_id', 'productVariant.productVariantAttribute.attribute:id,name,name_ar', 'productVariant.productVariantAttribute.attributeValue:id,product_attribute_id,value,value_ar']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }


    public function show(int $id): Inventory
    {
        $inventory = Inventory::with(['product:id,title_en,title_ar', 'productVariant', 'productVariant.productVariantAttribute', 'productVariant.productVariantAttribute.attribute', 'productVariant.productVariantAttribute.attributeValue'])
            ->findOrFail($id);
        return $inventory;
    }

    public function update($request, int $id)
    {
        $inventory = Inventory::findOrFail($id);
        // Use Laravel's built-in increment method for stock
        if ($request->has('stock')) {
            $inventory->increment('stock', $request->stock);
        }
        $inventory->update([
            'reserved' => $request->reserved ?? $inventory->reserved,
            'threshold' => $request->threshold ?? $inventory->threshold,
            'stock_status' => $request->stock_status ?? $inventory->stock_status,
            'location' => $request->location ?? $inventory->location,
            'is_active' => $request->is_active ?? $inventory->is_active,
            'note' => $request->note ?? $inventory->note,
        ]);
        $inventory->updateStockStatus();
        return $inventory->fresh();
    }

    // Or even better - use Laravel's increment method
    public function incrementStock($request, int $id)
    {
        $inventory = Inventory::findOrFail($id);
        $incrementAmount = $request->stock ?? 0;

        if ($incrementAmount > 0) {
            $inventory->increment('stock', $incrementAmount);
            $inventory->updateStockStatus();
        }

        return $inventory->fresh();
    }

    // For decrement (when orders are placed)
    public function decrementStock($request, int $id)
    {
        $inventory = Inventory::findOrFail($id);
        $decrementAmount = $request->stock ?? 0;

        if ($decrementAmount > 0 && $inventory->stock >= $decrementAmount) {
            $inventory->decrement('stock', $decrementAmount);
            $inventory->updateStockStatus();
        }

        return $inventory->fresh();
    }
}
