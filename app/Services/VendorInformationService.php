<?php

namespace App\Services;

use App\Models\User;
use App\Models\Vendor;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Mail\VendorPasswordMail;
use App\Models\VendorAddress;
use App\Models\VendorBank;
use App\Models\VendorContact;
use App\Models\VendorEoi;
use App\Models\Warehouse;

class VendorInformationService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Vendor::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['is_active' => '=', 'approval_status' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['spoc_name', 'spoc_email', 'name_tl_en', 'name_tl_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function storeVendor(Request $request)
    {
        DB::beginTransaction();
        try {
            // Prepare vendor main info
            $data = $this->prepareVendorInformationData($request);

            // Create or update the main Vendor by EOI ID
            $vendor = Vendor::updateOrCreate(['eoi_id' => $request->eoi_id], $data);

            // Save vendor addresses if present
            if ($request->has('vendor_address')) {
                foreach ($request->vendor_address as $address) {
                    $vendor->vendorAddress()->create($address);
                }
            }

            // Save vendor contacts if present
            if ($request->has('vendor_contacts')) {
                foreach ($request->vendor_contacts as $contact) {
                    $vendor->vendorContact()->create($contact);
                }
            }

            // Save vendor bank accounts if present
            if ($request->has('vendor_banks')) {
                foreach ($request->vendor_banks as $bank) {
                    $vendor->vendorBank()->create($bank);
                }
            }

            if (!empty($request->warehouse_types)) {
                if ($request->warehouse_types == 'own') {
                    $warehouse = Warehouse::create([
                        'name_en' => $request->warehouse_name_en,
                        'name_ar' => $request->warehouse_name_ar,
                        'code' => $request->warehouse_code,
                        'address' => $request->warehouse_address,
                        'location' => $request->warehouse_location,
                        'contact_person' => $request->warehouse_contact_person,
                        'contact_number' => $request->warehouse_contact_number,
                        'email' => $request->warehouse_email,
                        'status' => $request->warehouse_status,
                        'is_active' => 1,
                    ]);
                    $vendor->warehouse()->attach($warehouse->id, [
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                if ($request->warehouse_types == 'global') {
                    $warehouse = Warehouse::findOrFail($request->warehouse_id);
                    $vendor->warehouse()->attach($warehouse->id, [
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            // Find EOI and update its status
            $eoi = VendorEoi::where('veoi_id', $request->eoi_id)->first();
            $eoi->update(['status' => $request->status]);




            DB::commit();

            return $vendor->load(['vendorAddress', 'vendorContact', 'vendorBank']);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            // Prepare vendor main info
            $data = $this->prepareVendorInformationData($request);

            // Create or update the main Vendor by EOI ID
            $vendor = Vendor::create($data);
            // Save vendor addresses if present
            if ($request->has('vendor_address')) {
                foreach ($request->vendor_address as $address) {
                    $vendor->vendorAddress()->create($address);
                }
            }

            // Save vendor contacts if present
            if ($request->has('vendor_contacts')) {
                foreach ($request->vendor_contacts as $contact) {
                    $vendor->vendorContact()->create($contact);
                }
            }

            // Save vendor bank accounts if present
            if ($request->has('vendor_banks')) {
                foreach ($request->vendor_banks as $bank) {
                    $vendor->vendorBank()->create($bank);
                }
            }
            if (!empty($request->warehouse_types)) {
                if ($request->warehouse_types == 'own') {
                    $warehouse = Warehouse::create([
                        'name_en' => $request->warehouse_name_en,
                        'name_ar' => $request->warehouse_name_ar,
                        'code' => $request->warehouse_code,
                        'address' => $request->warehouse_address,
                        'location' => $request->warehouse_location,
                        'contact_person' => $request->warehouse_contact_person,
                        'contact_number' => $request->warehouse_contact_number,
                        'email' => $request->warehouse_email,
                        'status' => $request->warehouse_status,
                        'is_active' => 1,
                    ]);
                    $vendor->warehouse()->attach($warehouse->id, [
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
                if ($request->warehouse_types == 'global') {
                    $warehouse = Warehouse::findOrFail($request->warehouse_id);
                    $vendor->warehouse()->attach($warehouse->id, [
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            DB::commit();

            return $vendor->load(['vendorAddress', 'vendorContact', 'vendorBank']);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareVendorInformationData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Vendor())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['created_at'] = now();
        }
        $data['approval_status'] = 'Pending';
        $data['is_active'] = 0;


        return $data;
    }

    public function show(int $id): Vendor
    {
        return Vendor::with(['vendorBank', 'vendorContact', 'vendorAddress'])
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $vendorInformation = Vendor::findOrFail($id);
        $updateData = $this->prepareVendorInformationData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorInformation->update($updateData);

        return $vendorInformation;
    }

    public function destroy(int $id): bool
    {
        $vendorInformation = Vendor::findOrFail($id);
        return $vendorInformation->delete();
    }

    public function approveVendor(Request $request, int $id)
    {
        DB::beginTransaction();
        try {
            $vendorInformation = Vendor::findOrFail($id);
            $vendorInformation->update(['approval_status' => 'Approved', 'is_active' => 1, 'approved_by' => auth()->user()->id]);
            // user registration
            $randomPassword = Str::random(12); // Generates 12-character random string
            $user = User::create([
                'name' => $vendorInformation->spoc_name,
                'email' => $vendorInformation->spoc_email,
                'password' => Hash::make($randomPassword),
                'phone' => $vendorInformation->spoc_mobile ?? null,
                'avatar' => null,
                'uid' => Str::uuid(),
                'is_verified' => true,
                'status' => 'active',
                'is_active' => true,
                'vendor_id' => $vendorInformation->id,
            ]);

            // Assign vendor role
            $user->assignRole('vendor');

            // Send email with the random password
            Mail::to($user->email)->send(new VendorPasswordMail($user, $randomPassword));

            DB::commit();
            return $vendorInformation;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function activeDeactivateVendor(Request $request, int $id)
    {
        $vendorInformation = Vendor::findOrFail($id);
        $user = User::where('vendor_id', $id)->firstOrFail();

        $vendorInformation->update(['is_active' => $request->is_active]);
        $user->update(['is_active' => $request->is_active]);

        return $user;
    }

    public function profile()
    {
        $vendor = User::where('id', auth()->user()->id)
            ->with(['vendor', 'vendor.vendorAddress', 'vendor.vendorContact', 'vendor.vendorBank', 'vendor.warehouse'])
            ->firstOrFail();
        return $vendor;
    }

    public function updateProfile($request)
    {
        $vendor = Vendor::where('id', auth()->user()->vendor_id)->firstOrFail();
        $vendor->update($request->validated());
        $user = User::where('vendor_id', $vendor->id)->firstOrFail();
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone ?? null,
            'avatar' => $request->avatar ?? null,
        ]);
        return $vendor;
    }

    public function vendorListByActive($request)
    {
        $query = Vendor::where('is_active', true)
        ->select(['id', 'name_tl_en', 'name_tl_ar', 'spoc_name', 'spoc_email', 'spoc_mobile', 'approval_status'])
            ->where('approval_status', 'Approved')
            ->get();

        return $query;
    }
}
