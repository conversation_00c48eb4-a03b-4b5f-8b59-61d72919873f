<?php

namespace App\Services;

use App\Models\SupportReason;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportReasonService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportReason::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function activeList(): array
    {
        $reasons = SupportReason::where('status', 'active')
            ->select(['id', 'label', 'route_to', 'code_prefix'])
            ->orderBy('label', 'asc')
            ->get();

        $result = [];
        foreach ($reasons as $reason) {
            $result[] = [
                'id' => $reason->id,
                'label' => $reason->label,
                'route_to' => $reason->route_to,
                'code_prefix' => $reason->code_prefix,
                'auto_assignment' => [
                    'type' => $reason->route_to,
                    'description' => $this->getAutoAssignmentDescription($reason->route_to),
                    'requires_selection' => $this->requiresSelection($reason->route_to)
                ]
            ];
        }

        return $result;
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportReasonData($request);

        return SupportReason::create($data);
    }

    private function prepareSupportReasonData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new SupportReason())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'supportReason')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'supportReason');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'supportReason');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): SupportReason
    {
        return SupportReason::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $supportReason = SupportReason::findOrFail($id);
        $updateData = $this->prepareSupportReasonData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportReason->update($updateData);

        return $supportReason;
    }

    public function destroy(int $id): bool
    {
        $supportReason = SupportReason::findOrFail($id);
        return $supportReason->delete();
    }

    private function getAutoAssignmentDescription(string $routeTo): string
    {
        switch ($routeTo) {
            case 'admin':
                return 'Ticket will be automatically assigned to admin team';
            case 'vendor':
                return 'Ticket will be automatically assigned to the selected vendor';
            case 'tpl':
                return 'Ticket will be automatically assigned to the selected TPL service provider';
            default:
                return 'Ticket will be automatically assigned to admin team';
        }
    }

    private function requiresSelection(string $routeTo): bool
    {
        return in_array($routeTo, ['vendor', 'tpl']);
    }
}
