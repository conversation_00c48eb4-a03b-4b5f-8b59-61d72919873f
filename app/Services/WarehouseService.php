<?php

namespace App\Services;

use App\Models\Warehouse;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WarehouseService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Warehouse::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en', 'name_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Status filtering
        if ($request->has('status') && $request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {

        $data = $this->prepareWarehouseData($request);

        return Warehouse::create($data);
    }

    private function prepareWarehouseData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Warehouse())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'warehouse')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'warehouse');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'warehouse');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = Auth::id();
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Warehouse
    {
        return Warehouse::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $warehouse = Warehouse::findOrFail($id);
        $updateData = $this->prepareWarehouseData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $warehouse->update($updateData);

        return $warehouse;
    }

    public function destroy(int $id): bool
    {
        $warehouse = Warehouse::findOrFail($id);
        return $warehouse->delete();
    }

    public function warehouseListByActive(Request $request)
    {
        $warehouses = Warehouse::with('user:id,name,avatar')
            ->where('status', 'active')
            ->orderBy('name_en', 'asc')
            ->get();
        return $warehouses;
    }

    public function globalWarehouse(Request $request)
    {
        $warehouses = Warehouse::select('id', 'name', 'code', 'status')->where('is_global', true)->get();
        return $warehouses;
    }
}
