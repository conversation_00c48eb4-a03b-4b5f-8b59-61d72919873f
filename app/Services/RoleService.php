<?php

namespace App\Services;

use App\Models\Role;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RoleService
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = Role::query();
        $query->with('permissions');

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {

            $data = $this->prepareRoleData($request);
            $role = new Role();
            $role->name = $data['name'];
            $role->save();
            $role->syncPermissions($request->input('permissions', []));
            DB::commit();
            return $role->load('permissions');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareRoleData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new Role())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'role');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'role');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Role
    {
        return Role::with('permissions')->findOrFail($id);
    }


    public function update(Request $request, int $id)
    {
        DB::beginTransaction();
        try {
            $role = Role::findOrFail($id);
            $updateData = $this->prepareRoleData($request, false);
            $role->update($updateData);
            $role->syncPermissions($request->input('permissions', []));
            DB::commit();
            return $role;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $role = Role::findOrFail($id);
        return $role->delete();
    }
}
