<?php

namespace App\Services;

use App\Models\Brand;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class BrandService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Brand::query();

        // Select specific columns
        $query->select(['id', 'name_en', 'name_ar', 'slug', 'logo', 'country_of_origin', 'is_active','status', 'created_at', 'updated_at']);
        
              // Filtering
        $filters = ['status' => '=', 'is_active' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);
       

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en', 'name_ar', 'slug', 'country_of_origin']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function     store(Request $request)
    {
        $data = $this->prepareBrandData($request);

        return Brand::create($data);
    }

    /**
     * @throws \Exception
     */
    private function prepareBrandData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Brand())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Add created_by and created_at fields for new records
        if ($isNew) {
            if (auth()->check()) {
                $data['user_id'] = auth()->user()->id;
            }
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Brand
    {
        return Brand::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $brand = Brand::findOrFail($id);
        $updateData = $this->prepareBrandData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $brand->update($updateData);

        return $brand;
    }

    public function destroy(int $id): bool
    {
        $brand = Brand::findOrFail($id);
        return $brand->delete();
    }

    public function brandListByActive($request)
    {
        $brandQuery = Brand::where('status', 'approved')
            ->where('is_active', true)
            ->select(['id', 'name_en', 'name_ar', 'slug', 'logo', 'country_of_origin'])
            ->orderBy('name_en', 'asc')
            ->get();
        return $brandQuery;
    }



    public function getGroupedBrands($request)
    {
        $filterKey = Str::upper($request->index); // 'A', 'B', '0-9', 'Misc'

        // Use a common cache key for all brands
        $cacheKey = 'grouped_brands_data';

        // Cache for 60 minutes (adjust as needed)
        $data = Cache::remember($cacheKey, 60, function () {
            $brands = Brand::where('is_active', true)
                ->select('id', 'name_en', 'name_ar', 'slug', 'logo')
                ->orderBy('name_en')
                ->get();

            $keys = array_merge(['0-9'], range('A', 'Z'), ['Misc']);
            $groups = array_fill_keys($keys, []);

            foreach ($brands as $brand) {
                $firstChar = Str::upper(Str::substr($brand->name_en, 0, 1));

                if (ctype_digit($firstChar)) {
                    $groupKey = '0-9';
                } elseif (ctype_alpha($firstChar)) {
                    $groupKey = $firstChar;
                } else {
                    $groupKey = 'Misc';
                }

                $groups[$groupKey][] = [
                    'id'        => $brand->id,
                    'name_en'   => $brand->name_en,
                    'name_ar'   => $brand->name_ar,
                    'slug'      => $brand->slug,
                    'logo'      => $brand->logo,
                    'logo_url'  => $brand->logo_url,
                ];
            }

            // Build full index
            $index = [];
            foreach ($groups as $key => $groupBrands) {
                $exist = count($groupBrands) > 0;
                $index[] = ['key' => $key, 'exist' => $exist];
            }

            return [
                'index' => $index,
                'groups' => $groups, // all groups saved, not filtered yet
            ];
        });

        // Filter only selected group for the response
        $groupsArray = [];
        if ($filterKey && isset($data['groups'][$filterKey])) {
            $groupsArray[] = [
                'key' => $filterKey,
                'brands' => $data['groups'][$filterKey],
            ];
        } else {
            foreach ($data['groups'] as $key => $brands) {
                $groupsArray[] = [
                    'key' => $key,
                    'brands' => $brands,
                ];
            }
        }

        return [
            'index' => $data['index'],      // always full
            'brand_groups' => $groupsArray // filtered
        ];
    }

    public function getBrandBySlug($slug)
    {
        return Brand::where('slug', $slug)
            ->select('id', 'name_en', 'name_ar', 'slug', 'logo', 'country_of_origin')
            ->first();
    }
}
