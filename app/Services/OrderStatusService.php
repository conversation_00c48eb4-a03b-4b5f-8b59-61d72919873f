<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderStatusHistory;
use Illuminate\Support\Facades\DB;

class OrderStatusService
{
    /**
     * Initialize order status
     */
    public function initializeOrderStatus(Order $order): void
    {
        $this->updateOrderStatus($order, 'pending', 'Order created', [
            'initialization' => true,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(Order $order, string $newStatus, ?string $reason = null, ?array $metadata = null): void
    {
        DB::transaction(function () use ($order, $newStatus, $reason, $metadata) {
            $oldStatus = $order->fulfillment_status;

            // Update order status
            $order->update(['fulfillment_status' => $newStatus]);

            // Create status history record
            $this->createStatusHistory($order, $oldStatus, $newStatus, 'fulfillment', $reason, $metadata);

            // Handle status-specific actions
            $this->handleStatusChange($order, $oldStatus, $newStatus);
        });
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(Order $order, string $newStatus, ?string $reason = null, ?array $metadata = null): void
    {
        DB::transaction(function () use ($order, $newStatus, $reason, $metadata) {
            $oldStatus = $order->payment_status;

            // Update payment status
            $order->update([
                'payment_status' => $newStatus,
                'is_paid' => $newStatus === 'paid',
            ]);

            // Create status history record
            $this->createStatusHistory($order, $oldStatus, $newStatus, 'payment', $reason, $metadata);

            // Handle payment status-specific actions
            $this->handlePaymentStatusChange($order, $oldStatus, $newStatus);
        });
    }

    /**
     * Update item status
     */
    public function updateItemStatus(OrderItem $item, string $newStatus, ?string $reason = null, ?array $metadata = null): void
    {
        DB::transaction(function () use ($item, $newStatus, $reason, $metadata) {
            $oldStatus = $item->fulfillment_status;

            // Update item status
            $item->update(['fulfillment_status' => $newStatus]);

            // Create status history record for the order
            $this->createStatusHistory(
                $item->order,
                $oldStatus,
                $newStatus,
                'item_fulfillment',
                $reason,
                array_merge($metadata ?? [], [
                    'item_id' => $item->id,
                    'product_title' => $item->product_title,
                    'sku' => $item->sku,
                ])
            );

            // Check if all items have the same status and update order accordingly
            $this->updateOrderStatusBasedOnItems($item->order);
        });
    }

    /**
     * Bulk update order status
     */
    public function bulkUpdateOrderStatus(array $orderIds, string $newStatus, ?string $reason = null): array
    {
        $results = [];

        foreach ($orderIds as $orderId) {
            try {
                $order = Order::findOrFail($orderId);
                $this->updateOrderStatus($order, $newStatus, $reason);
                $results[$orderId] = ['success' => true];
            } catch (\Exception $e) {
                $results[$orderId] = ['success' => false, 'error' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * Get status transition options
     */
    public function getAvailableStatusTransitions(Order $order): array
    {
        $currentStatus = $order->fulfillment_status;

        $transitions = [
            'pending' => [
                ['status' => 'confirmed', 'label' => 'Confirm Order', 'color' => 'success'],
                ['status' => 'cancelled', 'label' => 'Cancel Order', 'color' => 'danger'],
            ],
            'confirmed' => [
                ['status' => 'processing', 'label' => 'Start Processing', 'color' => 'primary'],
                ['status' => 'cancelled', 'label' => 'Cancel Order', 'color' => 'danger'],
            ],
            'processing' => [
                ['status' => 'shipped', 'label' => 'Mark as Shipped', 'color' => 'info'],
                ['status' => 'cancelled', 'label' => 'Cancel Order', 'color' => 'danger'],
            ],
            'shipped' => [
                ['status' => 'delivered', 'label' => 'Mark as Delivered', 'color' => 'success'],
                ['status' => 'returned', 'label' => 'Mark as Returned', 'color' => 'warning'],
            ],
            'delivered' => [
                ['status' => 'returned', 'label' => 'Process Return', 'color' => 'warning'],
            ],
            'cancelled' => [],
            'returned' => [],
        ];

        return $transitions[$currentStatus] ?? [];
    }

    /**
     * Get payment status transition options
     */
    public function getAvailablePaymentTransitions(Order $order): array
    {
        $currentStatus = $order->payment_status;

        $transitions = [
            'pending' => [
                ['status' => 'paid', 'label' => 'Mark as Paid', 'color' => 'success'],
                ['status' => 'failed', 'label' => 'Mark as Failed', 'color' => 'danger'],
            ],
            'paid' => [
                ['status' => 'refunded', 'label' => 'Process Refund', 'color' => 'warning'],
            ],
            'failed' => [
                ['status' => 'paid', 'label' => 'Mark as Paid', 'color' => 'success'],
            ],
            'refunded' => [],
        ];

        return $transitions[$currentStatus] ?? [];
    }

    /**
     * Get order status history
     */
    public function getOrderStatusHistory(Order $order, ?string $statusType = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = $order->statusHistories()->with('user')->orderBy('changed_at', 'desc');

        if ($statusType) {
            $query->where('status_type', $statusType);
        }

        return $query->get();
    }

    /**
     * Protected helper methods
     */
    protected function createStatusHistory(Order $order, ?string $fromStatus, string $toStatus, string $statusType, ?string $reason, ?array $metadata): OrderStatusHistory
    {
        return $order->statusHistories()->create([
            'from_status' => $fromStatus,
            'to_status' => $toStatus,
            'status_type' => $statusType,
            'reason' => $reason,
            'metadata' => $metadata,
            'user_id' => auth()->id(),
            'changed_by_type' => $this->getChangedByType(),
            'changed_at' => now(),
        ]);
    }

    protected function getChangedByType(): string
    {
        if (!auth()->check()) {
            return 'system';
        }

        $user = auth()->user();

        if ($user->hasRole('admin')) {
            return 'admin';
        }

        if ($user->hasRole('vendor')) {
            return 'vendor';
        }

        if ($user->hasRole('customer')) {
            return 'customer';
        }

        return 'user';
    }

    protected function handleStatusChange(Order $order, string $oldStatus, string $newStatus): void
    {
        switch ($newStatus) {
            case 'confirmed':
                $this->handleOrderConfirmed($order);
                break;
            case 'processing':
                $this->handleOrderProcessing($order);
                break;
            case 'shipped':
                $this->handleOrderShipped($order);
                break;
            case 'delivered':
                $this->handleOrderDelivered($order);
                break;
            case 'cancelled':
                $this->handleOrderCancelled($order);
                break;
            case 'returned':
                $this->handleOrderReturned($order);
                break;
        }
    }

    protected function handlePaymentStatusChange(Order $order, string $oldStatus, string $newStatus): void
    {
        switch ($newStatus) {
            case 'paid':
                $this->handlePaymentConfirmed($order);
                break;
            case 'failed':
                $this->handlePaymentFailed($order);
                break;
            case 'refunded':
                $this->handlePaymentRefunded($order);
                break;
        }
    }

    protected function updateOrderStatusBasedOnItems(Order $order): void
    {
        $itemStatuses = $order->items()->pluck('fulfillment_status')->unique();

        // If all items have the same status, update order status
        if ($itemStatuses->count() === 1) {
            $itemStatus = $itemStatuses->first();
            
            if ($order->fulfillment_status !== $itemStatus) {
                $this->updateOrderStatus($order, $itemStatus, 'Updated based on item statuses');
            }
        }
    }

    // Status-specific handlers (can be extended with events/notifications)
    protected function handleOrderConfirmed(Order $order): void
    {
        // Handle order confirmation logic
        // e.g., send confirmation email, reserve inventory
    }

    protected function handleOrderProcessing(Order $order): void
    {
        // Handle order processing logic
        // e.g., notify warehouse, update inventory
    }

    protected function handleOrderShipped(Order $order): void
    {
        // Handle order shipped logic
        // e.g., send tracking information, update delivery estimates
    }

    protected function handleOrderDelivered(Order $order): void
    {
        // Handle order delivered logic
        // e.g., update customer analytics, send feedback request
        $this->updateCustomerAnalytics($order);
    }

    protected function handleOrderCancelled(Order $order): void
    {
        // Handle order cancellation logic
        // e.g., restore inventory, process refunds
    }

    protected function handleOrderReturned(Order $order): void
    {
        // Handle order return logic
        // e.g., process return, update inventory
    }

    protected function handlePaymentConfirmed(Order $order): void
    {
        // Handle payment confirmation logic
        // e.g., send payment confirmation, update accounting
    }

    protected function handlePaymentFailed(Order $order): void
    {
        // Handle payment failure logic
        // e.g., send failure notification, retry payment
    }

    protected function handlePaymentRefunded(Order $order): void
    {
        // Handle payment refund logic
        // e.g., process refund, update accounting
    }

    protected function updateCustomerAnalytics(Order $order): void
    {
        $customer = $order->user->customer;
        
        if ($customer) {
            $customer->updateSpendingStats();
            $customer->evaluateTierEligibility();
        }
    }
}
