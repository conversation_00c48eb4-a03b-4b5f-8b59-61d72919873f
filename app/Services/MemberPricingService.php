<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Customer;
use App\Models\CustomerPricingTier;

class MemberPricingService
{
    /**
     * Calculate member-specific pricing for a product
     */
    public function calculateMemberPrice(Product $product, Customer $customer, ?int $variantId = null): ?array
    {
        if (!$customer->hasActiveTier()) {
            return null;
        }

        $tier = $customer->pricingTier;
        $variant = $variantId ? ProductVariant::find($variantId) : null;

        // Get base price
        $basePrice = $variant?->price ?? $product->regular_price;

        // Get tier-specific pricing
        $tierPrice = $this->getTierSpecificPrice($product, $tier, $variant);

        // Calculate tier discount if no specific price is set
        $discountPrice = null;
        if ($tier->discount_percentage && !$tierPrice) {
            $discountPrice = $basePrice * (1 - ($tier->discount_percentage / 100));
        }

        // Determine the best price for the customer
        $finalPrice = $this->determineBestMemberPrice($basePrice, $tierPrice, $discountPrice);

        if ($finalPrice === $basePrice) {
            return null; // No member benefit
        }

        return [
            'base_price' => $basePrice,
            'tier_specific_price' => $tierPrice,
            'tier_discount_price' => $discountPrice,
            'final_price' => $finalPrice,
            'savings_amount' => $basePrice - $finalPrice,
            'savings_percentage' => (($basePrice - $finalPrice) / $basePrice) * 100,
            'applied_type' => $this->getAppliedPriceType($tierPrice, $discountPrice, $finalPrice),
            'tier_code' => $tier->code,
            'tier_name' => $tier->name,
        ];
    }

    /**
     * Get tier-specific price for product
     */
    protected function getTierSpecificPrice(Product $product, CustomerPricingTier $tier, ?ProductVariant $variant = null): ?float
    {
        // Check variant-specific tier pricing first
        if ($variant && isset($variant->pricing_tiers[$tier->code])) {
            return $variant->pricing_tiers[$tier->code];
        }

        // Check product-specific tier pricing
        if (isset($product->pricing_tiers[$tier->code])) {
            return $product->pricing_tiers[$tier->code];
        }

        // Check predefined tier prices on product
        switch ($tier->code) {
            case 'VIP':
                return $product->vip_price;
            case 'WHOLESALE':
                return $product->wholesale_price;
            case 'MEMBER':
                return $product->member_price;
            default:
                return null;
        }
    }

    /**
     * Determine the best member price
     */
    protected function determineBestMemberPrice(float $basePrice, ?float $tierPrice, ?float $discountPrice): float
    {
        $prices = array_filter([$basePrice, $tierPrice, $discountPrice]);
        return min($prices);
    }

    /**
     * Get the type of pricing that was applied
     */
    protected function getAppliedPriceType(?float $tierPrice, ?float $discountPrice, float $finalPrice): string
    {
        if ($tierPrice && $finalPrice == $tierPrice) {
            return 'tier_specific';
        }

        if ($discountPrice && $finalPrice == $discountPrice) {
            return 'tier_discount';
        }

        return 'base';
    }

    /**
     * Calculate member pricing for multiple products
     */
    public function calculateBulkMemberPricing(array $products, Customer $customer): array
    {
        $results = [];

        foreach ($products as $productData) {
            $product = $productData['product'];
            $quantity = $productData['quantity'] ?? 1;
            $variantId = $productData['variant_id'] ?? null;

            $memberPricing = $this->calculateMemberPrice($product, $customer, $variantId);

            $results[] = [
                'product_id' => $product->id,
                'variant_id' => $variantId,
                'quantity' => $quantity,
                'member_pricing' => $memberPricing,
                'total_savings' => $memberPricing ? $memberPricing['savings_amount'] * $quantity : 0,
            ];
        }

        return [
            'products' => $results,
            'total_savings' => array_sum(array_column($results, 'total_savings')),
            'customer_tier' => $customer->pricingTier->code,
        ];
    }

    /**
     * Get member pricing preview for customer
     */
    public function getMemberPricingPreview(Customer $customer, array $productIds): array
    {
        $products = Product::whereIn('id', $productIds)->get();
        $preview = [];

        foreach ($products as $product) {
            $memberPricing = $this->calculateMemberPrice($product, $customer);
            
            $preview[] = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'regular_price' => $product->regular_price,
                'member_pricing' => $memberPricing,
                'has_member_benefit' => $memberPricing !== null,
            ];
        }

        return [
            'customer_tier' => $customer->pricingTier?->code,
            'products' => $preview,
            'total_potential_savings' => array_sum(array_column($preview, 'member_pricing.savings_amount')),
        ];
    }

    /**
     * Validate member pricing configuration
     */
    public function validateMemberPricingConfiguration(Product $product): array
    {
        $issues = [];

        // Check if member pricing is enabled but no prices are set
        if ($product->enable_member_pricing) {
            $hasMemberPricing = $product->member_price || 
                               $product->wholesale_price || 
                               $product->vip_price || 
                               !empty($product->pricing_tiers);

            if (!$hasMemberPricing) {
                $issues[] = 'Member pricing is enabled but no member prices are configured';
            }
        }

        // Validate pricing tiers configuration
        if ($product->pricing_tiers) {
            foreach ($product->pricing_tiers as $tierCode => $price) {
                if ($price >= $product->regular_price) {
                    $issues[] = "Tier price for '{$tierCode}' should be less than regular price";
                }

                // Check if tier exists
                $tierExists = CustomerPricingTier::where('code', $tierCode)->where('is_active', true)->exists();
                if (!$tierExists) {
                    $issues[] = "Pricing tier '{$tierCode}' does not exist or is inactive";
                }
            }
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues,
        ];
    }

    /**
     * Get member pricing statistics
     */
    public function getMemberPricingStatistics(CustomerPricingTier $tier): array
    {
        $products = Product::where('enable_member_pricing', true)->get();
        $stats = [
            'total_products' => $products->count(),
            'products_with_tier_pricing' => 0,
            'products_with_discount_only' => 0,
            'average_savings_percentage' => 0,
            'total_potential_savings' => 0,
        ];

        $totalSavingsPercentage = 0;
        $totalPotentialSavings = 0;

        foreach ($products as $product) {
            $tierPrice = $this->getTierSpecificPrice($product, $tier);
            $discountPrice = null;

            if ($tier->discount_percentage && !$tierPrice) {
                $discountPrice = $product->regular_price * (1 - ($tier->discount_percentage / 100));
            }

            if ($tierPrice) {
                $stats['products_with_tier_pricing']++;
                $savings = $product->regular_price - $tierPrice;
                $savingsPercentage = ($savings / $product->regular_price) * 100;
            } elseif ($discountPrice) {
                $stats['products_with_discount_only']++;
                $savings = $product->regular_price - $discountPrice;
                $savingsPercentage = ($savings / $product->regular_price) * 100;
            } else {
                continue;
            }

            $totalSavingsPercentage += $savingsPercentage;
            $totalPotentialSavings += $savings;
        }

        $productsWithPricing = $stats['products_with_tier_pricing'] + $stats['products_with_discount_only'];
        
        if ($productsWithPricing > 0) {
            $stats['average_savings_percentage'] = $totalSavingsPercentage / $productsWithPricing;
        }

        $stats['total_potential_savings'] = $totalPotentialSavings;

        return $stats;
    }

    /**
     * Update customer tier based on spending
     */
    public function evaluateAndUpdateCustomerTier(Customer $customer): ?CustomerPricingTier
    {
        // Update customer spending statistics
        $customer->updateSpendingStats();

        // Find the best tier for customer
        $bestTier = CustomerPricingTier::findBestTierForCustomer($customer);

        if ($bestTier && $customer->pricing_tier_id !== $bestTier->id) {
            $customer->update([
                'pricing_tier_id' => $bestTier->id,
                'tier_assigned_at' => now(),
                'last_tier_evaluation_at' => now(),
            ]);

            return $bestTier;
        }

        // Update evaluation timestamp even if tier didn't change
        $customer->update(['last_tier_evaluation_at' => now()]);

        return $customer->pricingTier;
    }

    /**
     * Get tier upgrade recommendations for customer
     */
    public function getTierUpgradeRecommendations(Customer $customer): array
    {
        $currentTier = $customer->pricingTier;
        $nextTier = CustomerPricingTier::where('is_active', true)
            ->where('priority', '>', $currentTier?->priority ?? 0)
            ->orderBy('priority', 'asc')
            ->first();

        if (!$nextTier) {
            return [
                'has_next_tier' => false,
                'message' => 'You are already at the highest tier!',
            ];
        }

        $requirements = [];
        $progress = [];

        if ($nextTier->minimum_annual_spend) {
            $remaining = max(0, $nextTier->minimum_annual_spend - $customer->total_annual_spend);
            $requirements[] = "Spend AED " . number_format($remaining, 2) . " more this year";
            $progress['spend_progress'] = ($customer->total_annual_spend / $nextTier->minimum_annual_spend) * 100;
        }

        if ($nextTier->minimum_orders_count) {
            $remaining = max(0, $nextTier->minimum_orders_count - $customer->total_orders_count);
            $requirements[] = "Place {$remaining} more orders";
            $progress['orders_progress'] = ($customer->total_orders_count / $nextTier->minimum_orders_count) * 100;
        }

        return [
            'has_next_tier' => true,
            'next_tier' => $nextTier,
            'requirements' => $requirements,
            'progress' => $progress,
            'estimated_savings' => $this->calculatePotentialSavings($customer, $nextTier),
        ];
    }

    /**
     * Calculate potential savings for customer with a specific tier
     */
    protected function calculatePotentialSavings(Customer $customer, CustomerPricingTier $tier): float
    {
        // This is a simplified calculation - in reality, you'd analyze customer's purchase history
        $averageMonthlySpend = $customer->total_annual_spend / 12;
        $potentialSavings = 0;

        if ($tier->discount_percentage) {
            $potentialSavings = ($averageMonthlySpend * $tier->discount_percentage) / 100;
        }

        return $potentialSavings;
    }
}
