<?php

namespace App\Services;

use App\Models\User;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = User::query();
        $query->with([
            'roles' => function ($query) {
                $query->select(['id', 'name']);
            }
        ]);

        // Select specific columns
        $query->select(['*']);

        // Filtering
        $filters = ['is_active' => '=', 'status' => '=', 'is_verified' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        if ($request->filled('role_id')) {
            $roleId = $request->input('role_id');

            $query->whereHas('roles', function ($q) use ($roleId) {
                $q->where('roles.id', $roleId);
            });
        }

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name', 'email']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store($request)
    {
        DB::beginTransaction();
        try {
            $data = $this->prepareUserData($request);
            $user = User::create($data);
            $user->assignRole($request->input('roles', []));
            $user->syncPermissions($request->input('permissions', []));
            DB::commit();
            return $user->load('roles');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareUserData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new User())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        $data['avatar'] = $this->s3FileUpload($request, 'avatar', 'avatar')['path'] ?? null;
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'user');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
            $data['is_verified'] = true;
        }
        if (isset($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        }
        return $data;
    }


    public function show(int $id)
    {

        $user = User::with(['roles', 'permissions'])->findOrFail($id);
        $userData = $user->toArray();
        $userData['roles'] = $user->roles->pluck('name');
        $userData['permissions'] = $user->getAllPermissions()->pluck('name');
        return $userData;
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail($id);
            $updateData = $this->prepareUserData($request, false);
            $updateData = array_filter($updateData, function ($value) {
                return !is_null($value);
            });
            $user->update($updateData);
            if ($request->has('roles')) {
                $user->syncRoles($request->input('roles', []));
            }
            if ($request->has('permissions')) {
                $user->syncPermissions($request->input('permissions', []));
            }

            DB::commit();
            return $user;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $user = User::findOrFail($id);
        return $user->delete();
    }
}
