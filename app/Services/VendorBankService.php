<?php

namespace App\Services;

use App\Models\VendorBank;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorBankService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorBank::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['bank_name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorBankData($request);

        return VendorBank::create($data);
    }

    private function prepareVendorBankData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorBank())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorBank')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorBank');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorBank');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorBank
    {
        return VendorBank::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorBank = VendorBank::findOrFail($id);
        $updateData = $this->prepareVendorBankData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorBank->update($updateData);

        return $vendorBank;
    }

    public function destroy(int $id): bool
    {
        $vendorBank = VendorBank::findOrFail($id);
        return $vendorBank->delete();
    }
}
