<?php

namespace App\Services;

use App\Models\Fulfilment;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class FulfilmentService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Fulfilment::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en', 'name_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function activeList(): array
    {
        return Fulfilment::where('is_active', true)
            ->select(['id', 'name_en', 'name_ar'])
            ->orderBy('name_en', 'asc')
            ->get()
            ->toArray();
    }

    public function store(Request $request)
    {
        $data = $this->prepareFulfilmentData($request);

        return Fulfilment::create($data);
    }

    private function prepareFulfilmentData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Fulfilment())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Add created_at field for new records
        if ($isNew) {
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Fulfilment
    {
        return Fulfilment::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $fulfilment = Fulfilment::findOrFail($id);
        $updateData = $this->prepareFulfilmentData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $fulfilment->update($updateData);

        return $fulfilment;
    }

    public function destroy(int $id): bool
    {
        $fulfilment = Fulfilment::findOrFail($id);
        return $fulfilment->delete();
    }
}
