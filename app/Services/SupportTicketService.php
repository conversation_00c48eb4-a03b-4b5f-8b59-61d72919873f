<?php

namespace App\Services;

use App\Models\SupportReason;
use App\Models\SupportTicket;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SupportTicketService
{
    use HelperTrait;

    public function index($request): array
    {
        $searchKeys = ['subject', 'message', 'code'];
        $filters = [
            'status' => '=',
            'record_status' => '=',
            'priority' => '=',
            'user_id' => '=',
            'vendor_id' => '=',
            'tpl_id' => '=',
            'order_id' => '=',
            'reason_id' => '=',
            'category_id' => '=',
            'topic_id' => '='
        ];
        // Base query with relations
        $query = SupportTicket::with(['category', 'topic', 'reason', 'user', 'assignedTo']);

        $this->applySorting($query, $request);
        $this->applyFilters($query, $request, $filters);
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Clone for status count
        $countQuery = (clone $query)->getQuery(); // Get raw query builder

        // Remove select and order to avoid GROUP BY error in PostgreSQL
        $countQuery->orders = null;
        $countQuery->limit  = null;
        $countQuery->offset = null;

        // Now safely do the count
        $rawCounts = DB::table(DB::raw("({$countQuery->toSql()}) as sub"))
            ->mergeBindings($countQuery)
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $allStatuses = ['open', 'in_progress', 'resolved', 'closed'];
        $statusCounts = collect($allStatuses)
            ->mapWithKeys(fn($status) => [$status => $rawCounts[$status] ?? 0])
            ->toArray();

        $data = $this->paginateOrGet($query, $request);

        return [
            'data' => $data,
            'status_counts' => $statusCounts,
        ];
    }


    public function store(Request $request)
    {
        $data = $this->prepareSupportTicketData($request);

        $ticket = SupportTicket::create($data);

        // Send admin CC notifications if needed
        $this->sendAdminCCNotification($ticket);

        return $ticket->load(['reason', 'category', 'topic', 'vendor', 'tpl', 'assignedTo']);
    }

    private function prepareSupportTicketData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new SupportTicket())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->id();
            $data['created_at'] = now();

            // Auto-assign resolver based on reason
            $this->autoAssignTicket($data);
        }

        return $data;
    }

    private function autoAssignTicket(array &$data): void
    {
        $reason = null;
        if (isset($data['reason_id'])) {
            $reason = SupportReason::find($data['reason_id']);
        }

        // Auto-assign based on reason routing
        if ($reason) {
            switch ($reason->route_to) {
                case 'vendor':
                    if (isset($data['vendor_id'])) {
                        $vendor = \App\Models\Vendor::find($data['vendor_id']);
                        $data['assigned_to'] = $vendor?->user_id;
                    }
                    break;
                case 'tpl':
                    if (isset($data['tpl_id'])) {
                        $tpl = \App\Models\Tpl::find($data['tpl_id']);
                        $data['assigned_to'] = $tpl?->user_id;
                    }
                    break;
                case 'admin':
                default:
                    $data['assigned_to'] = $this->getAdminUserId();
                    break;
            }
        } else {
            // Default assignment logic if no reason specified
            if (isset($data['vendor_id'])) {
                $vendor = \App\Models\Vendor::find($data['vendor_id']);
                $data['assigned_to'] = $vendor?->user_id;
            } elseif (isset($data['tpl_id'])) {
                $tpl = \App\Models\Tpl::find($data['tpl_id']);
                $data['assigned_to'] = $tpl?->user_id;
            } else {
                $data['assigned_to'] = $this->getAdminUserId();
            }
        }
    }

    private function getAdminUserId(): ?int
    {
        return \App\Models\User::role('admin')->first()?->id;
    }

    private function sendAdminCCNotification(SupportTicket $ticket): void
    {
        // Send admin CC only if ticket is directed to vendor or TPL
        $reason = $ticket->reason;

        if ($reason && in_array($reason->route_to, ['vendor', 'tpl'])) {
            // Get all admin users
            $adminUsers = \App\Models\User::role('admin')->get();

            foreach ($adminUsers as $admin) {
                // Create a notification record or send email
                // This could be implemented with Laravel notifications
                \App\Models\Notification::create([
                    'type' => 'support_ticket_cc',
                    'notifiable_type' => 'App\Models\User',
                    'notifiable_id' => $admin->id,
                    'data' => [
                        'ticket_id' => $ticket->id,
                        'ticket_code' => $ticket->code,
                        'subject' => $ticket->subject,
                        'assigned_to' => $ticket->assigned_to,
                        'route_to' => $reason->route_to
                    ]
                ]);
            }
        }
    }

    public function show(int $id): SupportTicket
    {
        return SupportTicket::with(['category', 'topic', 'reason', 'vendor', 'tpl', 'assignedTo', 'user'])
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $supportTicket = SupportTicket::findOrFail($id);
        $updateData = $this->prepareSupportTicketData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportTicket->update($updateData);

        return $supportTicket;
    }

    public function destroy(int $id): bool
    {
        $supportTicket = SupportTicket::findOrFail($id);
        return $supportTicket->delete();
    }
}
