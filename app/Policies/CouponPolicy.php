<?php

namespace App\Policies;

use App\Models\Coupon;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CouponPolicy
{
    /**
     * Determine whether the user can view any coupons.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole(['admin', 'vendor']);
    }

    /**
     * Determine whether the user can view the coupon.
     */
    public function view(User $user, Coupon $coupon): bool
    {
        // Admin can view all coupons
        if ($user->hasRole('admin')) {
            return true;
        }

        // Vendor can only view their own coupons or platform-wide coupons
        if ($user->hasRole('vendor')) {
            return $coupon->vendor_id === $this->getUserVendorId($user) || $coupon->vendor_id === null;
        }

        return false;
    }

    /**
     * Determine whether the user can create coupons.
     */
    public function create(User $user): bool
    {
        return $user->hasRole(['admin', 'vendor']);
    }

    /**
     * Determine whether the user can update the coupon.
     */
    public function update(User $user, Coupon $coupon): bool
    {
        // Admin can update all coupons
        if ($user->hasRole('admin')) {
            return true;
        }

        // Vendor can only update their own coupons
        if ($user->hasRole('vendor')) {
            return $coupon->vendor_id === $this->getUserVendorId($user);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the coupon.
     */
    public function delete(User $user, Coupon $coupon): bool
    {
        // Admin can delete all coupons
        if ($user->hasRole('admin')) {
            return true;
        }

        // Vendor can only delete their own coupons
        if ($user->hasRole('vendor')) {
            return $coupon->vendor_id === $this->getUserVendorId($user);
        }

        return false;
    }

    /**
     * Determine whether the user can create platform-wide coupons.
     */
    public function createPlatformWide(User $user): bool
    {
        return $user->hasRole('admin');
    }

    /**
     * Determine whether the user can view vendor-specific coupons.
     */
    public function viewVendorCoupons(User $user, int $vendorId): bool
    {
        // Admin can view all vendor coupons
        if ($user->hasRole('admin')) {
            return true;
        }

        // Vendor can only view their own coupons
        if ($user->hasRole('vendor')) {
            return $vendorId === $this->getUserVendorId($user);
        }

        return false;
    }

    /**
     * Determine whether the user can view coupon statistics.
     */
    public function viewStats(User $user, ?int $vendorId = null): bool
    {
        // Admin can view all statistics
        if ($user->hasRole('admin')) {
            return true;
        }

        // Vendor can only view their own statistics
        if ($user->hasRole('vendor')) {
            return $vendorId === null || $vendorId === $this->getUserVendorId($user);
        }

        return false;
    }

    /**
     * Get the vendor ID associated with the user.
     * This is a placeholder - you'll need to implement the actual logic
     * based on how vendors are associated with users in your system.
     */
    private function getUserVendorId(User $user): ?int
    {
        // TODO: Implement the actual logic to get vendor ID from user
        // This could be through a direct relationship, a pivot table, or other means
        // For now, returning null as placeholder
        
        // Example implementations:
        // return $user->vendor_id; // if user has direct vendor_id field
        // return $user->vendor?->id; // if user has vendor relationship
        // return $user->vendorProfile?->vendor_id; // if through a profile relationship
        
        return null;
    }
}
