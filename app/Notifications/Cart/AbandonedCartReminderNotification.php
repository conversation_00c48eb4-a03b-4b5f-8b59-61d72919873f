<?php

namespace App\Notifications\Cart;

use App\Models\AbandonedCart;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AbandonedCartReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected AbandonedCart $abandonedCart;

    /**
     * Create a new notification instance.
     */
    public function __construct(AbandonedCart $abandonedCart)
    {
        $this->abandonedCart = $abandonedCart;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $cart = $this->abandonedCart->cart;
        $recoveryUrl = $this->getRecoveryUrl();

        return (new MailMessage)
            ->subject('You left something in your cart!')
            ->greeting("Hi {$notifiable->name},")
            ->line('You have items waiting in your shopping cart.')
            ->line("Cart value: {$cart->currency} {$cart->total_amount}")
            ->line("Items: {$cart->items_count}")
            ->action('Complete Your Purchase', $recoveryUrl)
            ->line('This link will expire in 7 days.')
            ->line('Thank you for shopping with us!');
    }

    /**
     * Get the recovery URL for the abandoned cart.
     */
    protected function getRecoveryUrl(): string
    {
        return url("/cart/recover/{$this->abandonedCart->recovery_token}");
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'cart_id' => $this->abandonedCart->cart_id,
            'cart_value' => $this->abandonedCart->cart_value,
            'items_count' => $this->abandonedCart->items_count,
            'recovery_token' => $this->abandonedCart->recovery_token,
        ];
    }
}
