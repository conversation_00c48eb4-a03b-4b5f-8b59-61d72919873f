<?php

namespace App\Jobs\Cart;

use App\Models\AbandonedCart;
use App\Models\ShoppingCart;
use App\Notifications\Cart\AbandonedCartReminderNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessAbandonedCartsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing abandoned carts job started');

        $this->identifyAbandonedCarts();
        $this->sendReminderEmails();
        $this->cleanupExpiredCarts();

        Log::info('Processing abandoned carts job completed');
    }

    /**
     * Identify and mark carts as abandoned.
     */
    protected function identifyAbandonedCarts(): void
    {
        $abandonmentThreshold = now()->subHours(24); // 24 hours of inactivity

        $abandonedCarts = ShoppingCart::where('status', 'active')
            ->where('last_activity_at', '<', $abandonmentThreshold)
            ->whereHas('items') // Only carts with items
            ->whereNotNull('user_id') // Only authenticated users
            ->get();

        foreach ($abandonedCarts as $cart) {
            $this->markCartAsAbandoned($cart);
        }

        Log::info("Identified {$abandonedCarts->count()} abandoned carts");
    }

    /**
     * Mark a cart as abandoned and create abandoned cart record.
     */
    protected function markCartAsAbandoned(ShoppingCart $cart): void
    {
        $cart->markAsAbandoned();

        // Create or update abandoned cart record
        AbandonedCart::updateOrCreate(
            [
                'cart_id' => $cart->id,
                'customer_id' => $cart->user_id,
            ],
            [
                'email' => $cart->user->email,
                'cart_value' => $cart->total_amount,
                'items_count' => $cart->items_count,
                'abandoned_at' => $cart->last_activity_at,
                'recovery_token' => $this->generateRecoveryToken(),
                'recovery_expires_at' => now()->addDays(7),
                'recovery_status' => 'pending',
            ]
        );

        Log::info("Cart {$cart->id} marked as abandoned for user {$cart->user_id}");
    }

    /**
     * Send reminder emails for abandoned carts.
     */
    protected function sendReminderEmails(): void
    {
        $pendingReminders = AbandonedCart::where('recovery_status', 'pending')
            ->where('reminder_sent_at', null)
            ->where('recovery_expires_at', '>', now())
            ->with(['customer', 'cart.items.product'])
            ->get();

        foreach ($pendingReminders as $abandonedCart) {
            $this->sendReminderEmail($abandonedCart);
        }

        Log::info("Sent {$pendingReminders->count()} abandoned cart reminder emails");
    }

    /**
     * Send reminder email for a specific abandoned cart.
     */
    protected function sendReminderEmail(AbandonedCart $abandonedCart): void
    {
        try {
            $customer = $abandonedCart->customer;
            
            if ($customer && $customer->email) {
                $customer->notify(new AbandonedCartReminderNotification($abandonedCart));
                
                $abandonedCart->update([
                    'reminder_sent_at' => now(),
                    'recovery_status' => 'sent',
                    'recovery_attempts' => $abandonedCart->recovery_attempts + 1,
                ]);

                Log::info("Sent abandoned cart reminder to {$customer->email} for cart {$abandonedCart->cart_id}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send abandoned cart reminder for cart {$abandonedCart->cart_id}: {$e->getMessage()}");
            
            $abandonedCart->increment('recovery_attempts');
        }
    }

    /**
     * Clean up expired carts and sessions.
     */
    protected function cleanupExpiredCarts(): void
    {
        $expiredThreshold = now()->subDays(30); // 30 days old

        // Mark very old abandoned carts as expired
        $expiredAbandonedCarts = AbandonedCart::where('recovery_expires_at', '<', now())
            ->where('recovery_status', '!=', 'expired')
            ->update(['recovery_status' => 'expired']);

        // Delete very old guest carts
        $deletedGuestCarts = ShoppingCart::where('status', 'active')
            ->whereNull('user_id')
            ->where('created_at', '<', $expiredThreshold)
            ->delete();

        // Clean up expired cart sessions
        $deletedSessions = \App\Models\CartSession::where('expires_at', '<', now())
            ->where('is_migrated', false)
            ->delete();

        Log::info("Cleanup completed: {$expiredAbandonedCarts} abandoned carts expired, {$deletedGuestCarts} guest carts deleted, {$deletedSessions} sessions deleted");
    }

    /**
     * Generate a unique recovery token.
     */
    protected function generateRecoveryToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['cart', 'abandoned', 'cleanup'];
    }
}
