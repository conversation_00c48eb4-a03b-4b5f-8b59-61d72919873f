<?php


use Illuminate\Support\Facades\Route;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\VendorEoiController;
use App\Http\Controllers\VendorStaffController;
use App\Http\Controllers\Vendor\OrderController;
use App\Http\Controllers\WarehouseController;

Route::middleware('auth:api')->group(function () {
    Route::get('profile', [VendorController::class, 'profile']);
    Route::post('update-profile', [VendorController::class, 'updateProfile']);
    Route::apiResource('staff', VendorStaffController::class);

    // Vendor order management routes
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('/dashboard', [OrderController::class, 'dashboard']);
        Route::get('/analytics', [OrderController::class, 'analytics']);
        Route::get('/pending-actions', [OrderController::class, 'pendingActions']);
        Route::get('/{uuid}', [OrderController::class, 'show']);
        Route::patch('/{uuid}/status', [OrderController::class, 'updateStatus']);
    });
});

Route::middleware('throttle:public-submissions')->group(function () {
    Route::post('vendor-eoi/submit', [VendorEoiController::class, 'store']);
    Route::get('vendor-eoi-details/{eoi_id}', [VendorEoiController::class, 'eoi_details']);
    Route::post('vendor-registration', [VendorController::class, 'storeVendor']);
    Route::get('global-warehouse', [WarehouseController::class, 'globalWarehouse']);
});
