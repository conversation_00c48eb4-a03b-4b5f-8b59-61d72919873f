<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\ProductClass;
use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        // Create dependencies if they don't exist
        $categories = Category::where('type', 'main')->get();
        if ($categories->isEmpty()) {
            $category = Category::factory()->create(['type' => 'main']);
        } else {
            $category = $this->faker->randomElement($categories);
        }

        $subCategories = Category::where('parent_id', $category->id)->get();
        if ($subCategories->isEmpty()) {
            // Create a subcategory if none exists
            $subCategory = Category::factory()->create([
                'type' => 'sub',
                'parent_id' => $category->id
            ]);
        } else {
            $subCategory = $this->faker->randomElement($subCategories);
        }

        $vendors = Vendor::all();
        if ($vendors->isEmpty()) {
            $vendor = Vendor::factory()->active()->create();
        } else {
            $vendor = $this->faker->randomElement($vendors);
        }

        // Try to get users with vendor role, fallback to creating a user
        try {
            $users = User::role('vendor')->get();
            $user = $users->isNotEmpty() ? $this->faker->randomElement($users) : User::factory()->create();
        } catch (\Exception $e) {
            // If role doesn't exist, just create a regular user
            $user = User::factory()->create();
        }

        $brands = Brand::where('status', 'approved')->get();
        if ($brands->isEmpty()) {
            $brand = Brand::factory()->create(['status' => 'approved']);
        } else {
            $brand = $this->faker->randomElement($brands);
        }

        $titleEn = $this->faker->words(3, true);
        $regularPrice = $this->faker->randomFloat(2, 20, 500);
        $offerPrice = $regularPrice * $this->faker->randomFloat(2, 0.7, 0.95);

        return [
            // Required fields that exist in the table
            'user_id' => $user->id,
            'vendor_id' => $vendor?->id,
            'category_id' => $category->id,
            'sub_category_id' => $subCategory->id,
            'brand_id' => $brand?->id,
            'vendor_sku' => 'VND-' . $this->faker->unique()->numerify('####'),
            'system_sku' => 'SKU-' . $this->faker->unique()->bothify('########'),
            'barcode' => $this->faker->optional()->numerify('#############'),
            'model_number' => $this->faker->optional()->bothify('MOD-###??'),

            // Product details
            'title_en' => ucwords($titleEn),
            'title_ar' => $this->faker->optional()->sentence(3),
            'short_name' => $this->faker->words(2, true),
            'short_description_en' => $this->faker->sentence(10),
            'short_description_ar' => $this->faker->optional()->sentence(10),
            'description_en' => $this->faker->paragraph(3),
            'description_ar' => $this->faker->optional()->paragraph(2),
            'key_ingredients' => $this->faker->optional()->words(5, true),
            'usage_instruction_en' => $this->faker->optional()->sentence(8),
            'usage_instruction_ar' => $this->faker->optional()->sentence(8),

            // Numeric fields (using IDs for foreign keys)
            'user_group_id' => null,
            'net_weight' => $this->faker->numberBetween(10, 2000),
            'net_weight_unit_id' => null,
            'formulation_id' => null,
            'servings' => $this->faker->optional()->numberBetween(10, 120),
            'flavour_id' => null,

            // Pricing
            'is_variant' => $this->faker->boolean(20),
            'regular_price' => $regularPrice,
            'offer_price' => $offerPrice,
            'vat_tax' => $this->faker->randomElement(['0%', '5%']),
            'discount_start_date' => $this->faker->optional()->dateTimeBetween('now', '+1 month'),
            'discount_end_date' => $this->faker->optional()->dateTimeBetween('+1 month', '+3 months'),
            'approx_commission' => $this->faker->randomFloat(2, 5, 25),

            // Compliance
            'dietary_need_ids' => null,
            'is_vegan' => $this->faker->boolean(30),
            'is_vegetarian' => $this->faker->boolean(50),
            'is_halal' => $this->faker->boolean(80),
            'allergen_info_ids' => null,
            'storage_conditions' => $this->faker->optional()->sentence(6),
            'vat_tax_utl' => $this->faker->optional()->bothify('VAT-####'),
            'regulatory_product_registration' => $this->faker->optional()->bothify('REG-####??'),
            'country_of_origin' => $this->faker->optional()->country(),
            'bbe_date' => $this->faker->dateTimeBetween('+1 year', '+5 years'),
            'fulfillment_id' => null,

            // Package dimensions
            'package_length' => $this->faker->randomFloat(2, 5, 30),
            'package_width' => $this->faker->randomFloat(2, 5, 25),
            'package_height' => $this->faker->randomFloat(2, 5, 35),
            'package_weight' => $this->faker->randomFloat(2, 0.05, 3),

            // Status
            'is_active' => $this->faker->boolean(85),
            'is_approved' => $this->faker->boolean(75),
            'status' => $this->faker->randomElement(['draft', 'pending', 'submitted']),
        ];
    }

    /**
     * Indicate that the product is active and approved.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'is_approved' => true,
            'status' => 'submitted',
        ]);
    }

    /**
     * Indicate that the product is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'is_approved' => false,
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the product is a health supplement.
     */
    public function healthSupplement(): static
    {
        return $this->state(function (array $attributes) {
            $healthCategory = Category::where('code', 'H')->first();
            $vitaminSubcategory = Category::where('code', 'VS')->first();
            
            return [
                'category_id' => $healthCategory?->id,
                'sub_category_id' => $vitaminSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['capsules', 'tablets']),
                'servings' => $this->faker->numberBetween(30, 120),
                'is_halal' => true,
                'usage_instructions' => 'Take 1-2 capsules daily with food',
                'storage_conditions' => 'Store in cool, dry place',
            ];
        });
    }

    /**
     * Indicate that the product is a beauty product.
     */
    public function beauty(): static
    {
        return $this->state(function (array $attributes) {
            $beautyCategory = Category::where('code', 'B')->first();
            $skinCareSubcategory = Category::where('code', 'SC')->first();
            
            return [
                'category_id' => $beautyCategory?->id,
                'sub_category_id' => $skinCareSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['ml', 'g']),
                'net_weight' => $this->faker->numberBetween(15, 100),
                'is_vegan' => $this->faker->boolean(60),
                'usage_instructions' => 'Apply to clean skin as directed',
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
            ];
        });
    }

    /**
     * Indicate that the product is a food item.
     */
    public function food(): static
    {
        return $this->state(function (array $attributes) {
            $foodCategory = Category::where('code', 'F')->first();
            $healthFoodSubcategory = Category::where('code', 'HF')->first();
            
            return [
                'category_id' => $foodCategory?->id,
                'sub_category_id' => $healthFoodSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['g', 'ml', 'kg']),
                'net_weight' => $this->faker->numberBetween(100, 2000),
                'vat_tax' => 0.00, // Food items are VAT exempt in UAE
                'is_halal' => true,
                'storage_conditions' => null, // Will be set to dropdown option ID if needed
            ];
        });
    }

    /**
     * Indicate that the product is a sports nutrition item.
     */
    public function sportsNutrition(): static
    {
        return $this->state(function (array $attributes) {
            $sportsCategory = Category::where('code', 'S')->first();
            $proteinSubcategory = Category::where('code', 'PT')->first();
            
            return [
                'category_id' => $sportsCategory?->id,
                'sub_category_id' => $proteinSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['g', 'kg']),
                'net_weight' => $this->faker->numberBetween(500, 5000),
                'servings' => $this->faker->numberBetween(20, 100),
                'flavour' => $this->faker->randomElement(['vanilla', 'chocolate', 'strawberry', 'unflavored']),
                'usage_instructions' => 'Mix with water or milk as directed',
                'is_halal' => true,
            ];
        });
    }

    /**
     * Indicate that the product has a discount.
     */
    public function onSale(): static
    {
        return $this->state(function (array $attributes) {
            $regularPrice = $attributes['regular_price'] ?? $this->faker->randomFloat(2, 50, 300);
            $discountPercent = $this->faker->randomFloat(2, 0.1, 0.4); // 10-40% discount
            
            return [
                'regular_price' => $regularPrice,
                'offer_price' => $regularPrice * (1 - $discountPercent),
                'discount_start_date' => Carbon::now(),
                'discount_end_date' => Carbon::now()->addDays($this->faker->numberBetween(7, 60)),
            ];
        });
    }
}
