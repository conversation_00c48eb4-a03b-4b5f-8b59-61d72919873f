<?php

namespace Database\Factories;

use App\Models\Warehouse;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

class WarehouseFactory extends Factory
{
    protected $model = Warehouse::class;

    public function definition(): array
    {
        $warehouseTypes = [
            'Distribution Center',
            'Fulfillment Center',
            'Storage Facility',
            'Cold Storage',
            'Logistics Hub',
            'Regional Warehouse',
            'Cross-Dock Facility',
            'Returns Processing Center'
        ];

        $uaeAreas = [
            'Dubai Industrial City',
            'Jebel Ali Free Zone',
            'Dubai Investment Park',
            'Al Quoz Industrial Area',
            'Sharjah Industrial Area',
            'Abu Dhabi Industrial City',
            'Ras Al Khaimah Industrial Zone',
            'Ajman Free Zone',
            'Fujairah Free Zone',
            'Dubai South Logistics District'
        ];

        $warehouseName = $this->faker->randomElement($warehouseTypes) . ' - ' . $this->faker->randomElement($uaeAreas);

        return [
            'user_id' => null, // Will be set in seeder if needed
            'name' => $warehouseName,
            'code' => 'WH-' . strtoupper($this->faker->unique()->bothify('??###')),
            'address' => $this->generateUAEAddress(),
            'location' => $this->generateLocationCoordinates(),
            'contact_person' => $this->faker->name(),
            'contact_number' => $this->generateUAEPhoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
        ];
    }

    private function generateUAEAddress(): string
    {
        $streets = [
            'Sheikh Zayed Road', 'Al Wasl Road', 'Jumeirah Beach Road', 'Emirates Road',
            'Al Khaleej Road', 'Airport Road', 'Industrial Road', 'Logistics Avenue',
            'Trade Center Road', 'Business Bay Boulevard', 'Marina Walk', 'JLT Boulevard'
        ];

        $areas = [
            'Dubai', 'Abu Dhabi', 'Sharjah', 'Ajman', 'Ras Al Khaimah', 'Fujairah', 'Umm Al Quwain'
        ];

        $buildingNumber = $this->faker->numberBetween(1, 999);
        $street = $this->faker->randomElement($streets);
        $area = $this->faker->randomElement($areas);
        $poBox = 'P.O. Box ' . $this->faker->numberBetween(1000, 99999);

        return "{$buildingNumber} {$street}, {$area}, UAE, {$poBox}";
    }

    private function generateLocationCoordinates(): string
    {
        // UAE coordinates range
        $latitude = $this->faker->randomFloat(6, 22.5, 26.5); // UAE latitude range
        $longitude = $this->faker->randomFloat(6, 51.0, 56.5); // UAE longitude range

        return "{$latitude},{$longitude}";
    }

    private function generateUAEPhoneNumber(): string
    {
        $areaCodes = ['02', '03', '04', '06', '07', '09']; // UAE area codes
        $areaCode = $this->faker->randomElement($areaCodes);
        $number = $this->faker->numerify('#######');

        return "+971-{$areaCode}-{$number}";
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
            'is_active' => false,
        ]);
    }

    public function withVendor(): static
    {
        return $this->state(fn (array $attributes) => [
            'vendor_id' => Vendor::factory(),
        ]);
    }

    public function withUser(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => User::factory(),
        ]);
    }

    public function coldStorage(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Cold Storage Facility - ' . $this->faker->randomElement([
                'Dubai Industrial City', 'Jebel Ali', 'Abu Dhabi Industrial'
            ]),
        ]);
    }

    public function distributionCenter(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Distribution Center - ' . $this->faker->randomElement([
                'Dubai South', 'Sharjah Industrial', 'RAK Industrial'
            ]),
        ]);
    }
}
