<?php

namespace Database\Factories;

use App\Models\SupportTicket;
use App\Models\User;
use App\Models\Vendor;
use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\SupportReason;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupportTicketFactory extends Factory
{
    protected $model = SupportTicket::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'vendor_id' => null,
            'tpl_id' => null,
            'order_id' => null,
            'reason_id' => null,
            'category_id' => SupportCategory::factory(),
            'topic_id' => SupportTopic::factory(),
            'subject' => $this->faker->sentence(),
            'message' => $this->faker->paragraph(),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high']),
            'status' => $this->faker->randomElement(['open', 'in_progress', 'resolved', 'closed']),
            'record_status' => 'active',
            'assigned_to' => null,
            'resolved_at' => null,
        ];
    }

    public function withVendor(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'vendor_id' => Vendor::factory(),
            ];
        });
    }

    public function resolved(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'resolved',
                'resolved_at' => now(),
            ];
        });
    }

    public function closed(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'closed',
                'resolved_at' => now(),
            ];
        });
    }
}
