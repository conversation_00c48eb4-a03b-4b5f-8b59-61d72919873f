<?php

namespace Database\Factories;

use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShoppingCart>
 */
class ShoppingCartFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ShoppingCart::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => $this->faker->uuid(),
            'user_id' => User::factory(),
            'session_id' => $this->faker->optional()->uuid(),
            'currency' => 'AED',
            'subtotal' => $this->faker->randomFloat(2, 0, 500),
            'tax_amount' => $this->faker->randomFloat(2, 0, 50),
            'discount_amount' => $this->faker->randomFloat(2, 0, 100),
            'shipping_amount' => $this->faker->randomFloat(2, 0, 50),
            'total_amount' => $this->faker->randomFloat(2, 0, 600),
            'status' => $this->faker->randomElement(['active', 'abandoned', 'converted', 'expired']),
            'expires_at' => $this->faker->optional()->dateTimeBetween('now', '+7 days'),
            'last_activity_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'applied_coupons' => null,
            'applied_discounts' => null,
            'notes' => $this->faker->optional()->sentence(),
            'metadata' => null,
        ];
    }

    /**
     * Indicate that the cart is for a guest user.
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => null,
            'session_id' => $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the cart is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the cart is converted.
     */
    public function converted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'converted',
        ]);
    }

    /**
     * Indicate that the cart is abandoned.
     */
    public function abandoned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'abandoned',
        ]);
    }

    /**
     * Indicate that the cart is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
        ]);
    }
}
