<?php

namespace Database\Factories;

use App\Models\SupportTopic;
use App\Models\SupportCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupportTopicFactory extends Factory
{
    protected $model = SupportTopic::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'category_id' => SupportCategory::factory(),
            'name' => $this->faker->words(3, true),
            'status' => 'active',
        ];
    }
}
