<?php

namespace Database\Factories;

use App\Models\Dropdown;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class DropdownFactory extends Factory
{
    protected $model = Dropdown::class;

    public function definition(): array
    {
        $nameEn = $this->faker->randomElement([
            'Color',
            'Size',
            'Flavor',
            'Brand Type',
            'Package Type',
            'Dosage Form'
        ]);
        
        return [
            'name_en' => $nameEn,
            'name_ar' => $nameEn, // Simplified for testing
            'slug' => Str::slug($nameEn) . '-' . $this->faker->unique()->numberBetween(1, 1000),
        ];
    }
}
