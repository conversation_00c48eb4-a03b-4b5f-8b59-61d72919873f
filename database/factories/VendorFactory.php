<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

class VendorFactory extends Factory
{
    protected $model = Vendor::class;

    public function definition(): array
    {
        return [
            'code' => 'VEN' . $this->faker->unique()->numberBetween(1000, 9999),
            'name_tl_en' => $this->faker->company(),
            'name_tl_ar' => 'شركة ' . $this->faker->company(),
            'vendor_display_name_en' => $this->faker->company(),
            'vendor_display_name_ar' => 'متجر ' . $this->faker->company(),
            'website' => $this->faker->optional()->url(),
            'instagram_page' => $this->faker->optional()->userName(),
            'facebook_page' => $this->faker->optional()->userName(),
            'other_social_media' => $this->faker->optional()->url(),
            'business_type' => $this->faker->randomElement(['retail', 'wholesale', 'manufacturer', 'distributor']),
            'manufacturer_brands' => $this->faker->optional()->words(3, true),
            'categories_to_sell' => $this->faker->optional()->words(5, true),
            'tl_license_issuing_authority' => $this->faker->randomElement(['Dubai Municipality', 'Abu Dhabi Municipality', 'Sharjah Municipality']),
            'tl_license_first_issue_date' => $this->faker->date(),
            'tl_license_renewal_date' => $this->faker->date(),
            'tl_license_valid_till' => $this->faker->dateTimeBetween('+1 year', '+5 years'),
            'tl_entity_type' => $this->faker->randomElement(['LLC', 'FZE', 'FZCO', 'Branch']),
            'tl_no_of_partners' => $this->faker->numberBetween(1, 10),
            'tl_doc_copy_of_trade_license' => $this->faker->optional()->filePath(),
            'tax_registration_number' => $this->faker->numerify('###########'),
            'trn_issue_date' => $this->faker->date(),
            'trn_name_in_english' => $this->faker->company(),
            'trn_name_in_arabic' => 'شركة ' . $this->faker->company(),
            'vat_doc_copy_of_registration_certificate' => $this->faker->optional()->filePath(),
            'director_name' => $this->faker->name(),
            'director_designation' => $this->faker->randomElement(['CEO', 'Managing Director', 'General Manager', 'Director']),
            'director_full_name_passport' => $this->faker->name(),
            'director_passport_number' => $this->faker->regexify('[A-Z]{2}[0-9]{7}'),
            'director_emirates_id_number' => $this->faker->numerify('###-####-#######-#'),
            'director_emirates_id_issue_date' => $this->faker->date(),
            'director_emirates_id_expiry_date' => $this->faker->dateTimeBetween('+1 year', '+10 years'),
            'director_email' => $this->faker->unique()->safeEmail(),
            'director_mobile' => $this->faker->phoneNumber(),
            'director_preferred_language' => $this->faker->randomElement(['en', 'ar']),
            'director_passport_copy' => $this->faker->optional()->filePath(),
            'approval_status' => $this->faker->randomElement(['Pending', 'Approved', 'Rejected']),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the vendor is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => 'Approved',
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the vendor is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => 'Approved',
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the vendor is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => 'Pending',
            'is_active' => false,
        ]);
    }
}
