<?php

namespace Database\Factories;

use App\Models\SupportCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupportCategoryFactory extends Factory
{
    protected $model = SupportCategory::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => $this->faker->words(2, true),
            'status' => 'active',
        ];
    }
}
