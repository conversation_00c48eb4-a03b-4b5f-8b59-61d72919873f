<?php

namespace Database\Factories;

use App\Models\Coupon;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

class CouponFactory extends Factory
{
    protected $model = Coupon::class;

    public function definition(): array
    {
        $type = $this->faker->randomElement(['percentage', 'fixed']);
        $value = $type === 'percentage' 
            ? $this->faker->numberBetween(5, 50) 
            : $this->faker->numberBetween(10, 500);

        return [
            'code' => strtoupper($this->faker->unique()->lexify('????##')),
            'title_en' => $this->faker->sentence(3),
            'title_ar' => 'كوبون خصم ' . $this->faker->numberBetween(5, 50) . '%',
            'description_en' => $this->faker->sentence(10),
            'description_ar' => 'وصف الكوبون باللغة العربية',
            'type' => $type,
            'value' => $value,
            'min_order_value' => $this->faker->optional(0.7)->numberBetween(50, 200),
            'usage_limit' => $this->faker->optional(0.8)->numberBetween(100, 1000),
            'per_user_limit' => $this->faker->optional(0.6)->numberBetween(1, 5),
            'vendor_id' => $this->faker->optional(0.3)->passthrough(
                Vendor::factory()
            ),
            'user_id' => User::factory(),
            'start_date' => $this->faker->optional(0.8)->dateTimeBetween('-1 month', '+1 month'),
            'end_date' => $this->faker->optional(0.8)->dateTimeBetween('+1 month', '+6 months'),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the coupon is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the coupon is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the coupon is a percentage discount.
     */
    public function percentage(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'percentage',
            'value' => $this->faker->numberBetween(5, 50),
        ]);
    }

    /**
     * Indicate that the coupon is a fixed amount discount.
     */
    public function fixed(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'fixed',
            'value' => $this->faker->numberBetween(10, 500),
        ]);
    }

    /**
     * Indicate that the coupon is platform-wide (no vendor).
     */
    public function platformWide(): static
    {
        return $this->state(fn (array $attributes) => [
            'vendor_id' => null,
        ]);
    }

    /**
     * Indicate that the coupon is vendor-specific.
     */
    public function vendorSpecific(): static
    {
        return $this->state(fn (array $attributes) => [
            'vendor_id' => Vendor::factory(),
        ]);
    }

    /**
     * Indicate that the coupon is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => $this->faker->dateTimeBetween('-3 months', '-2 months'),
            'end_date' => $this->faker->dateTimeBetween('-2 months', '-1 month'),
        ]);
    }

    /**
     * Indicate that the coupon is upcoming (not started yet).
     */
    public function upcoming(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => $this->faker->dateTimeBetween('+1 month', '+2 months'),
            'end_date' => $this->faker->dateTimeBetween('+2 months', '+6 months'),
        ]);
    }

    /**
     * Indicate that the coupon is currently valid.
     */
    public function valid(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'end_date' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
        ]);
    }
}
