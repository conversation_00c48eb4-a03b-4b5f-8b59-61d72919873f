<?php

namespace Database\Factories;

use App\Models\Fulfilment;
use Illuminate\Database\Eloquent\Factories\Factory;

class FulfilmentFactory extends Factory
{
    protected $model = Fulfilment::class;

    public function definition(): array
    {
        $nameEn = $this->faker->randomElement([
            'Standard Delivery',
            'Express Delivery',
            'Same Day Delivery',
            'Pickup from Store',
            'Vendor Fulfillment'
        ]);
        
        return [
            'name_en' => $nameEn,
            'name_ar' => $nameEn, // Simplified for testing
            'description_en' => $this->faker->sentence(),
            'description_ar' => $this->faker->sentence(),
            'is_active' => true,
        ];
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
