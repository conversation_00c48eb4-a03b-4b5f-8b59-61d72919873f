<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplateHistory>
 */
class EmailTemplateHistoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplateHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'template_id' => EmailTemplate::factory(),
            'version_number' => $this->faker->numberBetween(1, 10),
            'name' => $this->faker->words(3, true),
            'subject' => $this->faker->sentence(),
            'body_html' => '<html><body><h1>{{user.name}}</h1><p>' . $this->faker->paragraph() . '</p></body></html>',
            'body_text' => $this->faker->paragraph(),
            'variables' => ['user.name', 'site.name'],
            'metadata' => ['type' => 'test', 'priority' => 'medium'],
            'changed_by' => User::factory(),
            'change_reason' => $this->faker->sentence(),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Set the template for this history record.
     */
    public function forTemplate(EmailTemplate $template): static
    {
        return $this->state(fn () => [
            'template_id' => $template->id,
        ]);
    }

    /**
     * Set the version number.
     */
    public function version(int $version): static
    {
        return $this->state(fn () => [
            'version_number' => $version,
        ]);
    }

    /**
     * Set the user who made the change.
     */
    public function changedBy(User $user): static
    {
        return $this->state(fn () => [
            'changed_by' => $user->id,
        ]);
    }
}
