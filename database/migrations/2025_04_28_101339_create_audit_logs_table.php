<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();

            /**
             * Who performed the action
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * What happened
             */
            $table->string('event'); // e.g., created, updated, deleted, login, logout
            $table->string('action_type')->nullable(); // e.g., 'product', 'order', 'settings'

            /**
             * What record was affected
             */
            $table->string('auditable_type'); // e.g., App\Models\Product
            $table->unsignedBigInteger('auditable_id'); // ID of the affected record

            /**
             * Old and new values (if applicable)
             */
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();

            /**
             * Additional context
             */
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
