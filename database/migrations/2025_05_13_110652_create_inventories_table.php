<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->id();

            // Product or variant (one is required, the other nullable)
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');

            // Inventory tracking
            $table->integer('stock')->default(0);        // Current stock
            $table->integer('reserved')->default(0);     // Reserved for orders
            $table->integer('threshold')->default(0);    // Low-stock warning level

            // Optional: Inventory status (can be updated via job/observer)
            $table->enum('stock_status', ['in_stock', 'out_of_stock', 'low_stock'])->nullable();

            // Vendor-specific stock (multi-vendor control)
            $table->string('location')->nullable(); // e.g., "Main Warehouse", "Zone A1"

            // Notes or internal flags
            $table->boolean('is_active')->default(true);
            $table->text('note')->nullable();

            $table->timestamps();

            // Prevent duplicate inventory records per item
            $table->unique(['product_id', 'product_variant_id'], 'inventory_product_variant_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
