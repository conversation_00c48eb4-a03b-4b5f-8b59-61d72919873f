<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_template_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->integer('version_number')->comment('Version number for this template');
            $table->string('name')->comment('Template name at this version');
            $table->string('subject', 500)->comment('Email subject at this version');
            $table->longText('body_html')->comment('HTML body at this version');
            $table->longText('body_text')->nullable()->comment('Text body at this version');
            $table->json('variables')->nullable()->comment('Variables at this version');
            $table->json('metadata')->nullable()->comment('Metadata at this version');
            $table->unsignedBigInteger('changed_by')->nullable();
            $table->string('change_reason', 500)->nullable()->comment('Reason for this change');
            $table->timestamp('created_at')->nullable();

            // Indexes for performance
            $table->index(['template_id', 'version_number']);
            $table->index('created_at');
            $table->index('changed_by');

            // Foreign key constraints
            $table->foreign('template_id')->references('id')->on('email_templates')->onDelete('cascade');
            $table->foreign('changed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_template_histories');
    }
};
