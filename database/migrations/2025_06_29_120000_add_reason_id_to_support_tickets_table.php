<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('support_tickets', function (Blueprint $table) {
            $table->foreignId('reason_id')->nullable()->after('order_id')->constrained('support_reasons')->onDelete('set null');
            $table->foreignId('tpl_id')->nullable()->after('vendor_id')->constrained('tpls')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('support_tickets', function (Blueprint $table) {
            $table->dropForeign(['reason_id']);
            $table->dropColumn('reason_id');
            $table->dropForeign(['tpl_id']);
            $table->dropColumn('tpl_id');
        });
    }
};
