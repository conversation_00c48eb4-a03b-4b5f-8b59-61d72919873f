<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_addresses', function (Blueprint $table) {
          $table->id();
            $table->unsignedBigInteger('vendor_id');
            // Office / Retail / Warehouse
            $table->enum('type', ['office', 'retail_outlet', 'warehouse']);
            $table->text('address')->nullable();
            $table->float('lat')->nullable();
            $table->float('long')->nullable();
            $table->text('map_location')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vendor_addresses');
    }
};
