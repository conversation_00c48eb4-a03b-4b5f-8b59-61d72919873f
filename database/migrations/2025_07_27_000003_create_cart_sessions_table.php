<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_sessions', function (Blueprint $table) {
            $table->id();
            
            // Session identification
            $table->string('session_id')->unique()->index();
            $table->string('fingerprint')->nullable(); // Browser fingerprint for additional security
            
            // Cart data storage for guest users
            $table->json('cart_data'); // Stores cart items and metadata
            $table->json('user_preferences')->nullable(); // Guest user preferences
            
            // Session lifecycle
            $table->timestamp('expires_at');
            $table->timestamp('last_accessed_at')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            // Migration tracking
            $table->boolean('is_migrated')->default(false);
            $table->foreignId('migrated_to_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('migrated_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('expires_at');
            $table->index('last_accessed_at');
            $table->index(['session_id', 'is_migrated']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_sessions');
    }
};
