<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_pricing_tiers', function (Blueprint $table) {
            $table->id();
            
            // Tier details
            $table->string('name'); // e.g., "VIP", "Wholesale", "Premium"
            $table->string('code', 50)->unique(); // e.g., "VIP", "WHOLESALE", "PREMIUM"
            $table->text('description')->nullable();
            
            // Pricing configuration
            $table->decimal('discount_percentage', 5, 2)->nullable(); // Global discount percentage
            $table->decimal('minimum_order_value', 10, 2)->nullable(); // Minimum order to qualify
            $table->integer('priority')->default(0); // Priority for tier application (higher = more priority)
            
            // Tier requirements
            $table->decimal('minimum_annual_spend', 10, 2)->nullable(); // Annual spend requirement
            $table->integer('minimum_orders_count')->nullable(); // Minimum orders to qualify
            
            // Status and configuration
            $table->boolean('is_active')->default(true);
            $table->boolean('auto_assign')->default(false); // Auto-assign based on criteria
            $table->json('benefits')->nullable(); // Additional benefits (free shipping, etc.)
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['is_active', 'priority']);
            $table->index('code');
            $table->index('auto_assign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_pricing_tiers');
    }
};
