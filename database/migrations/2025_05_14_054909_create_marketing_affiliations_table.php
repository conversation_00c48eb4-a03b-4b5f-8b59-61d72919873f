<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This table allows you to onboard both users and external partners as affiliates.
     *
     * Use code to track referrals in orders or carts.
     *
     * You can build reporting tools around clicks, conversions, and total_earned.
     */
    public function up(): void
    {
        Schema::create('marketing_affiliations', function (Blueprint $table) {
            $table->id();

            /**
             * Who the affiliate/influencer is
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // influencer or affiliate user
            $table->string('name')->nullable();  // for external non-user-based affiliates

            /**
             * Affiliate details
             */
            $table->string('code')->unique();       // unique referral code
            $table->string('channel')->nullable();  // e.g., 'instagram', 'youtube', 'email'
            $table->string('tracking_link')->nullable(); // full URL with UTM or redirect

            /**
             * Commission configuration
             */
            $table->enum('commission_type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('commission_value', 10, 2)->default(0);

            /**
             * Analytics
             */
            $table->integer('clicks')->default(0);
            $table->integer('conversions')->default(0);
            $table->decimal('total_earned', 10, 2)->default(0);

            /**
             * Status
             */
            $table->boolean('is_active')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketing_affiliations');
    }
};
