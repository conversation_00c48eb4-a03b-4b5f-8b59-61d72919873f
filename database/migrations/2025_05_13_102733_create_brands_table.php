<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id();

            // Brand Basic Info
            $table->string('name_en'); // English Brand Name
            $table->string('name_ar')->nullable(); // Arabic Brand Name
            $table->string('slug')->unique(); // URL-friendly identifier
            $table->string('country_of_origin')->nullable(); // Country of origin or manufacture
            $table->boolean('is_trademark_registered')->default(false)->comment('Yes/No - Is trademark registered');
            $table->string('website')->nullable();
            $table->string('instagram')->nullable();
            $table->string('facebook')->nullable();

            // Brand Relationships - Yes/No fields
            $table->boolean('manufacturer')->default(false)->comment('Yes/No');
            $table->boolean('brand_owner')->default(false)->comment('Yes/No');
            $table->boolean('marketing_auth_holder')->default(false)->comment('Yes/No - Marketing Authorization Holder');
            $table->boolean('exclusive_distributor')->default(false)->comment('Yes/No');
            $table->boolean('authorized_distributor')->default(false)->comment('Yes/No');
            $table->boolean('wholesaler')->default(false)->comment('Yes/No');
            $table->boolean('authorized_retailer')->default(false)->comment('Yes/No');
            $table->boolean('direct_importer')->default(false)->comment('Yes/No');
            $table->boolean('parallel_importer')->default(false)->comment('Yes/No - Grey Importer');
            $table->boolean('drop_shipper')->default(false)->comment('Yes/No');

            // Brand Visibility
            $table->integer('skus_on_brand_website')->nullable();
            $table->integer('skus_on_amazon')->nullable();
            $table->integer('skus_on_noon')->nullable();
            $table->integer('skus_on_other_marketplaces')->nullable();
            $table->integer('skus_on_own_website')->nullable();

            // Sales Channels - Free text store lists
            $table->text('sold_in_hypermarkets')->nullable()->comment('Comma-separated store names');
            $table->text('sold_in_pharmacies')->nullable()->comment('Comma-separated store names');
            $table->text('sold_in_specialty_stores')->nullable()->comment('Comma-separated store names');

            // Regulatory Compliance
            $table->enum('mohap_registration', ['yes', 'no', 'not_required'])->nullable()->comment('MOHAP Registration Status');
            $table->enum('dubai_municipality_registration', ['yes', 'no', 'not_required'])->nullable()->comment('Dubai Municipality Registration Status');

            // Brand Highlights
            $table->text('brand_usp')->nullable()->comment('Unique Selling Proposition');
            $table->json('top_products')->nullable()->comment('Array of top 3 products');

            // Supporting Documents (file paths or URLs)
            $table->string('logo')->nullable()->comment('Brand logo image');
            $table->string('trademark_document')->nullable()->comment('Trademark registration document');
            $table->string('relationship_proof')->nullable()->comment('Proof of relationship with brand');
            $table->string('purchase_proof')->nullable()->comment('Proof of purchase');
            $table->string('self_declaration')->nullable()->comment('Self-declaration or UTL file');
            $table->json('product_pictures')->nullable()->comment('Array of product image URLs or paths');

            // Status and Activation
            $table->boolean('is_active')->default(true)->comment('Yes/No - Is this brand active');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending')->comment('Approval status');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
