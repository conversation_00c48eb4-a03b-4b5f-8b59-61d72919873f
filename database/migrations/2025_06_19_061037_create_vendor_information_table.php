<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_eoi_id')->nullable();
            $table->string('name_tl_en');
            $table->string('name_tl_ar')->nullable();
            $table->string('vendor_display_name_en')->nullable();
            $table->string('vendor_display_name_ar')->nullable();
            $table->string('website')->nullable();
            $table->string('instagram_page')->nullable();
            $table->string('facebook_page')->nullable();
            $table->string('other_social_media')->nullable();

            $table->json('business_type')->nullable();
            $table->text('manufacturer_brands')->nullable();
            $table->text('categories_to_sell')->nullable();

            // Trade License Info
            $table->string('tl_license_issuing_authority')->nullable();
            $table->date('tl_license_first_issue_date')->nullable();
            $table->date('tl_license_renewal_date')->nullable();
            $table->date('tl_license_valid_till')->nullable();
            $table->string('tl_entity_type')->nullable(); // LLC or PJSC or Sole Prop
            $table->integer('tl_no_of_partners')->default(0)->nullable();
            $table->string('tl_doc_copy_of_trade_license')->nullable();

            // VAT / TAX Registration
            $table->string('tax_registration_number')->nullable();
            $table->date('trn_issue_date')->nullable();
            $table->string('trn_name_in_english')->nullable();
            $table->string('trn_name_in_arabic')->nullable();
            $table->string('vat_doc_copy_of_registration_certificate')->nullable();

            // Director or Signing Authority
            $table->string('director_name')->nullable();
            $table->string('director_designation')->nullable();
            $table->string('director_full_name_passport')->nullable();
            $table->string('director_passport_number')->nullable();
            $table->string('director_emirates_id_number')->nullable();
            $table->date('director_emirates_id_issue_date')->nullable();
            $table->date('director_emirates_id_expiry_date')->nullable();
            $table->string('director_email')->nullable();
            $table->string('director_mobile')->nullable();
            $table->string('director_preferred_language')->nullable();
            $table->string('director_passport_copy')->nullable(); // File path of Passport copy of the director 
            $table->string('director_emirates_id_copy')->nullable(); // File path of Passport copy of the director 

            // SPOC 
            $table->string('spoc_name')->comment('Authorized Primary Contact Person')->nullable();
            $table->string('spoc_designation')->nullable();
            $table->string('spoc_email')->nullable();
            $table->string('spoc_mobile')->nullable();
            $table->string('spoc_passport_number')->nullable();
            $table->string('spoc_emirates_id_number')->nullable();
            $table->date('spoc_emirates_id_issue_date')->nullable();
            $table->date('spoc_emirates_id_expiry_date')->nullable();
            $table->string('spoc_letter_of_authorization')->nullable();
            $table->string('spoc_passport_copy')->nullable(); // File path of Passport copy of the SPOC 
            $table->string('spoc_emirates_id_copy')->nullable(); // File path of emirates ID copy of the SPOC 
            $table->string('spoc_loa_copy')->nullable(); // File path of LOA copy of the SPOC 

            $table->text('additional_info')->nullable(); // Brand USP or other notes
            $table->enum('approval_status', ['Pending', 'Approved', 'Rejected', 'OnHold', 'Cancelled'])->default('Pending');
            $table->bigInteger('approved_by')->nullable();

            $table->text('signing_self_declaration')->nullable();
            $table->boolean('is_active')->default(1);
            $table->softDeletes();

            $table->timestamps();
        });

        Schema::create('vendor_bank_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_infromation_id');
            $table->string('bank_name')->nullable();
            $table->string('branch_name')->nullable();
            $table->string('account_holder_name')->nullable();
            $table->string('iban_number')->nullable();
            $table->string('original_cheque_number')->nullable();
            $table->string('bank_certificate_copy')->nullable();
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('vendor_contact_persons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_infromation_id');
            $table->enum('type', ['alternate_sales_marketing', 'logistics', 'finance_accounting', 'others']);
            $table->string('full_name');
            $table->string('designation')->nullable();
            $table->string('email')->nullable();
            $table->string('mobile_number')->nullable();
            $table->timestamps();
        });

        Schema::create('vendor_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_infromation_id');
            // Office / Retail / Warehouse
            $table->enum('type', ['office', 'retail_outlet', 'warehouse']);
            $table->text('address')->nullable();
            $table->float('lat')->nullable();
            $table->float('long')->nullable();
            $table->text('map_location')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_information');
    }
};
