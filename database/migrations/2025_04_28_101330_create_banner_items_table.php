<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banner_items', function (Blueprint $table) {
            $table->id();

            /**
             * Reference to the banner group this item belongs to
             */
            $table->foreignId('banner_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Media URL (image or video)
             */
            $table->string('media_path'); // path or external URL

            /**
             * Optional click-through link
             */
            $table->string('link_url')->nullable(); // URL to navigate when banner is clicked
            $table->string('target')->nullable();   // e.g., _self or _blank

            /**
             * Alt text for SEO or accessibility
             */
            $table->string('alt_text')->nullable();

            /**
             * Position/order for sorting within the banner group
             */
            $table->integer('position')->default(0);

            /**
             * Active status to enable/disable the item without deleting it
             */
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banner_items');
    }
};
