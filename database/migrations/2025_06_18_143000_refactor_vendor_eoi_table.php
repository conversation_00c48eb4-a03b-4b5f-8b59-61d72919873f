<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_eoi', function (Blueprint $table) {
            // Remove director-related fields
            $table->dropColumn([
                'director_name',
                'director_designation',
                'director_full_name_passport',
                'director_passport_number',
                'director_emirates_id_number',
                'director_emirates_id_issue_date',
                'director_emirates_id_expiry_date',
                'director_email',
                'director_mobile',
                'director_preferred_language'
            ]);

            // Remove document upload fields
            $table->dropColumn([
                'director_passport_copy',
                'director_emirates_id_copy',
                'spoc_passport_copy',
                'spoc_emirates_id_copy',
                'spoc_loa_copy'
            ]);

            // Remove business type specific fields
            $table->dropColumn([
                'manufacturer_brands',
                'importer_brands',
                'distributor_stores',
                'retailer_total_outlets'
            ]);

            // Remove tax/TRN related fields
            $table->dropColumn([
                'tax_registration_number',
                'trn_issue_date',
                'trn_name_in_english',
                'trn_name_in_arabic'
            ]);

            // Remove SPOC document fields but keep basic contact info
            $table->dropColumn([
                'spoc_passport_number',
                'spoc_emirates_id_number',
                'spoc_emirates_id_issue_date',
                'spoc_emirates_id_expiry_date',
                'spoc_letter_of_authorization'
            ]);

            // Remove business_type field
            $table->dropColumn('business_type');
        });

        // Check database driver to use appropriate syntax
        $databaseDriver = Schema::getConnection()->getDriverName();

        if ($databaseDriver === 'pgsql') {
            // PostgreSQL syntax
            DB::statement("ALTER TABLE vendor_eoi ALTER COLUMN tl_entity_type TYPE varchar(255) USING tl_entity_type::varchar");
            DB::statement("ALTER TABLE vendor_eoi ALTER COLUMN tl_entity_type DROP NOT NULL");
        } else {
            // MySQL/MariaDB syntax - drop and recreate the column
            Schema::table('vendor_eoi', function (Blueprint $table) {
                $table->dropColumn('tl_entity_type');
            });
        }

        Schema::table('vendor_eoi', function (Blueprint $table) use ($databaseDriver) {
            // Add new fields
            $table->string('other_social_media')->nullable()->after('instagram_page');
            $table->json('business_data')->nullable()->after('other_social_media');
            $table->enum('inventory_management', ['store_inventory', 'manage_orders', 'both'])->nullable()->after('categories_to_sell');
            $table->json('order_collection_location')->nullable()->after('inventory_management');
            $table->text('order_collection_location_details')->nullable()->after('order_collection_location');

            // Re-add the modified field for MySQL/MariaDB
            if ($databaseDriver !== 'pgsql') {
                $table->enum('tl_entity_type', ['LLC', 'PJSC', 'Sole_Proprietorship', 'Partnership', 'Branch'])->nullable()->after('order_collection_location_details');
            }
        });

        // For PostgreSQL, add the enum constraint
        if ($databaseDriver === 'pgsql') {
            DB::statement("ALTER TABLE vendor_eoi ADD CONSTRAINT tl_entity_type_check CHECK (tl_entity_type IN ('LLC', 'PJSC', 'Sole_Proprietorship', 'Partnership', 'Branch'))");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check database driver
        $databaseDriver = Schema::getConnection()->getDriverName();

        // Drop the check constraint for PostgreSQL
        if ($databaseDriver === 'pgsql') {
            try {
                DB::statement("ALTER TABLE vendor_eoi DROP CONSTRAINT IF EXISTS tl_entity_type_check");
            } catch (\Exception $e) {
                // Constraint might not exist, continue
            }
        }

        Schema::table('vendor_eoi', function (Blueprint $table) use ($databaseDriver) {
            // Remove new fields first
            $table->dropColumn([
                'other_social_media',
                'business_data',
                'inventory_management',
                'order_collection_location',
                'order_collection_location_details'
            ]);

            // For MySQL/MariaDB, drop the recreated tl_entity_type column
            if ($databaseDriver !== 'pgsql') {
                $table->dropColumn('tl_entity_type');
            }
        });

        Schema::table('vendor_eoi', function (Blueprint $table) {
            // Add back removed fields
            $table->string('director_name')->nullable();
            $table->string('director_designation')->nullable();
            $table->string('director_full_name_passport')->nullable();
            $table->string('director_passport_number')->nullable();
            $table->string('director_emirates_id_number')->nullable();
            $table->date('director_emirates_id_issue_date')->nullable();
            $table->date('director_emirates_id_expiry_date')->nullable();
            $table->string('director_email')->nullable();
            $table->string('director_mobile')->nullable();
            $table->string('director_preferred_language')->nullable();
            $table->string('director_passport_copy')->nullable();
            $table->string('director_emirates_id_copy')->nullable();
            $table->string('spoc_passport_copy')->nullable();
            $table->string('spoc_emirates_id_copy')->nullable();
            $table->string('spoc_loa_copy')->nullable();
            $table->text('manufacturer_brands')->nullable();
            $table->text('importer_brands')->nullable();
            $table->text('distributor_stores')->nullable();
            $table->integer('retailer_total_outlets')->nullable();
            $table->string('tax_registration_number')->nullable();
            $table->date('trn_issue_date')->nullable();
            $table->string('trn_name_in_english')->nullable();
            $table->string('trn_name_in_arabic')->nullable();
            $table->string('spoc_passport_number')->nullable();
            $table->string('spoc_emirates_id_number')->nullable();
            $table->date('spoc_emirates_id_issue_date')->nullable();
            $table->date('spoc_emirates_id_expiry_date')->nullable();
            $table->string('spoc_letter_of_authorization')->nullable();
            $table->enum('business_type', ['Manufacturer', 'Importer', 'Distributor', 'Retailer', 'Others'])->default('Others');

            // Re-add tl_entity_type for MySQL/MariaDB
            if ($databaseDriver !== 'pgsql') {
                $table->string('tl_entity_type')->nullable();
            }
        });

        // For PostgreSQL, revert the column type
        if ($databaseDriver === 'pgsql') {
            try {
                DB::statement("ALTER TABLE vendor_eoi ALTER COLUMN tl_entity_type TYPE varchar(255)");
            } catch (\Exception $e) {
                // Column might already be varchar, continue
            }
        }
    }
};
