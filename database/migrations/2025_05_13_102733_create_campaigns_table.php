<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();

            /**
             * Basic details
             */
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->text('description')->nullable();

            /**
             * Media & visual assets
             */
            $table->string('banner_image')->nullable(); // optional campaign banner

            /**
             * Timeframe for the campaign
             */
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();

            /**
             * Status and visibility
             */
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false); // show on homepage or top section

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
