<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * 🧠 How It Works:
     * When users place orders, you log product pairs that appeared together.
     *
     * Over time, analyze the frequency and relationship strength between items.
     *
     * This data powers “Frequently Bought Together”, “You May Also Like”, and combo offers.
     */
    public function up(): void
    {
        Schema::create('basket_analysis', function (Blueprint $table) {
            $table->id();

            /**
             * Primary product in the basket
             */
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            /**
             * Associated product that is frequently purchased with the primary product
             */
            $table->foreignId('associated_product_id')->constrained('products')->onDelete('cascade');

            /**
             * Number of times this pair appeared together in orders or carts
             */
            $table->integer('frequency')->default(1);

            /**
             * Confidence score (optional, used in market basket analysis)
             * Example: out of all users who bought product A, how many also bought product B
             */
            $table->decimal('confidence', 5, 2)->nullable();

            /**
             * Lift score (optional, statistical strength of association)
             * A lift > 1 suggests a strong association between the two products
             */
            $table->decimal('lift', 5, 2)->nullable();

            /**
             * This table is used to track frequently bought-together product pairs.
             * It supports upselling, cross-selling, and combo offer suggestions.
             */
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('basket_analysis');
    }
};
