<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the related tables as they are no longer needed
        Schema::dropIfExists('vendor_eoi_bank_information');
        Schema::dropIfExists('vendor_eoi_contact_persons');
        Schema::dropIfExists('vendor_eoi_locations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the tables in case we need to rollback
        Schema::create('vendor_eoi_bank_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_eoi_id');
            $table->string('bank_name')->nullable();
            $table->string('branch_name')->nullable();
            $table->string('account_holder_name')->nullable();
            $table->string('iban_number')->nullable();
            $table->string('original_cheque_number')->nullable();
            $table->boolean('is_active')->default(1);
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('vendor_eoi_contact_persons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_eoi_id');
            $table->enum('type', ['alternate_sales_marketing', 'logistics', 'finance_accounting', 'others']);
            $table->string('full_name');
            $table->string('designation')->nullable();
            $table->string('email')->nullable();
            $table->string('mobile_number')->nullable();
            $table->timestamps();
        });

        Schema::create('vendor_eoi_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_eoi_id');
            $table->enum('type', ['office', 'retail_outlet', 'warehouse']);
            $table->text('address')->nullable();
            $table->text('map_location')->nullable();
            $table->timestamps();
        });
    }
};
