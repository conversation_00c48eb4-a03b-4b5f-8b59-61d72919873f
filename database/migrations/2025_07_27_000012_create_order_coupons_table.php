<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_coupons', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('coupon_id')->nullable()->constrained()->onDelete('set null'); // Reference to original coupon
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null'); // Vendor-specific coupon
            
            // Coupon snapshot (in case coupon is deleted/modified later)
            $table->string('coupon_code');
            $table->string('coupon_title');
            $table->string('coupon_type'); // percentage, fixed
            $table->decimal('coupon_value', 10, 2);
            $table->decimal('min_order_value', 10, 2)->nullable();
            
            // Applied discount details
            $table->decimal('discount_amount', 10, 2); // Actual discount applied
            $table->decimal('order_subtotal_at_application', 10, 2); // Order subtotal when coupon was applied
            
            // Application details
            $table->timestamp('applied_at')->useCurrent();
            $table->json('metadata')->nullable(); // Additional coupon data
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['order_id', 'applied_at']);
            $table->index('coupon_code');
            $table->index('vendor_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_coupons');
    }
};
