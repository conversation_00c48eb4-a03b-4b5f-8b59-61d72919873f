<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wishlists', function (Blueprint $table) {
            $table->id();

            /**
             * User who saved the item
             */
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            /**
             * Product and optional variant
             */
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Vendor (if applicable in multi-vendor setup)
             */
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Optional: tagging or notes
             */
            $table->text('note')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wishlists');
    }
};
