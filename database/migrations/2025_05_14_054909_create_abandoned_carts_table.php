<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('abandoned_carts', function (Blueprint $table) {
            $table->id();

            /**
             * Identifies the user or session
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // for logged-in users
            $table->string('session_id')->nullable(); // for guests

            /**
             * Snapshot of cart contents (JSON)
             */
            $table->json('items'); // includes product ID, quantity, variant, etc.

            /**
             * Cart total and currency at the time
             */
            $table->decimal('total', 10, 2)->default(0);
            $table->string('currency', 10)->default('USD');

            /**
             * Abandonment tracking
             */
            $table->timestamp('last_interacted_at')->nullable(); // last activity timestamp
            $table->boolean('is_recovered')->default(false);     // if order placed later
            $table->timestamp('reminder_sent_at')->nullable();   // for follow-up emails/sms

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('abandoned_carts');
    }
};
