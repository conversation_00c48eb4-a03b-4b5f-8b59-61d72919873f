<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Supports both manual admin recommendations and ML-based logic.
     * type allows segmentation (e.g., upselling, related, bundles).
     * Can be used for “You may also like”, “Customers also bought”, etc.
     */

    public function up(): void
    {
        Schema::create('product_recommendations', function (Blueprint $table) {
            $table->id();

            /**
             * The base product for which recommendations are being made
             */
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            /**
             * The recommended product
             */
            $table->foreignId('recommended_product_id')->constrained('products')->onDelete('cascade');

            /**
             * Optional metadata
             */
            $table->integer('position')->default(0);         // controls order of appearance
            $table->string('type')->nullable();              // e.g., 'upsell', 'cross-sell', 'related'
            $table->boolean('is_active')->default(true);     // toggle recommendation visibility

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_recommendations');
    }
};
