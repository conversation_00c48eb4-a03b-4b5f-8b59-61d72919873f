<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();

            /**
             * Notification recipient (nullable for broadcast)
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');

            /**
             * Notification content
             */
            $table->string('title');
            $table->text('message')->nullable();

            /**
             * Notification type & context
             */
            $table->enum('type', ['system', 'order', 'promotion', 'support', 'payment', 'custom'])->default('system');
            $table->string('link')->nullable(); // optional URL to redirect on click
            $table->string('icon')->nullable(); // optional icon class

            /**
             * Delivery and status flags
             */
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_popup')->default(false); // whether to show as popup/toast

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
