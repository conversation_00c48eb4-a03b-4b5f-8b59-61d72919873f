<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('search_analytics', function (Blueprint $table) {
            $table->id();

            /**
             * User and session data
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id')->nullable(); // for guest users

            /**
             * Search query details
             */
            $table->string('query');
            $table->integer('results_count')->default(0);   // number of results returned
            $table->boolean('clicked')->default(false);     // if user clicked on any result
            $table->string('clicked_product')->nullable();  // optional product title/ID

            /**
             * Environment data
             */
            $table->string('device')->nullable();           // e.g., mobile, desktop
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('search_analytics');
    }
};
