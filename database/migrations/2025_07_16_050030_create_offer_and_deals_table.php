<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use function PHPSTORM_META\type;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_and_deals', function (Blueprint $table) {
            $table->id();
            $table->string('title_en');  
            $table->string('title_ar')->nullable();                       
            $table->string('tag')->nullable();               
            $table->text('description_en')->nullable();       
            $table->text('description_ar')->nullable();       
            $table->string('image')->nullable();  
            $table->string('link')->nullable();
            $table->string('type')->nullable(); // Changed from enum to string for flexibility
            $table->decimal('regular_price', 10, 2)->nullable(); 
            $table->decimal('offer_price', 10, 2)->nullable();   
            $table->unsignedTinyInteger('discount_percentage')->nullable();
            $table->timestamp('start_time')->nullable(); 
            $table->timestamp('end_time')->nullable(); 
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_and_deals');
    }
};
