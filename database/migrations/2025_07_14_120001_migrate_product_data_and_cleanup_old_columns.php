<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\DropdownOption;
use App\Models\Dropdown;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Since we're doing a fresh database, we only need to clean up the old columns
     * and rename the new ones for API compatibility.
     */
    public function up(): void
    {
        // Remove old columns (they exist but are empty in fresh database)
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['storage_conditions', 'country_of_origin']);
        });

        // Rename new columns to match original field names for API compatibility
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('storage_conditions_id', 'storage_conditions');
            $table->renameColumn('country_of_origin_id', 'country_of_origin');
            $table->renameColumn('is_returnable_id', 'is_returnable');
            $table->renameColumn('warranty_id', 'warranty');
        });
    }

    /**
     * Reverse the migrations.
     *
     * Restore original column structure
     */
    public function down(): void
    {
        // Rename columns back to temporary names
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('storage_conditions', 'storage_conditions_id');
            $table->renameColumn('country_of_origin', 'country_of_origin_id');
            $table->renameColumn('is_returnable', 'is_returnable_id');
            $table->renameColumn('warranty', 'warranty_id');
        });
    

        // Recreate original columns
        Schema::table('products', function (Blueprint $table) {
            $table->text('storage_conditions')->nullable()->after('storage_conditions_id');
            $table->string('country_of_origin')->nullable()->after('country_of_origin_id');
        });

        // Remove the dropdown reference columns
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['storage_conditions_id']);
            $table->dropForeign(['country_of_origin_id']);
            $table->dropForeign(['is_returnable_id']);
            $table->dropForeign(['warranty_id']);

            $table->dropColumn([
                'storage_conditions_id',
                'country_of_origin_id',
                'is_returnable_id',
                'warranty_id'
            ]);
        });
    }

};
