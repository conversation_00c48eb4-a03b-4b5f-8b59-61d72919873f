<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payouts', function (Blueprint $table) {
            $table->id();

            /**
             * Recipient details
             */
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('tpl_id')->nullable()->constrained()->onDelete('set null'); // for third-party logistics partners

            /**
             * Payment information
             */
            $table->decimal('amount', 10, 2);
            $table->string('currency', 10)->default('USD');
            $table->enum('method', ['bank_transfer', 'paypal', 'stripe', 'manual'])->default('bank_transfer');
            $table->string('transaction_id')->nullable(); // from payment gateway

            /**
             * Payout metadata
             */
            $table->enum('status', ['pending', 'processing', 'paid', 'failed'])->default('pending');
            $table->timestamp('paid_at')->nullable();
            $table->text('note')->nullable(); // internal remarks or comments

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payouts');
    }
};
