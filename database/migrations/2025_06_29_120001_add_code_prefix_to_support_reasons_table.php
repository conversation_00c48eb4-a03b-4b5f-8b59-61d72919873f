<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('support_reasons', function (Blueprint $table) {
            $table->string('code_prefix', 2)->nullable()->after('route_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('support_reasons', function (Blueprint $table) {
            $table->dropColumn('code_prefix');
        });
    }
};
