<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_integrations', function (Blueprint $table) {
            $table->id();

            /**
             * Basic integration identity
             */
            $table->string('name');                    // e.g., "Stripe", "Amazon", "PartnerX"
            $table->string('type')->nullable();        // e.g., 'inbound', 'outbound'
            $table->string('description')->nullable(); // short purpose of the integration

            /**
             * Credentials and endpoints
             */
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->string('endpoint_url')->nullable();    // base or callback URL

            /**
             * Status and control
             */
            $table->boolean('is_active')->default(true);
            $table->boolean('log_requests')->default(true); // for debugging or auditing

            /**
             * Optional metadata
             */
            $table->json('config')->nullable();            // store JSON headers, tokens, scopes, etc.

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_integrations');
    }
};
