<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->id();

            /**
             * Promotion type and title
             */
            $table->string('title');
            $table->enum('type', ['flash_sale', 'featured', 'deal_of_the_day', 'vendor_offer'])->default('flash_sale');

            /**
             * Targeting: Apply to specific product or vendor
             */
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');

            /**
             * Discount info
             */
            $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('discount_value', 10, 2)->default(0);

            /**
             * Active period
             */
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();

            /**
             * Status flags
             */
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false); // show in homepage/banner area

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotions');
    }
};
