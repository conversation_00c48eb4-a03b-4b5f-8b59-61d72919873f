<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Member-specific pricing fields
            $table->decimal('member_price', 10, 2)->nullable()->after('offer_price');
            $table->decimal('wholesale_price', 10, 2)->nullable()->after('member_price');
            $table->decimal('vip_price', 10, 2)->nullable()->after('wholesale_price');
            
            // Pricing tier configuration
            $table->json('pricing_tiers')->nullable()->after('vip_price'); // Custom pricing for different tiers
            $table->boolean('enable_member_pricing')->default(false)->after('pricing_tiers');
            
            // Indexes for performance
            $table->index('enable_member_pricing');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['enable_member_pricing']);
            $table->dropColumn([
                'member_price',
                'wholesale_price', 
                'vip_price',
                'pricing_tiers',
                'enable_member_pricing'
            ]);
        });
    }
};
