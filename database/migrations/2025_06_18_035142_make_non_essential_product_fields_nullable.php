<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Make non-essential product fields nullable to simplify product creation.
     * Only essential fields (vendor_id, category_id, sub_category_id, class_id,
     * sub_class_id, vendor_sku, model_number, brand_id) remain required.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Make title_en nullable (was previously required)
            $table->string('title_en')->nullable()->change();

            // Make regular_price nullable (was previously required with default 0)
            $table->decimal('regular_price', 10, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * Restore the original constraints for backward compatibility.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Restore title_en as required (set a default for existing null values)
            $table->string('title_en')->nullable(false)->default('')->change();

            // Restore regular_price as required with default 0
            $table->decimal('regular_price', 10, 2)->default(0)->nullable(false)->change();
        });
    }
};
