<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Pricing tier relationship
            $table->foreignId('pricing_tier_id')->nullable()->after('customer_type')->constrained('customer_pricing_tiers')->onDelete('set null');
            
            // Enhanced customer analytics for tier qualification
            $table->decimal('total_annual_spend', 15, 2)->default(0)->after('pricing_tier_id');
            $table->integer('total_orders_count')->default(0)->after('total_annual_spend');
            $table->timestamp('tier_assigned_at')->nullable()->after('total_orders_count');
            $table->timestamp('last_tier_evaluation_at')->nullable()->after('tier_assigned_at');
            
            // Indexes for performance
            $table->index('pricing_tier_id');
            $table->index(['total_annual_spend', 'total_orders_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropForeign(['pricing_tier_id']);
            $table->dropIndex(['pricing_tier_id']);
            $table->dropIndex(['total_annual_spend', 'total_orders_count']);
            $table->dropColumn([
                'pricing_tier_id',
                'total_annual_spend',
                'total_orders_count',
                'tier_assigned_at',
                'last_tier_evaluation_at'
            ]);
        });
    }
};
