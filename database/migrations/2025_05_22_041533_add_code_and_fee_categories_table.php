<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->string('code')->after('id')->nullable();
            $table->string('fee_text')->after('code')->nullable();
        });

        Schema::table('product_classes', function (Blueprint $table) {
            $table->string('code')->after('id')->nullable();
            $table->boolean('is_popular')->after('code')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn(['code', 'fee_text']);
        });

        Schema::table('product_classes', function (Blueprint $table) {
            $table->dropColumn(['code', 'is_popular']);
        });
    }
};
