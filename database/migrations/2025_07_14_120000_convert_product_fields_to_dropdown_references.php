<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Convert product fields to use dropdown option foreign keys:
     * - storage_conditions: text -> integer (dropdown option ID)
     * - country_of_origin: string -> integer (dropdown option ID)  
     * - is_returnable: add as integer (dropdown option ID)
     * - warranty: add as integer (dropdown option ID)
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // First, add the new columns as nullable integers
            $table->unsignedBigInteger('storage_conditions_id')->nullable()->after('storage_conditions');
            $table->unsignedBigInteger('country_of_origin_id')->nullable()->after('country_of_origin');
            $table->unsignedBigInteger('is_returnable_id')->nullable()->after('country_of_origin_id');
            $table->unsignedBigInteger('warranty_id')->nullable()->after('is_returnable_id');
            
            // Add foreign key constraints
            $table->foreign('storage_conditions_id')->references('id')->on('dropdown_options')->onDelete('set null');
            $table->foreign('country_of_origin_id')->references('id')->on('dropdown_options')->onDelete('set null');
            $table->foreign('is_returnable_id')->references('id')->on('dropdown_options')->onDelete('set null');
            $table->foreign('warranty_id')->references('id')->on('dropdown_options')->onDelete('set null');
        });
        
        // Note: Data migration will be handled separately after dropdown options are seeded
        // This allows for a clean separation of schema changes and data migration
    }

    /**
     * Reverse the migrations.
     * 
     * Remove the new dropdown reference columns and restore original structure
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['storage_conditions_id']);
            $table->dropForeign(['country_of_origin_id']);
            $table->dropForeign(['is_returnable_id']);
            $table->dropForeign(['warranty_id']);
            
            // Drop the new columns
            $table->dropColumn([
                'storage_conditions_id',
                'country_of_origin_id', 
                'is_returnable_id',
                'warranty_id'
            ]);
        });
    }
};
