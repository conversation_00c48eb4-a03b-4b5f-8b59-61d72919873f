<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();

            /**
             * Reviewer and review target
             */
            $table->foreignId('user_id')->constrained()->onDelete('cascade');         // Reviewer
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade'); // Reviewed product
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');  // Reviewed vendor
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');  // Optional reference

            /**
             * Review content
             */
            $table->tinyInteger('rating')->default(0);        // 1 to 5 stars
            $table->text('comment')->nullable();              // Review message

            /**
             * Moderation and visibility
             */
            $table->boolean('is_approved')->default(false);   // Admin moderation
            $table->boolean('is_visible')->default(true);     // For toggling display

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
