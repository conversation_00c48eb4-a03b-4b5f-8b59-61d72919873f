<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('gender')->nullable();                // Optional
            $table->date('date_of_birth')->nullable();           // Optional
            $table->integer('loyalty_points')->default(0);       // Reward program
            $table->string('customer_type')->nullable();         // e.g., 'retail', 'wholesale'
            $table->string('preferred_language', 5)->nullable(); // e.g., 'en', 'ar'
            $table->string('preferred_currency')->nullable();    // e.g., 'AED', 'USD'
            $table->enum('kyc_document_type', ['passport', 'emirates_id', 'driving_license', 'national_id', 'student_id'])->nullable();
            $table->string('kyc_file')->nullable();
            $table->boolean('kyc_verified')->default(false);
            $table->string('referral_code')->nullable();
            $table->string('referred_by')->nullable();
            $table->boolean('loyalty_points_awarded')->default(false);
            $table->string('occupation')->nullable();
            $table->string('designation')->nullable();
            $table->string('company_name')->nullable();
            $table->boolean('newsletter_consent')->default(false);
            $table->boolean('is_vrps')->default(false);
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
