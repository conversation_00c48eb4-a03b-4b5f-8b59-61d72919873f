<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blogs', function (Blueprint $table) {
            $table->id();

            /**
             * Category and optional author
             */
            $table->foreignId('blog_category_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // blog author/admin

            /**
             * Titles and slugs
             */
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->string('slug')->unique();

            /**
             * Content
             */
            $table->text('summary_en')->nullable();
            $table->text('summary_ar')->nullable();
            $table->longText('content_en')->nullable();
            $table->longText('content_ar')->nullable();

            /**
             * SEO metadata
             */
            $table->string('meta_title')->nullable();
            $table->string('meta_description')->nullable();
            $table->string('keywords')->nullable();

            /**
             * Media
             */
            $table->string('featured_image')->nullable();

            /**
             * Publication and status
             */
            $table->enum('status', ['draft', 'published'])->default('draft');
            $table->timestamp('published_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blogs');
    }
};
