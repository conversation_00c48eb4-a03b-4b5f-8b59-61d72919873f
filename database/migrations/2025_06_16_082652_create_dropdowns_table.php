<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('dropdowns')) {
            Schema::create('dropdowns', function (Blueprint $table) {
                $table->id();
                $table->string('name_en');         // English name, e.g. "Color"
                $table->string('name_ar')->nullable(); // Arabic name, e.g. "اللون"
                $table->string('slug')->unique();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dropdowns');
    }
};
