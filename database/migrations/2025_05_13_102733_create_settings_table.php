<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();

            /**
             * Key-value storage for dynamic settings
             */
            $table->string('key')->unique();   // e.g., 'site_name', 'support_email'
            $table->text('value')->nullable(); // can be string, number, or JSON

            /**
             * Optional grouping and description
             */
            $table->string('group')->nullable();       // e.g., 'general', 'email', 'payment'
            $table->text('description')->nullable();   // admin helper info

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
