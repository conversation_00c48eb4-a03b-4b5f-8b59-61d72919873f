<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Add banner_id foreign key column
            $table->foreignId('banner_id')->nullable()->after('ordering_number')->constrained('banners')->onDelete('set null');
            
            // Remove the old banner and cover_image columns
            $table->dropColumn(['banner', 'cover_image']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Drop the foreign key constraint and column
            $table->dropForeign(['banner_id']);
            $table->dropColumn('banner_id');
            
            // Re-add the old banner and cover_image columns
            $table->string('banner')->nullable()->after('ordering_number');
            $table->string('cover_image')->nullable()->after('icon');
        });
    }
};