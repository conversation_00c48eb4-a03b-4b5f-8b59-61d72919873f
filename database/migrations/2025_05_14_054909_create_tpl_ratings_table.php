<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Let users rate delivery experience
     *
     * Track performance of logistics partners
     *
     * Use scores for ranking, dashboard KPIs, or vendor switching decisions
     */
    public function up(): void
    {
        Schema::create('tpl_ratings', function (Blueprint $table) {
            $table->id();

            /**
             * Who gave the rating (customer, vendor, admin, etc.)
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Rated TPL provider
             */
            $table->foreignId('tpl_id')->constrained()->onDelete('cascade');

            /**
             * Optional reference to order
             */
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Rating and feedback
             */
            $table->tinyInteger('rating')->default(0);      // e.g., 1 to 5 stars
            $table->text('comment')->nullable();            // optional user feedback

            /**
             * Moderation and visibility
             */
            $table->boolean('is_approved')->default(true);
            $table->boolean('is_visible')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tpl_ratings');
    }
};
