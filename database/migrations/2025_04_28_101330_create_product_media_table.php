<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductMediaTable extends Migration
{
    public function up()
    {
        Schema::create('product_media', function (Blueprint $table) {
            $table->id();

            // Core product reference
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            // Optional: Variant-specific media
            $table->foreignId('product_variant_id')->nullable()->constrained()->onDelete('cascade');

            // Media type: image, video, lifestyle
            $table->enum('type', ['image', 'video', 'lifestyle'])->default('image');

            // Media URL (can be local path or external)
            $table->string('path');

            // SEO & accessibility metadata
            $table->string('title')->nullable();
            $table->string('alt_text')->nullable();

            // Optional language support (e.g., 'en', 'ar')
            $table->string('lang_code', 5)->nullable();

            // Display ordering
            $table->integer('position')->default(0);

            // Is this the main/default image?
            $table->boolean('is_primary')->default(false);

            $table->timestamps();

            // Unique constraint for upsert
            $table->unique(['product_id', 'path']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_media');
    }
}
