<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Applies to either vendor or product level
             */
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade'); // optional if global/product-based
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade'); // for specific product override

            /**
             * Type of commission: percentage or fixed amount
             */
            $table->enum('type', ['percentage', 'fixed'])->default('percentage');

            /**
             * Commission value
             * - % if type is 'percentage'
             * - flat currency value if type is 'fixed'
             */
            $table->decimal('value', 10, 2)->default(0);

            /**
             * Effective date range (optional)
             */
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();

            /**
             * Optional flags
             */
            $table->boolean('is_active')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commissions');
    }
};
