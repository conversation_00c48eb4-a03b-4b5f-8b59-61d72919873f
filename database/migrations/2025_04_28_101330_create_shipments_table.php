<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();

            /**
             * Associated order and optional order item
             */
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_item_id')->nullable()->constrained()->onDelete('set null'); // optional if item-level shipment

            /**
             * Fulfillment partner (Vendor or TPL)
             */
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('tpl_id')->nullable()->constrained()->onDelete('set null'); // third-party logistics

            /**
             * Shipment tracking
             */
            $table->string('tracking_number')->nullable();
            $table->string('carrier_name')->nullable();        // e.g., Aramex, DHL
            $table->string('carrier_contact')->nullable();     // hotline or support URL
            $table->dateTime('shipped_at')->nullable();
            $table->dateTime('delivered_at')->nullable();

            /**
             * Current status
             */
            $table->enum('status', [
                'pending',
                'processing',
                'shipped',
                'in_transit',
                'delivered',
                'cancelled',
                'returned'
            ])->default('pending');

            /**
             * Internal notes (optional)
             */
            $table->text('remarks')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
