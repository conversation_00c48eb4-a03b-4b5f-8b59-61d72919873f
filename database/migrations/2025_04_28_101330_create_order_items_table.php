<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();

            /**
             * Relation to order and product
             */
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained()->onDelete('set null'); // if variant exists
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');

            /**
             * Product snapshot
             * (in case product details change later)
             */
            $table->string('product_title');
            $table->string('sku')->nullable();
            $table->string('barcode')->nullable();
            $table->json('product_snapshot'); // Complete product snapshot for historical accuracy

            /**
             * Quantity and pricing
             */
            $table->integer('quantity')->default(1);
            $table->decimal('price', 10, 2);               // single unit price
            $table->decimal('total', 10, 2);               // quantity * price
            $table->decimal('discount', 10, 2)->default(0);
            $table->decimal('tax', 10, 2)->default(0);
            $table->decimal('shipping_fee', 10, 2)->default(0);

            /**
             * Enhanced pricing breakdown
             */
            $table->decimal('base_price', 10, 2); // Original product price
            $table->decimal('promotional_price', 10, 2)->nullable(); // If on promotion
            $table->decimal('member_price', 10, 2)->nullable(); // Member-specific pricing
            $table->json('applied_discounts')->nullable(); // Item-specific discounts
            $table->json('customizations')->nullable(); // For customizable products
            $table->text('special_instructions')->nullable();
            $table->json('metadata')->nullable();

            /**
             * Fulfillment status (optional per item)
             */
            $table->enum('fulfillment_status', ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'])->default('pending');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
