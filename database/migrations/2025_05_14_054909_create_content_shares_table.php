<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Helps you measure content reach and which platforms drive the most engagement.
     *
     * Use it to boost social proof, reward sharing behavior, or fine-tune campaigns.
     *
     * You can make this polymorphic to track shares of products, blogs, pages, etc.
     */
    public function up(): void
    {
        Schema::create('content_shares', function (Blueprint $table) {
            $table->id();

            /**
             * Who shared the content
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // customer or vendor
            $table->string('user_type')->nullable(); // optional: 'customer', 'vendor', 'admin'

            /**
             * What was shared (polymorphic relationship)
             */
            $table->string('shareable_type');  // e.g., App\Models\Product, App\Models\Blog
            $table->unsignedBigInteger('shareable_id');

            /**
             * Where it was shared
             */
            $table->enum('platform', ['facebook', 'twitter', 'instagram', 'whatsapp', 'email', 'other'])->nullable();

            /**
             * Optional share metadata
             */
            $table->string('shared_url')->nullable(); // URL of shared content
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_shares');
    }
};
