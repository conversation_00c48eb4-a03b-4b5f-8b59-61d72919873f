<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();

            /**
             * Basic coupon info (multi-language support)
             */
            $table->string('code')->unique(); // e.g., SAVE10, FREESHIP
            $table->string('title')->nullable(); // admin-friendly label (English)
            $table->string('title_ar')->nullable(); // admin-friendly label (Arabic)
            $table->text('description')->nullable(); // description (English)
            $table->text('description_ar')->nullable(); // description (Arabic)

            /**
             * Discount type and value
             */
            $table->enum('type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('value', 10, 2); // discount amount

            /**
             * Usage restrictions
             */
            $table->decimal('min_order_value', 10, 2)->nullable(); // optional minimum order
            $table->integer('usage_limit')->nullable(); // total number of uses
            $table->integer('per_user_limit')->nullable(); // max times one user can apply it

            /**
             * Optional scoping to user, vendor, or product/category
             */
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');

            /**
             * Activation schedule
             */
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();

            /**
             * Status
             */
            $table->boolean('is_active')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
