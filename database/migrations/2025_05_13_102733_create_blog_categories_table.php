<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_categories', function (Blueprint $table) {
            $table->id();

            /**
             * Category titles (multi-language)
             */
            $table->string('title_en');
            $table->string('title_ar')->nullable();

            /**
             * Optional parent category for nesting
             */
            $table->foreignId('parent_id')->nullable()->constrained('blog_categories')->onDelete('cascade');

            /**
             * SEO & status
             */
            $table->string('slug')->unique();
            $table->enum('status', ['active', 'inactive'])->default('active');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_categories');
    }
};
