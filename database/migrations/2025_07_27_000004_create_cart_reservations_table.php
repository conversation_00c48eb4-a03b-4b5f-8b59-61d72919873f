<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_reservations', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('cart_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('variant_id')->nullable()->constrained('product_variants')->onDelete('cascade');
            $table->foreignId('inventory_id')->nullable()->constrained('inventories')->onDelete('cascade');
            
            // Reservation details
            $table->integer('quantity_reserved');
            $table->timestamp('reserved_until');
            $table->enum('status', ['active', 'expired', 'released', 'converted'])->default('active');
            
            // Reservation metadata
            $table->string('reservation_token')->unique();
            $table->text('notes')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['product_id', 'status']);
            $table->index(['reserved_until', 'status']);
            $table->index('reservation_token');
            $table->index(['cart_item_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_reservations');
    }
};
