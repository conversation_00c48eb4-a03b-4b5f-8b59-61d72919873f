<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_tasks', function (Blueprint $table) {
            $table->id();

            /**
             * Task metadata
             */
            $table->string('title');                  // e.g., "Review Vendor Application"
            $table->text('description')->nullable(); // optional detailed instructions

            /**
             * Related entity (polymorphic for flexibility)
             */
            $table->string('taskable_type')->nullable();  // e.g., App\Models\Vendor
            $table->unsignedBigInteger('taskable_id')->nullable();

            /**
             * Assignee and creator tracking
             */
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');

            /**
             * Status and timing
             */
            $table->enum('status', ['open', 'in_progress', 'completed', 'cancelled'])->default('open');
            $table->timestamp('due_date')->nullable();
            $table->timestamp('completed_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_tasks');
    }
};
