<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Store admin-generated reports for later download.
     *
     * Track status of long-running exports or scheduled jobs.
     *
     * Associate reports with user or role access for security.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();

            /**
             * Report metadata
             */
            $table->string('title');                    // e.g., "Monthly Sales Report"
            $table->string('type');                     // e.g., "sales", "orders", "users", "payouts"
            $table->string('period')->nullable();       // e.g., "2025-01", "last_7_days", "Q1-2025"
            $table->string('file_path')->nullable();    // path to generated file (PDF, Excel, CSV)

            /**
             * Who generated or requested the report
             */
            $table->foreignId('generated_by')->nullable()->constrained('users')->onDelete('set null');

            /**
             * Generation status and control
             */
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->timestamp('generated_at')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
