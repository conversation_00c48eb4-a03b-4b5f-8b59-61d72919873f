<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();

            /**
             * Reference to the order and user who made the payment
             */
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Payment gateway and method
             */
            $table->enum('gateway', ['stripe', 'paypal'])->default('stripe');
            $table->enum('method', ['card', 'wallet', 'bank', 'cash_on_delivery'])->nullable();

            /**
             * Transaction identifiers
             */
            $table->string('transaction_id')->nullable();  // from Stripe/PayPal
            $table->string('payment_reference')->nullable(); // internal or external reference

            /**
             * Financials
             */
            $table->decimal('amount', 10, 2);
            $table->string('currency', 10)->default('USD');
            $table->decimal('fee', 10, 2)->nullable(); // gateway/processing fee

            /**
             * Status tracking
             */
            $table->enum('status', ['pending', 'succeeded', 'failed', 'refunded'])->default('pending');
            $table->timestamp('paid_at')->nullable();

            /**
             * Metadata for logs/debugging
             */
            $table->json('response_data')->nullable(); // store full gateway response
            $table->text('note')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
