<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_reconciliations', function (Blueprint $table) {
            $table->id();

            /**
             * Related payment and order
             */
            $table->foreignId('payment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Gateway metadata
             */
            $table->string('gateway')->nullable(); // e.g., stripe, paypal
            $table->string('gateway_transaction_id')->nullable(); // gateway reference ID
            $table->string('payment_reference')->nullable(); // optional internal ref

            /**
             * Financials
             */
            $table->decimal('expected_amount', 10, 2)->default(0);
            $table->decimal('actual_amount', 10, 2)->default(0);
            $table->decimal('difference', 10, 2)->default(0);

            /**
             * Reconciliation status
             */
            $table->enum('status', ['pending', 'matched', 'mismatched', 'resolved'])->default('pending');
            $table->timestamp('reconciled_at')->nullable();

            /**
             * Notes and logs
             */
            $table->text('notes')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_reconciliations');
    }
};
