<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade'); // or link to variant/fulfillment if needed
            $table->string('zone')->nullable(); // e.g. 'local', 'intercity', 'international'
            $table->decimal('min_weight_kg', 8, 2);
            $table->decimal('max_weight_kg', 8, 2);
            $table->decimal('rate', 10, 2); // cost in AED or selected currency
            $table->string('currency')->default('AED');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_rates');
    }
};
