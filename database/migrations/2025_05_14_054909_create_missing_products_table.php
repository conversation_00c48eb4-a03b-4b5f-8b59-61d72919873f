<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Collect input from users who didn’t find what they searched for.
     *
     * Let vendors suggest products they want to sell but can’t find listed.
     *
     * Track fulfillment status as new products are added.
     */
    public function up(): void
    {
        Schema::create('missing_products', function (Blueprint $table) {
            $table->id();

            /**
             * Who submitted the missing product request
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // optional (customer or vendor)
            $table->string('user_type')->nullable(); // 'customer', 'vendor', etc.

            /**
             * Requested product details
             */
            $table->string('product_name');
            $table->string('brand')->nullable();
            $table->string('category')->nullable();
            $table->text('description')->nullable();

            /**
             * Optional input for tracking and follow-up
             */
            $table->string('preferred_vendor')->nullable(); // if a vendor knows who supplies it
            $table->boolean('is_fulfilled')->default(false); // marked true when added to catalog
            $table->timestamp('fulfilled_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('missing_products');
    }
};
