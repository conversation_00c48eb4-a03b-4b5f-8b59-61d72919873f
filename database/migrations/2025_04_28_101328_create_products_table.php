<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver name
        $driver = DB::getDriverName();

        // Handle database-specific UUID setup
        if ($driver === 'pgsql') {
            // PostgreSQL: Enable uuid-ossp extension
            DB::statement('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        }

        Schema::create('products', function (Blueprint $table) use ($driver) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('restrict');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');

            // Database-specific UUID column creation
            if ($driver === 'pgsql') {
                // PostgreSQL: Use uuid_generate_v4() function
                $table->uuid('uuid')->unique()->default(DB::raw('uuid_generate_v4()'));
            } else {
                // MySQL and other databases: Create UUID column without default
                // UUID will be generated in the model's boot method
                $table->uuid('uuid')->unique();
            }
            /**
             * Classification
             */
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('sub_category_id');
            $table->foreignId('class_id')->nullable();
            $table->foreignId('sub_class_id')->nullable();
            $table->string('brand_id')->nullable();
            $table->string('vendor_sku')->unique()->nullable();
            $table->string('system_sku')->unique();  // System-generated SKU No input
            $table->string('barcode')->nullable();
            $table->string('model_number')->nullable();

            /**
             * Product Details
             */
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->string('short_name')->nullable();
            $table->text('short_description_en')->nullable();
            $table->text('short_description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('key_ingredients')->nullable();
            $table->text('usage_instruction_en')->nullable();
            $table->text('usage_instruction_ar')->nullable();
            $table->foreignId('user_group_id')->nullable();

            $table->integer('net_weight')->nullable();
            $table->foreignId('net_weight_unit_id')->nullable();
            $table->foreignId('formulation_id')->nullable();
            $table->integer('servings')->nullable();
            $table->foreignId('flavour_id')->nullable();

            /**
             * Pricing & Inventory
             */
            $table->boolean('is_variant')->default(false);
            $table->decimal('regular_price', 10, 2)->default(0);
            $table->decimal('offer_price', 10, 2)->nullable();
            $table->string('vat_tax')->nullable();
            $table->timestamp('discount_start_date')->nullable();
            $table->timestamp('discount_end_date')->nullable();
            $table->decimal('approx_commission', 10, 2)->nullable();

            /**
             * Compliance & Fulfillment
             */

            $table->json('dietary_need_ids')->nullable();
            $table->boolean('is_vegan')->default(false);
            $table->boolean('is_vegetarian')->default(false);
            $table->boolean('is_halal')->default(false);
            $table->json('allergen_info_ids')->nullable();
            $table->text('storage_conditions')->nullable();
            $table->string('vat_tax_utl')->nullable();
            $table->string('regulatory_product_registration')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->date('bbe_date')->nullable(); // Best Before End
            $table->foreignId('fulfillment_id')->nullable();

            $table->decimal('package_length', 8, 2)->nullable(); // cm
            $table->decimal('package_width', 8, 2)->nullable();  // cm
            $table->decimal('package_height', 8, 2)->nullable(); // cm
            $table->decimal('package_weight', 8, 2)->nullable(); // kg


            /**
             * Status & Control
             */
            $table->boolean('is_active')->default(false);
            $table->boolean('is_approved')->default(false); // Admin review status
            $table->enum('status', ['draft', 'pending', 'submitted',])->default('draft');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
