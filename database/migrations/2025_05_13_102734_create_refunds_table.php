<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refunds', function (Blueprint $table) {
            $table->id();

            /**
             * Relationships
             */
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // who requested the refund
            $table->foreignId('order_item_id')->nullable()->constrained()->onDelete('set null'); // optional item-level refund

            /**
             * Refund details
             */
            $table->decimal('amount', 10, 2);
            $table->enum('reason', ['product_issue', 'customer_request', 'overcharge', 'other'])->default('customer_request');
            $table->text('notes')->nullable();

            /**
             * Gateway and status
             */
            $table->string('gateway_refund_id')->nullable(); // Stripe/PayPal refund reference
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending');
            $table->timestamp('refunded_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refunds');
    }
};
