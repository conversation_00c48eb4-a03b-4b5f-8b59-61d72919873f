<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rewards', function (Blueprint $table) {
            $table->id();

            /**
             * Associated user (customer)
             */
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            /**
             * Source of reward
             */
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null'); // if reward came from a purchase
            $table->enum('type', ['earn', 'redeem', 'adjustment'])->default('earn');

            /**
             * Point transaction
             */
            $table->integer('points'); // positive for earn, negative for redeem
            $table->string('reason')->nullable(); // optional (e.g., "purchase", "signup", "admin adjustment")
            $table->text('note')->nullable();

            /**
             * Tracking and timing
             */
            $table->boolean('is_expired')->default(false);
            $table->timestamp('expires_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rewards');
    }
};
