<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('support_topics', function (Blueprint $table) {
            // Add the new name_ar field first
            $table->string('name_ar')->nullable()->after('name');
        });

        // Rename the existing 'name' column to 'name_en'
        // Using raw SQL for better MySQL/PostgreSQL compatibility
        $connection = config('database.default');
        
        if ($connection === 'mysql') {
            DB::statement('ALTER TABLE `support_topics` CHANGE `name` `name_en` VARCHAR(255) NOT NULL');
        } else {
            // PostgreSQL
            DB::statement('ALTER TABLE "support_topics" RENAME COLUMN "name" TO "name_en"');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rename name_en back to name
        $connection = config('database.default');
        
        if ($connection === 'mysql') {
            DB::statement('ALTER TABLE `support_topics` CHANGE `name_en` `name` VARCHAR(255) NOT NULL');
        } else {
            // PostgreSQL
            DB::statement('ALTER TABLE "support_topics" RENAME COLUMN "name_en" TO "name"');
        }

        Schema::table('support_topics', function (Blueprint $table) {
            // Drop the name_ar field
            $table->dropColumn('name_ar');
        });
    }
};
