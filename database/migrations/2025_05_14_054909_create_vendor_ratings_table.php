<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_ratings', function (Blueprint $table) {
            $table->id();

            /**
             * Who submitted the rating (customer or admin)
             */
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Vendor being rated
             */
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade');

            /**
             * Optional link to the order for verified rating
             */
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');

            /**
             * Rating value and optional written feedback
             */
            $table->tinyInteger('rating')->default(0);     // 1 to 5 scale
            $table->text('comment')->nullable();

            /**
             * Moderation and visibility controls
             */
            $table->boolean('is_approved')->default(true); // admin-approved
            $table->boolean('is_visible')->default(true);  // display on storefront

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_ratings');
    }
};
