<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use <PERSON><PERSON>\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
{
    $groups = [
        'orders' => [
            'browse_orders','view_order','create_order','edit_order','delete_order',
            'deliver_order','manage_shipment_tracking','log_payment_gateway_transactions'
        ],
        'products' => [
            'browse_products','view_product','create_product','edit_product','delete_product',
            'manage_product_variants','manage_sponsored_products','manage_flash_sales_and_featured_deals'
        ],
        'users' => [
            'browse_users','view_user','create_user','edit_user','delete_user','manage_buyer_accounts'
        ],
        'vendors' => [
            'browse_vendors','view_vendor','create_vendor','edit_vendor','delete_vendor',
            'approve_vendor','suspend_vendor','manage_seller_accounts'
        ],
        'categories' => [
            'browse_categories','view_category','create_category','edit_category','delete_category'
        ],
        'product_variants' => [
            'browse_product_variants','view_variant','create_product_variant',
            'edit_product_variant','delete_product_variant'
        ],
        'commissions' => [
            'browse_commissions','view_commission','calculate_commission_earnings',
            'manage_commission_payouts'
        ],
        'shipments' => [
            'browse_shipments','view_shipment','create_shipment','edit_shipment',
            'delete_shipment','update_shipment_status'
        ],
        'coupons' => [
            'browse_coupons','view_coupon','create_coupon','edit_coupon',
            'delete_coupon','apply_coupon'
        ],
        'banners' => [
            'browse_banners','view_banner','create_banner','edit_banner',
            'delete_banner','manage_homepage_banners'
        ],
        'reviews' => [
            'browse_reviews','view_review','approve_review','reject_review','delete_review'
        ],
        'support_tickets' => [
            'browse_support_tickets','view_ticket','create_support_ticket',
            'edit_support_ticket','delete_support_ticket','assign_support_ticket','manage_ticket_status'
        ],
        'audit_logs' => [
            'browse_audit_logs','view_audit_log','export_audit_logs','view_sensitive_admin_actions'
        ],
        'roles' => [
            'manage_roles_and_permissions'
        ],
        'settings' => [
            'manage_settings','manage_currency_settings'
        ],
        'system' => [
            'manage_maintenance_mode','schedule_and_manage_cron_jobs','define_error_handling_and_logging'
        ],
        'deployment' => [
            'manage_deployment_pipelines','set_up_deployment_scripts'
        ],
        'environment' => [
            'manage_environment_variables_and_secret_keys'
        ],
        'database' => [
            'install_and_configure_database_server','manage_database_backups','manage_caching_system'
        ],
        'analytics' => [
            'view_sales_reports','view_tax_report','analyze_buyer_behavior','analyze_product_performance',
            'track_sales_trends','conduct_churn_analysis'
        ],
        'marketing' => [
            'manage_discount_codes_and_campaigns','manage_email_marketing_campaigns',
            'manage_push_notifications','manage_affiliate_and_referral_programs'
        ],
        'content' => [
            'create_and_edit_informational_pages','manage_blog_posts_and_content',
            'create_developer_guide_for_project_setup'
        ],
        'performance' => [
            'install_performance_monitoring_tools','add_code_formatting_and_quality_tools'
        ],
        'permissions' => [
            'set_up_admin_roles_and_permissions_in_the_database'
        ],
    ];

    // Create permissions
    foreach ($groups as $group => $perms) {
        foreach ($perms as $perm) {
            Permission::updateOrCreate(
                ['name' => $perm, 'guard_name' => 'api'],
                ['group_name' => $group]
            );
        }
    }

    // Roles
    $roles = [
        'user','admin','admin_assistant','admin_logistics','admin_finance','admin_sales',
        'vendor','vendor_assistant','vendor_logistics','vendor_finance','vendor_sales',
        'delivery_partner','storage_partner','customer'
    ];

    foreach ($roles as $role) {
        Role::firstOrCreate(['name' => $role, 'guard_name' => 'api']);
    }

    // Assign permissions to roles
    Role::findByName('admin', 'api')->syncPermissions(Permission::all());
    Role::findByName('vendor', 'api')->syncPermissions(Permission::whereIn('name', array_merge(
        $groups['products'], $groups['orders'], $groups['shipments']
    ))->pluck('id'));

    Role::findByName('vendor_assistant', 'api')->syncPermissions(Permission::whereIn('name', [
        'browse_orders','view_order','browse_products','view_product'
    ])->pluck('id'));

    Role::findByName('vendor_logistics', 'api')->syncPermissions(Permission::whereIn('name', [
        'browse_shipments','view_shipment','update_shipment_status'
    ])->pluck('id'));

    Role::findByName('vendor_finance', 'api')->syncPermissions(Permission::whereIn('name', [
        'browse_orders','log_payment_gateway_transactions','view_sales_reports'
    ])->pluck('id'));

    Role::findByName('vendor_sales', 'api')->syncPermissions(Permission::whereIn('name', [
        'manage_sponsored_products','manage_flash_sales_and_featured_deals','view_sales_reports'
    ])->pluck('id'));

    Role::findByName('delivery_partner', 'api')->syncPermissions(Permission::whereIn('name', [
        'deliver_order','update_shipment_status','view_shipment'
    ])->pluck('id'));

    Role::findByName('storage_partner', 'api')->syncPermissions(Permission::whereIn('name', [
        'create_shipment','manage_shipment_tracking'
    ])->pluck('id'));
}

}
