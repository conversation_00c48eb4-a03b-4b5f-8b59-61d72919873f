<?php

namespace Database\Seeders;

use App\Models\Vendor;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class VendorSeeder extends Seeder
{
    public function run(): void
    {
        $vendors = [
            [
                'code' => 'VND001',
                'vendor_eoi_id' => null,
                'name_tl_en' => 'HealthPlus Trading LLC',
                'name_tl_ar' => 'هيلث بلس للتجارة ذ.م.م',
                'vendor_display_name_en' => 'HealthPlus',
                'vendor_display_name_ar' => 'هيلث بلس',
                'website' => 'https://healthplus.ae',
                'instagram_page' => null,
                'facebook_page' => null,
                'other_social_media' => null,
                'business_type' => json_encode(['distributor']),
                'manufacturer_brands' => 'HealthPlus, BioMed',
                'categories_to_sell' => 'Healthcare, Supplements',

                // Trade License Info
                'tl_license_issuing_authority' => 'Dubai Economic Department',
                'tl_license_first_issue_date' => Carbon::now()->subYears(3),
                'tl_license_renewal_date' => Carbon::now()->subYear(),
                'tl_license_valid_till' => Carbon::now()->addYears(2),
                'tl_entity_type' => 'LLC',
                'tl_no_of_partners' => 2,
                'tl_doc_copy_of_trade_license' => null,

                // VAT / TAX Registration
                'tax_registration_number' => '100123456789003',
                'trn_issue_date' => Carbon::now()->subYears(3),
                'trn_name_in_english' => 'HealthPlus Trading LLC',
                'trn_name_in_arabic' => 'هيلث بلس للتجارة ذ.م.م',
                'vat_doc_copy_of_registration_certificate' => null,

                // Director or Signing Authority
                'director_name' => 'Ahmed Al Mansouri',
                'director_designation' => 'Managing Director',
                'director_full_name_passport' => 'Ahmed Mohammed Al Mansouri',
                'director_passport_number' => '********',
                'director_emirates_id_number' => '784-1985-1234567-8',
                'director_emirates_id_issue_date' => Carbon::now()->subYears(5),
                'director_emirates_id_expiry_date' => Carbon::now()->addYears(5),
                'director_email' => '<EMAIL>',
                'director_mobile' => '+971501234567',
                'director_preferred_language' => 'eng',
                'director_passport_copy' => null,
                'director_emirates_id_copy' => null,

                // SPOC
                'spoc_name' => 'Sarah Al Mansouri',
                'spoc_designation' => 'Operations Manager',
                'spoc_email' => '<EMAIL>',
                'spoc_mobile' => '+971507654321',
                'spoc_passport_number' => 'A7654321',
                'spoc_emirates_id_number' => '784-1990-7654321-2',
                'spoc_emirates_id_issue_date' => Carbon::now()->subYears(3),
                'spoc_emirates_id_expiry_date' => Carbon::now()->addYears(7),
                'spoc_letter_of_authorization' => null,
                'spoc_passport_copy' => null,
                'spoc_emirates_id_copy' => null,
                'spoc_loa_copy' => null,

                // Misc
                'additional_info' => 'Leading healthcare supplier in UAE.',
                'approval_status' => 'Approved',
                'approved_by' => null,
                'signing_self_declaration' => 'Ahmed Al Mansouri',
                'is_active' => true,
            ],
        ];

        foreach ($vendors as $vendor) {
            Vendor::updateOrCreate(
                ['name_tl_en' => $vendor['name_tl_en']], // or use 'vendor_eoi_id' if it's unique
                $vendor
            );
        }
    }
}
