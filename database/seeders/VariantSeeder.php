<?php

namespace Database\Seeders;

use App\Models\Variant;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $variants = [
            [
                'name_en' => 'Color',
                'name_ar' => 'لون',
                'category_id' => 1,
                'sub_category_id' => 2,
                'status' => 'active',
                'user_id' => 1,
            ],
            [
                'name_en' => 'Size',
                'name_ar' => 'حجم',
                'category_id' => 1,
                'sub_category_id' => 3,
                'status' => 'active',
                'user_id' => 1,
            ],
            [
                'name_en' => 'Material',
                'name_ar' => 'مادة',
                'category_id' => 2,
                'sub_category_id' => null,
                'status' => 'inactive',
                'user_id' => 1,
            ],
            [
                'name_en' => 'Style',
                'name_ar' => 'نمط',
                'category_id' => 2,
                'sub_category_id' => 4,
                'status' => 'active',
                'user_id' => 1,
            ],
            [
                'name_en' => 'Pattern',
                'name_ar' => 'نمط',
                'category_id' => 3,
                'sub_category_id' => null,
                'status' => 'active',
                'user_id' => 1,
            ],
            [
                'name_en' => 'Brand',
                'name_ar' => 'علامة تجارية',
                'category_id' => 4,
                'sub_category_id' => 5,
                'status' => 'inactive',
                'user_id' => 1,
            ],

        ];

        Variant::insert($variants);
    }
}
