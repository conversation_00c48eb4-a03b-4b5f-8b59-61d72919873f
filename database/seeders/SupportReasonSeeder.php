<?php

namespace Database\Seeders;

use App\Models\SupportReason;
use Illuminate\Database\Seeder;

class SupportReasonSeeder extends Seeder
{
    public function run(): void
    {
        $reasons = [
            // Customer → Admin
            [
                'label' => 'Account Issues',
                'route_to' => 'admin',
                'code_prefix' => 'CA',
                'status' => 'active'
            ],
            [
                'label' => 'Payment Problems',
                'route_to' => 'admin',
                'code_prefix' => 'CA',
                'status' => 'active'
            ],
            [
                'label' => 'Technical Support',
                'route_to' => 'admin',
                'code_prefix' => 'CA',
                'status' => 'active'
            ],
            
            // Customer → Vendor
            [
                'label' => 'Product Quality Issues',
                'route_to' => 'vendor',
                'code_prefix' => 'CV',
                'status' => 'active'
            ],
            [
                'label' => 'Refund Request',
                'route_to' => 'vendor',
                'code_prefix' => 'CV',
                'status' => 'active'
            ],
            [
                'label' => 'Product Information',
                'route_to' => 'vendor',
                'code_prefix' => 'CV',
                'status' => 'active'
            ],
            [
                'label' => 'Vendor Communication',
                'route_to' => 'vendor',
                'code_prefix' => 'CV',
                'status' => 'active'
            ],
            
            // Customer → TPL
            [
                'label' => 'Delivery Issues',
                'route_to' => 'tpl',
                'code_prefix' => 'CT',
                'status' => 'active'
            ],
            [
                'label' => 'Shipping Delays',
                'route_to' => 'tpl',
                'code_prefix' => 'CT',
                'status' => 'active'
            ],
            [
                'label' => 'Package Damage',
                'route_to' => 'tpl',
                'code_prefix' => 'CT',
                'status' => 'active'
            ],
            [
                'label' => 'Tracking Issues',
                'route_to' => 'tpl',
                'code_prefix' => 'CT',
                'status' => 'active'
            ],
            
            // Vendor → Admin
            [
                'label' => 'Commission Disputes',
                'route_to' => 'admin',
                'code_prefix' => 'VA',
                'status' => 'active'
            ],
            [
                'label' => 'Platform Issues',
                'route_to' => 'admin',
                'code_prefix' => 'VA',
                'status' => 'active'
            ],
            [
                'label' => 'Policy Questions',
                'route_to' => 'admin',
                'code_prefix' => 'VA',
                'status' => 'active'
            ],
            
            // TPL → Admin
            [
                'label' => 'Service Agreement Issues',
                'route_to' => 'admin',
                'code_prefix' => 'TA',
                'status' => 'active'
            ],
            [
                'label' => 'Payment Disputes',
                'route_to' => 'admin',
                'code_prefix' => 'TA',
                'status' => 'active'
            ],
            [
                'label' => 'System Integration',
                'route_to' => 'admin',
                'code_prefix' => 'TA',
                'status' => 'active'
            ],
        ];

        foreach ($reasons as $reason) {
            SupportReason::create($reason);
        }
    }
}
