<?php

namespace Database\Seeders;

use App\Models\Banner;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    public function run()
    {
        Category::query()->delete();

        $csvData = $this->parseCsvData();

        $this->createCategories($csvData);
    }

    protected function parseCsvData(): array
    {
        $banner = Banner::first();
        return [
            [
                'code' => 'H',
                'name' => 'VHMS',
                'fee_text' => '10%',
                'icon'=>'uploads/cccfc1dd-c43e-4db1-8102-b7b0cb1a20d4.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'VS', 'name' => 'Vitamin'],
                    ['code' => 'HS', 'name' => 'Herbs'],
                    ['code' => 'MS', 'name' => 'Mineral'],
                    ['code' => 'SS', 'name' => 'Speciality Supplements'],
                    ['code' => 'AO', 'name' => 'Antioxidants'],
                    ['code' => 'BS', 'name' => 'Beauty within'],
                    ['code' => 'CH', 'name' => 'Children\'s Health'],
                    ['code' => 'DH', 'name' => 'Digestive Health'],
                    ['code' => 'EE', 'name' => 'Energy & Endurance'],
                    ['code' => 'FO', 'name' => 'Fish Oil & Omegas'],
                    ['code' => 'SF', 'name' => 'Greens & Superfoods'],
                    ['code' => 'HH', 'name' => 'Heart Health'],
                    ['code' => 'IM', 'name' => 'Immune Support'],
                    ['code' => 'JB', 'name' => 'Joint & Bone'],
                    ['code' => 'KD', 'name' => 'Kidney Support'],
                    ['code' => 'LV', 'name' => 'Liver Support'],
                    ['code' => 'MH', 'name' => 'Men\'s Health'],
                    ['code' => 'NB', 'name' => 'Nerve & Brain Health'],
                    ['code' => 'OG', 'name' => 'Organs & Glandular'],
                    ['code' => 'WH', 'name' => 'Women\'s Health'],
                    ['code' => 'SR', 'name' => 'Sleep & Relaxation'],
                    ['code' => 'CD', 'name' => 'Cleansing & Detox'],
                    ['code' => 'DS', 'name' => 'Diabetes Support'],
                    ['code' => 'EN', 'name' => 'Ear Eye & Nose'],
                    ['code' => 'MS', 'name' => 'Misc Supps'],
                ],
            ],
            [
                'code' => 'B',
                'name' => 'Beauty',
                'fee_text' => '10 TO 15%',
                'icon' => 'uploads/ad004508-4ac9-4a42-8356-e47312f4b8a2.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'SC', 'name' => 'Skin Care'],
                    ['code' => 'HC', 'name' => 'Hair Care'],
                    ['code' => 'PC', 'name' => 'Personal Care'],
                    ['code' => 'KB', 'name' => 'K Beauty'],
                    ['code' => 'MC', 'name' => 'Make up & Colour'],
                    ['code' => 'FP', 'name' => 'Fragrance & Perfume'],
                    ['code' => 'BH', 'name' => 'Bath & Home'],
                    ['code' => 'TE', 'name' => 'Tools & Equip'],
                    ['code' => 'MG', 'name' => 'Male Grooming'],
                    ['code' => 'BS', 'name' => 'Beauty Supplements'],
                    ['code' => '', 'name' => 'Hair Removal'],
                    ['code' => '', 'name' => 'Hair, Skin and Nail'],
                ],
            ],
            [
                'code' => 'MB',
                'name_en' => 'Mom & Baby',
                'name_ar' => 'الأم والطفل',
                'name' => 'Mom & Baby',
                'fee_text' => '10%',
                'icon'=> 'uploads/f9471058-6d41-4af7-b47d-f0f343a0a448.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'PG', 'name' => 'Pregnancy'],
                    ['code' => 'BF', 'name' => 'Breastfeeding'],
                    ['code' => 'BN', 'name' => 'Baby Nutrition'],
                    ['code' => 'BC', 'name' => 'Baby Care'],
                    ['code' => 'BH', 'name' => 'Baby Health'],
                    ['code' => 'BT', 'name' => 'Baby Toys'],
                    ['code' => 'BS', 'name' => 'Baby Safety'],
                    ['code' => 'MH', 'name' => 'Maternal Health'],
                    ['code' => 'PP', 'name' => 'Postpartum Care'],
                    ['code' => 'IF', 'name' => 'Infant Formula'],
                    ['code' => 'BFD', 'name' => 'Baby Food'],
                    ['code' => 'DI', 'name' => 'Diapers'],
                ],
            ],
            [
                'code' => 'F',
                'name' => 'Food & Drink',
                'fee_text' => '10%',
                'icon'=> 'uploads/827d6d4e-a661-4e1a-9d44-c49d4a3bab66.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'HF', 'name' => 'Health Food'],
                    ['code' => 'BV', 'name' => 'Beverages'],
                    ['code' => 'FF', 'name' => 'Free From Sp Food'],
                    ['code' => 'TC', 'name' => 'Tea & Coffee'],
                    ['code' => 'HS', 'name' => 'Honey & Spreads'],
                    ['code' => 'SK', 'name' => 'Healthy Snacks'],
                    ['code' => 'NS', 'name' => 'Nuts & Seeds'],
                    ['code' => 'OR', 'name' => 'Organic & Raw'],
                    ['code' => 'SF', 'name' => 'SuperFoods & Greens'],
                    ['code' => 'VF', 'name' => 'Vegan Food'],
                    ['code' => '', 'name' => 'Memory & Brain Support'],
                    ['code' => '', 'name' => 'Hormone Boosters'],
                    ['code' => '', 'name' => 'Betaine HCL'],
                    ['code' => '', 'name' => 'Digestive Enzymes'],
                    ['code' => '', 'name' => 'Prebiotic'],
                    ['code' => '', 'name' => 'Probiotic'],
                    ['code' => '', 'name' => 'Fiber Supplement'],
                    ['code' => '', 'name' => 'Notropic & Nerve Health'],
                    ['code' => '', 'name' => 'Free From'],
                    ['code' => '', 'name' => 'Gluten Free'],
                    ['code' => '', 'name' => 'Sugar Free'],
                    ['code' => '', 'name' => 'Nut Free'],
                    ['code' => '', 'name' => 'Fat Free'],
                    ['code' => '', 'name' => 'Lactose Free'],
                    ['code' => '', 'name' => 'GMO Free'],
                    ['code' => '', 'name' => 'Other Free From Food'],
                ],
            ],
            [
                'code' => 'S',
                'name' => 'Sports Nutrition',
                'fee_text' => '4 TO 7%',
                'icon' => 'uploads/82be4505-9e60-4c8d-b19e-c8511d5d1fd2.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'PT', 'name' => 'Protein'],
                    ['code' => 'CR', 'name' => 'Creatine'],
                    ['code' => 'PW', 'name' => 'Pre-Workout Boosters'],
                ],
            ],
            [
                'code' => 'W',
                'name' => 'Weight Management',
                'fee_text' => '10%',
                'icon'=> 'uploads/8332a7fc-09cd-42ef-af28-25a3b05aa2d4.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'WL', 'name' => 'Weight Loss'],
                    ['code' => 'WG', 'name' => 'Weight Gain'],
                    ['code' => 'KT', 'name' => 'Keto'],
                ],
            ],
            [
                'code' => 'P',
                'name' => 'Para Pharmacy',
                'fee_text' => '4 TO 10%',
                'icon'=> 'uploads/f848c89b-d5ef-49f6-a253-3af277c5b4d0.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'OT', 'name' => 'OTC'],
                    ['code' => 'PA', 'name' => 'Accessories'],
                    ['code' => 'OB', 'name' => 'Ointments & Balms'],
                ],
            ],
            [
                'code' => 'E',
                'name' => 'Med Equipment',
                'fee_text' => '10%',
                'icon'=> 'uploads/75d3c27c-8b66-47f4-ad6a-fee4c85523ae.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => 'BP', 'name' => 'BP Monitor'],
                    ['code' => 'BS', 'name' => 'Glucometer'],
                    ['code' => 'HR', 'name' => 'Heart Monitors'],
                ],
            ],
            [
                'code' => 'G',
                'name' => 'Misc Gen Items',
                'fee_text' => '4 to 10%',
                'icon'=> 'uploads/f733489d-d9d1-47a0-aab9-77bcd2f7152b.svg',
                'banner_id' => $banner->id??null,
                'subcategories' => [
                    ['code' => '', 'name' => 'First AID'],
                    ['code' => '', 'name' => 'Safety'],
                    ['code' => '', 'name' => 'Hygine'],
                ],
            ],
        ];
    }

    protected function createCategories(array $categoriesData): void
    {
        foreach ($categoriesData as $categoryData) {
            $category = Category::create([
                'name_en'         => $categoryData['name_en'] ?? $categoryData['name'],
                'name_ar'         => $categoryData['name_ar'] ?? null,
                'code'            => $categoryData['code'],
                'fee_text'        => $categoryData['fee_text'],
                'slug'            => Str::slug($categoryData['name']),
                'type'            => 'main',
                'parent_id'       => null,
                'ordering_number' => $this->getOrderingNumber($categoryData['code']),
                'icon'            => $categoryData['icon'] ?? null,
                'banner_id'       => $categoryData['banner_id'] ?? null,
            ]);

            foreach ($categoryData['subcategories'] as $index => $subcategoryData) {
                Category::create([
                    'name_en'         => $subcategoryData['name'],
                    'code'            => $subcategoryData['code'],
                    'slug'            => Str::slug($subcategoryData['name']),
                    'type'            => 'sub',
                    'parent_id'       => $category->id,
                    'ordering_number' => $index + 1,
                    'status'          => 'active',
                ]);
            }
        }
    }

    protected function getOrderingNumber(string $code): int
    {
        return [
            'H' => 1,
            'B' => 2,
            'MB' => 3,
            'F' => 4,
            'S' => 5,
            'W' => 6,
            'P' => 7,
            'E' => 8,
            'G' => 9,
        ][$code] ?? 999;
    }
}