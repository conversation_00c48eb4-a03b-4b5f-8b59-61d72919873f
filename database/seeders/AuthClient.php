<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AuthClient extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // oauth_clients // row 

        DB::table('oauth_clients')->insert([
            'id' => '9ef2435a-9f0c-4a2e-b55e-d45e03efdb89',
            'user_id' => null,
            'name' => 'Laravel Personal Access Client',
            'secret' => '0wue2LDJryzH7jdhzikWhZH136DU3JWEWewd81U1',
            'provider' => "users",
            'redirect' => 'http://localhost',
            'personal_access_client' => '1',
            'password_client' => '1',
            'revoked' => '0',
            'created_at' => now(),
            'updated_at' => now(),
        ]);


    }
}
