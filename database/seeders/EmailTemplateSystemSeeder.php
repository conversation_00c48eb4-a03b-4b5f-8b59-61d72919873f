<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class EmailTemplateSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed email template categories
        $this->seedCategories();

        // Seed email template variables
        $this->seedVariables();

        // Seed default email templates
        $this->seedDefaultTemplates();
    }

    /**
     * Seed email template categories
     */
    private function seedCategories(): void
    {
        $categories = [
            [
                'name' => 'Authentication & Security',
                'slug' => 'authentication-security',
                'description' => 'Templates for user authentication, verification, and security-related communications',
                'icon' => 'fas fa-shield-alt',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Order Management',
                'slug' => 'order-management',
                'description' => 'Templates for order confirmations, updates, and shipping notifications',
                'icon' => 'fas fa-shopping-cart',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Vendor Communications',
                'slug' => 'vendor-communications',
                'description' => 'Templates for vendor onboarding, approvals, and business communications',
                'icon' => 'fas fa-store',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Customer Engagement',
                'slug' => 'customer-engagement',
                'description' => 'Templates for customer engagement, promotions, and marketing communications',
                'icon' => 'fas fa-users',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'Support & Service',
                'slug' => 'support-service',
                'description' => 'Templates for customer support, service announcements, and help communications',
                'icon' => 'fas fa-headset',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('email_template_categories')->updateOrInsert(
                ['slug' => $category['slug']],
                array_merge($category, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }
    }

    /**
     * Seed email template variables
     */
    private function seedVariables(): void
    {
        $variables = [
            // User variables
            [
                'name' => 'User ID',
                'key' => 'user.id',
                'description' => 'Unique identifier for the user',
                'data_type' => 'number',
                'default_value' => null,
                'is_required' => false,
                'category' => 'user',
                'example_value' => '12345',
            ],
            [
                'name' => 'User Name',
                'key' => 'user.name',
                'description' => 'Full name of the user',
                'data_type' => 'string',
                'default_value' => 'Valued Customer',
                'is_required' => false,
                'category' => 'user',
                'example_value' => 'John Doe',
            ],
            [
                'name' => 'User Email',
                'key' => 'user.email',
                'description' => 'Email address of the user',
                'data_type' => 'string',
                'default_value' => null,
                'is_required' => false,
                'category' => 'user',
                'example_value' => '<EMAIL>',
            ],
            [
                'name' => 'User Phone',
                'key' => 'user.phone',
                'description' => 'Phone number of the user',
                'data_type' => 'string',
                'default_value' => null,
                'is_required' => false,
                'category' => 'user',
                'example_value' => '+971501234567',
            ],

            // Order variables
            [
                'name' => 'Order Number',
                'key' => 'order.order_number',
                'description' => 'Unique order identifier',
                'data_type' => 'string',
                'default_value' => null,
                'is_required' => false,
                'category' => 'order',
                'example_value' => 'INV-2025-0001',
            ],
            [
                'name' => 'Order Total',
                'key' => 'order.total',
                'description' => 'Total order amount',
                'data_type' => 'number',
                'default_value' => '0.00',
                'is_required' => false,
                'category' => 'order',
                'example_value' => '299.99',
            ],
            [
                'name' => 'Order Currency',
                'key' => 'order.currency',
                'description' => 'Order currency code',
                'data_type' => 'string',
                'default_value' => 'AED',
                'is_required' => false,
                'category' => 'order',
                'example_value' => 'AED',
            ],
            [
                'name' => 'Order Status',
                'key' => 'order.fulfillment_status',
                'description' => 'Current order fulfillment status',
                'data_type' => 'string',
                'default_value' => 'pending',
                'is_required' => false,
                'category' => 'order',
                'example_value' => 'shipped',
            ],

            // Vendor variables
            [
                'name' => 'Vendor Name',
                'key' => 'vendor.name',
                'description' => 'Name of the vendor',
                'data_type' => 'string',
                'default_value' => null,
                'is_required' => false,
                'category' => 'vendor',
                'example_value' => 'Health Plus Store',
            ],
            [
                'name' => 'Vendor Contact Name',
                'key' => 'vendor.spoc_name',
                'description' => 'Primary contact person name',
                'data_type' => 'string',
                'default_value' => null,
                'is_required' => false,
                'category' => 'vendor',
                'example_value' => 'Ahmed Al-Rashid',
            ],

            // System variables
            [
                'name' => 'Site Name',
                'key' => 'site.name',
                'description' => 'Name of the website',
                'data_type' => 'string',
                'default_value' => 'Vitamins.ae',
                'is_required' => false,
                'category' => 'system',
                'example_value' => 'Vitamins.ae',
            ],
            [
                'name' => 'Site URL',
                'key' => 'site.url',
                'description' => 'Website URL',
                'data_type' => 'string',
                'default_value' => 'https://vitamins.ae',
                'is_required' => false,
                'category' => 'system',
                'example_value' => 'https://vitamins.ae',
            ],
            [
                'name' => 'Support Email',
                'key' => 'site.support_email',
                'description' => 'Customer support email address',
                'data_type' => 'string',
                'default_value' => '<EMAIL>',
                'is_required' => false,
                'category' => 'system',
                'example_value' => '<EMAIL>',
            ],
            [
                'name' => 'Current Date',
                'key' => 'current.date',
                'description' => 'Current date',
                'data_type' => 'date',
                'default_value' => null,
                'is_required' => false,
                'category' => 'system',
                'example_value' => '2025-01-28',
            ],
        ];

        foreach ($variables as $variable) {
            DB::table('email_template_variables')->updateOrInsert(
                ['key' => $variable['key']],
                array_merge($variable, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }
    }

    /**
     * Seed default email templates
     */
    private function seedDefaultTemplates(): void
    {
        // Get category IDs
        $authCategory = DB::table('email_template_categories')->where('slug', 'authentication-security')->first();
        $orderCategory = DB::table('email_template_categories')->where('slug', 'order-management')->first();
        $vendorCategory = DB::table('email_template_categories')->where('slug', 'vendor-communications')->first();

        $templates = [
            [
                'uuid' => Str::uuid(),
                'name' => 'OTP Verification',
                'slug' => 'otp-verification',
                'subject' => 'Account Verification OTP - {{site.name}}',
                'body_html' => $this->getOtpVerificationHtml(),
                'body_text' => $this->getOtpVerificationText(),
                'category_id' => $authCategory->id,
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'otp', 'site.name']),
                'metadata' => json_encode(['type' => 'authentication', 'priority' => 'high']),
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Password Reset',
                'slug' => 'password-reset',
                'subject' => 'Reset Your Password - {{site.name}}',
                'body_html' => $this->getPasswordResetHtml(),
                'body_text' => $this->getPasswordResetText(),
                'category_id' => $authCategory->id,
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'reset_url', 'site.name']),
                'metadata' => json_encode(['type' => 'authentication', 'priority' => 'high']),
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Order Confirmation',
                'slug' => 'order-confirmation',
                'subject' => 'Order Confirmation #{{order.order_number}} - {{site.name}}',
                'body_html' => $this->getOrderConfirmationHtml(),
                'body_text' => $this->getOrderConfirmationText(),
                'category_id' => $orderCategory->id,
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'order.order_number', 'order.total', 'order.currency', 'site.name']),
                'metadata' => json_encode(['type' => 'order', 'priority' => 'medium']),
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Vendor EOI Approved',
                'slug' => 'vendor-eoi-approved',
                'subject' => 'Your Vendor Application Has Been Approved - {{site.name}}',
                'body_html' => $this->getVendorEoiApprovedHtml(),
                'body_text' => $this->getVendorEoiApprovedText(),
                'category_id' => $vendorCategory->id,
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['vendor.spoc_name', 'site.name']),
                'metadata' => json_encode(['type' => 'vendor', 'priority' => 'high']),
            ],
        ];

        foreach ($templates as $template) {
            DB::table('email_templates')->updateOrInsert(
                ['slug' => $template['slug']],
                array_merge($template, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }
    }

    /**
     * Get OTP verification HTML template
     */
    private function getOtpVerificationHtml(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #d7e7cc; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .otp-code { background-color: #f8f9fa; border: 2px solid #28a745; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Account Verification</h2>
        </div>
        <div class="content">
            <p>Hello {{user.name}},</p>
            <p>Thank you for registering with {{site.name}}. To complete your registration, please use the following OTP:</p>
            <div class="otp-code">{{otp}}</div>
            <p>This OTP will expire in 10 minutes. If you did not request this verification, please ignore this email.</p>
            <p>Best regards,<br>The {{site.name}} Team</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get OTP verification text template
     */
    private function getOtpVerificationText(): string
    {
        return 'Hello {{user.name}},

Thank you for registering with {{site.name}}. To complete your registration, please use the following OTP (One-Time Password):

OTP: {{otp}}

This OTP will expire in 10 minutes. If you did not request this verification, please ignore this email.

Best regards,
The {{site.name}} Team

---
This is an automated email. Please do not reply to this message.';
    }

    /**
     * Get password reset HTML template
     */
    private function getPasswordResetHtml(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #d7e7cc; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .reset-button { background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Password Reset Request</h2>
        </div>
        <div class="content">
            <p>Hello {{user.name}},</p>
            <p>You are receiving this email because we received a password reset request for your account.</p>
            <p style="text-align: center;">
                <a href="{{reset_url}}" class="reset-button">Reset Password</a>
            </p>
            <p>If you did not request a password reset, no further action is required.</p>
            <p>This password reset link will expire in 60 minutes.</p>
            <p>Best regards,<br>The {{site.name}} Team</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get password reset text template
     */
    private function getPasswordResetText(): string
    {
        return 'Hello {{user.name}},

You are receiving this email because we received a password reset request for your account.

To reset your password, please visit: {{reset_url}}

If you did not request a password reset, no further action is required.

This password reset link will expire in 60 minutes.

Best regards,
The {{site.name}} Team

---
This is an automated email. Please do not reply to this message.';
    }

    /**
     * Get order confirmation HTML template
     */
    private function getOrderConfirmationHtml(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #d7e7cc; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .order-details { background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Order Confirmation</h2>
        </div>
        <div class="content">
            <p>Hello {{user.name}},</p>
            <p>Thank you for your order! We have received your order and it is being processed.</p>
            <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{order.order_number}}</p>
                <p><strong>Total Amount:</strong> {{order.currency}} {{order.total}}</p>
            </div>
            <p>You will receive another email once your order has been shipped.</p>
            <p>Best regards,<br>The {{site.name}} Team</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get order confirmation text template
     */
    private function getOrderConfirmationText(): string
    {
        return 'Hello {{user.name}},

Thank you for your order! We have received your order and it is being processed.

Order Details:
- Order Number: {{order.order_number}}
- Total Amount: {{order.currency}} {{order.total}}

You will receive another email once your order has been shipped.

Best regards,
The {{site.name}} Team

---
This is an automated email. Please do not reply to this message.';
    }

    /**
     * Get vendor EOI approved HTML template
     */
    private function getVendorEoiApprovedHtml(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendor Application Approved</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #d7e7cc; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .success-badge { background-color: #28a745; color: white; padding: 10px 20px; border-radius: 20px; display: inline-block; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Vendor Application Status</h2>
        </div>
        <div class="content">
            <p>Dear {{vendor.spoc_name}},</p>
            <div class="success-badge">🎉 APPROVED</div>
            <p>Congratulations! Your Expression of Interest (EOI) has been <strong>approved</strong>!</p>
            <p>We are pleased to inform you that your application has been reviewed and approved by our team. You can now proceed with completing your vendor profile.</p>
            <p>If you have any questions or need further assistance, feel free to contact us at {{site.support_email}}.</p>
            <p>We look forward to working with you!</p>
            <p>Best regards,<br>The {{site.name}} Team</p>
        </div>
        <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get vendor EOI approved text template
     */
    private function getVendorEoiApprovedText(): string
    {
        return 'Dear {{vendor.spoc_name}},

🎉 Congratulations! Your Expression of Interest (EOI) has been APPROVED!

We are pleased to inform you that your application has been reviewed and approved by our team. You can now proceed with completing your vendor profile.

If you have any questions or need further assistance, feel free to contact us at {{site.support_email}}.

We look forward to working with you!

Best regards,
The {{site.name}} Team

---
This is an automated email. Please do not reply to this message.';
    }
}
