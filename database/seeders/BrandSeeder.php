<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some active/approved brands
        Brand::factory()
            ->count(15)
            ->active()
            ->create();

        // Create some pending brands
        Brand::factory()
            ->count(5)
            ->pending()
            ->create();

        // Create some rejected brands
        Brand::factory()
            ->count(3)
            ->rejected()
            ->create();

        // Create comprehensive well-known brands for testing
        $wellKnownBrands = [
            // Technology Brands
            [
                'name_en' => 'Apple',
                'name_ar' => 'أبل',
                'slug' => 'apple',
                'country_of_origin' => 'United States',
                'is_trademark_registered' => true,
                'website' => 'https://www.apple.com',
                'instagram' => 'apple',
                'facebook' => 'apple',
                'brand_owner' => true,
                'manufacturer' => true,
                'skus_on_brand_website' => 150,
                'skus_on_amazon' => 75,
                'skus_on_noon' => 50,
                'skus_on_other_marketplaces' => 100,
                'skus_on_own_website' => 150,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket, Spinneys',
                'sold_in_specialty_stores' => 'iStyle, Sharaf DG, Jumbo Electronics',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'not_required',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Think Different - Premium technology products with innovative design',
                'top_products' => ['iPhone 15 Pro', 'MacBook Air M3', 'iPad Pro'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],
            [
                'name_en' => 'Samsung',
                'name_ar' => 'سامسونغ',
                'slug' => 'samsung',
                'country_of_origin' => 'South Korea',
                'is_trademark_registered' => true,
                'website' => 'https://www.samsung.com',
                'instagram' => 'samsung',
                'facebook' => 'SamsungMobile',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_distributor' => true,
                'skus_on_brand_website' => 200,
                'skus_on_amazon' => 120,
                'skus_on_noon' => 80,
                'skus_on_other_marketplaces' => 150,
                'skus_on_own_website' => 200,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket, Union Coop',
                'sold_in_specialty_stores' => 'Sharaf DG, Emax, Jumbo Electronics',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'not_required',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Innovation and technology leadership across all device categories',
                'top_products' => ['Galaxy S24 Ultra', 'QLED TV', 'Galaxy Watch'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Sports & Fashion Brands
            [
                'name_en' => 'Nike',
                'name_ar' => 'نايك',
                'slug' => 'nike',
                'country_of_origin' => 'United States',
                'is_trademark_registered' => true,
                'website' => 'https://www.nike.com',
                'instagram' => 'nike',
                'facebook' => 'nike',
                'brand_owner' => true,
                'authorized_retailer' => true,
                'skus_on_brand_website' => 500,
                'skus_on_amazon' => 200,
                'skus_on_noon' => 150,
                'skus_on_other_marketplaces' => 300,
                'skus_on_own_website' => 500,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket',
                'sold_in_specialty_stores' => 'Nike Store, Sun & Sand Sports, Go Sport',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'not_required',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Just Do It - Athletic excellence and innovation in sportswear',
                'top_products' => ['Air Jordan', 'Air Max', 'Dri-FIT'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],
            [
                'name_en' => 'Adidas',
                'name_ar' => 'أديداس',
                'slug' => 'adidas',
                'country_of_origin' => 'Germany',
                'is_trademark_registered' => true,
                'website' => 'https://www.adidas.com',
                'instagram' => 'adidas',
                'facebook' => 'adidas',
                'brand_owner' => true,
                'authorized_retailer' => true,
                'skus_on_brand_website' => 450,
                'skus_on_amazon' => 180,
                'skus_on_noon' => 120,
                'skus_on_other_marketplaces' => 250,
                'skus_on_own_website' => 450,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket',
                'sold_in_specialty_stores' => 'Adidas Store, Sun & Sand Sports, Go Sport',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'not_required',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Impossible is Nothing - Performance through sport',
                'top_products' => ['Ultraboost', 'Stan Smith', 'Predator'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Health & Beauty Brands
            [
                'name_en' => 'L\'Oréal',
                'name_ar' => 'لوريال',
                'slug' => 'loreal',
                'country_of_origin' => 'France',
                'is_trademark_registered' => true,
                'website' => 'https://www.loreal.com',
                'instagram' => 'lorealparis',
                'facebook' => 'lorealparis',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_distributor' => true,
                'skus_on_brand_website' => 300,
                'skus_on_amazon' => 150,
                'skus_on_noon' => 100,
                'skus_on_other_marketplaces' => 200,
                'skus_on_own_website' => 300,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket, Spinneys',
                'sold_in_pharmacies' => 'Aster Pharmacy, Life Pharmacy, Boots',
                'sold_in_specialty_stores' => 'Sephora, Faces, Paris Gallery',
                'mohap_registration' => 'yes',
                'dubai_municipality_registration' => 'yes',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Because You\'re Worth It - Beauty and cosmetics innovation',
                'top_products' => ['Revitalift', 'Excellence Hair Color', 'True Match Foundation'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Food & Beverage Brands
            [
                'name_en' => 'Nestlé',
                'name_ar' => 'نستله',
                'slug' => 'nestle',
                'country_of_origin' => 'Switzerland',
                'is_trademark_registered' => true,
                'website' => 'https://www.nestle.com',
                'instagram' => 'nestle',
                'facebook' => 'nestle',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_distributor' => true,
                'skus_on_brand_website' => 400,
                'skus_on_amazon' => 250,
                'skus_on_noon' => 200,
                'skus_on_other_marketplaces' => 300,
                'skus_on_own_website' => 400,
                'sold_in_hypermarkets' => 'Carrefour, LuLu Hypermarket, Union Coop, Spinneys',
                'sold_in_specialty_stores' => 'Organic Foods & Cafe, Waitrose, Choithrams',
                'mohap_registration' => 'yes',
                'dubai_municipality_registration' => 'yes',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Good Food, Good Life - Nutrition, health and wellness',
                'top_products' => ['KitKat', 'Nescafé', 'Maggi'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Automotive Brands
            [
                'name_en' => 'Toyota',
                'name_ar' => 'تويوتا',
                'slug' => 'toyota',
                'country_of_origin' => 'Japan',
                'is_trademark_registered' => true,
                'website' => 'https://www.toyota.com',
                'instagram' => 'toyota',
                'facebook' => 'toyota',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_distributor' => true,
                'skus_on_brand_website' => 50,
                'skus_on_amazon' => 0,
                'skus_on_noon' => 0,
                'skus_on_other_marketplaces' => 25,
                'skus_on_own_website' => 50,
                'sold_in_specialty_stores' => 'Al-Futtaim Toyota, Toyota Showrooms',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'yes',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Let\'s Go Places - Reliability and innovation in mobility',
                'top_products' => ['Camry', 'Prius', 'Land Cruiser'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Home & Garden Brands
            [
                'name_en' => 'IKEA',
                'name_ar' => 'ايكيا',
                'slug' => 'ikea',
                'country_of_origin' => 'Sweden',
                'is_trademark_registered' => true,
                'website' => 'https://www.ikea.com',
                'instagram' => 'ikea',
                'facebook' => 'IKEA',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_retailer' => true,
                'skus_on_brand_website' => 1000,
                'skus_on_amazon' => 0,
                'skus_on_noon' => 0,
                'skus_on_other_marketplaces' => 50,
                'skus_on_own_website' => 1000,
                'sold_in_specialty_stores' => 'IKEA Store Dubai, IKEA Store Abu Dhabi',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'yes',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Democratic Design - Affordable furniture and home solutions',
                'top_products' => ['BILLY Bookcase', 'MALM Bed', 'POÄNG Chair'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],

            // Luxury Brands
            [
                'name_en' => 'Rolex',
                'name_ar' => 'رولكس',
                'slug' => 'rolex',
                'country_of_origin' => 'Switzerland',
                'is_trademark_registered' => true,
                'website' => 'https://www.rolex.com',
                'instagram' => 'rolex',
                'facebook' => 'rolex',
                'brand_owner' => true,
                'manufacturer' => true,
                'authorized_retailer' => true,
                'skus_on_brand_website' => 100,
                'skus_on_amazon' => 0,
                'skus_on_noon' => 0,
                'skus_on_other_marketplaces' => 0,
                'skus_on_own_website' => 100,
                'sold_in_specialty_stores' => 'Ahmed Seddiqi & Sons, Rivoli Group, Damas',
                'mohap_registration' => 'not_required',
                'dubai_municipality_registration' => 'not_required',
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'A Crown for Every Achievement - Luxury Swiss timepieces',
                'top_products' => ['Submariner', 'Daytona', 'GMT-Master'],
                'logo' => 'uploads/e7e7a1ca-352d-428a-acf9-076bcb400f4f.png',
            ],
        ];

        foreach ($wellKnownBrands as $brandData) {
            Brand::create($brandData);
        }
    }
}
