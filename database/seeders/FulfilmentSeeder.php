<?php

namespace Database\Seeders;

use App\Models\Fulfilment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FulfilmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fulfilments = [
            [
                'name_en' => 'Vendor Fulfilled',
                'name_ar' => 'تنفيذ بواسطة البائع',
                'description_en' => 'The vendor handles storage, packaging, and shipping.',
                'description_ar' => 'البائع مسؤول عن التخزين والتغليف والشحن.',
                'is_active' => true,
            ],
            [
                'name_en' => 'Platform Fulfilled (FBA)',
                'name_ar' => 'تنفيذ بواسطة المنصة (مثل FBA)',
                'description_en' => 'The platform takes care of fulfillment on behalf of the vendor.',
                'description_ar' => 'المنصة تتولى التنفيذ نيابة عن البائع.',
                'is_active' => true,
            ],
            [
                'name_en' => 'Hybrid Fulfilment',
                'name_ar' => 'تنفيذ مختلط',
                'description_en' => 'A mix of vendor and platform fulfillment, based on availability or setup.',
                'description_ar' => 'تنفيذ مشترك من قبل البائع والمنصة حسب التوفر.',
                'is_active' => true,
            ],
            [
                'name_en' => 'Dropshipping',
                'name_ar' => 'دروبشيبينغ',
                'description_en' => 'A third-party supplier ships the product directly to the customer.',
                'description_ar' => 'يتم شحن المنتج مباشرة من قبل مورد خارجي.',
                'is_active' => true,
            ],
            [
                'name_en' => 'Pickup Only',
                'name_ar' => 'استلام فقط',
                'description_en' => 'Customer must pick up the product. No delivery is provided.',
                'description_ar' => 'يجب على العميل استلام المنتج بنفسه. لا يوجد توصيل.',
                'is_active' => true,
            ],
        ];

        foreach ($fulfilments as $fulfilment) {
            Fulfilment::create($fulfilment);
        }
    }
}
