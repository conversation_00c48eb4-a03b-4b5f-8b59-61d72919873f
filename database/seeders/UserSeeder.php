<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '01617703137',
            'password' => Hash::make('password'),
            'is_verified' => true,
        ]);
        $admin->assignRole('admin');

        $vendor = User::create([
            'name' => 'Vendor User',
            'email' => '<EMAIL>',
            'phone' => '01617703138',
            'password' => Hash::make('password'),
            'is_verified' => true,
        ]);
        $vendor->assignRole('vendor');

        $delivery = User::create([
            'name' => 'Delivery User',
            'email' => '<EMAIL>',
            'phone' => '01617703139',
            'password' => Hash::make('password'),
            'is_verified' => true,
        ]);
        $delivery->assignRole('delivery_partner');

        $user = User::create([
            'name' => 'Normal User',
            'email' => '<EMAIL>',
            'phone' => '01617703140',
            'password' => Hash::make('password'),
            'is_verified' => true,
        ]);
        $user->assignRole('user');
    }
}
