<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductAttribute;

class AttributeSeeder extends Seeder
{
    public function run(): void
    {
        $attributes = [
            [
                'name' => 'Flavor',
                'name_ar' => 'النكهة',
                'values' => [
                    ['value' => 'Vanilla', 'value_ar' => 'فانيليا'],
                    ['value' => 'Chocolate', 'value_ar' => 'شوكولاتة'],
                    ['value' => 'Strawberry', 'value_ar' => 'فراولة'],
                ],
            ],
            [
                'name' => 'Dosage',
                'name_ar' => 'الجرعة',
                'values' => [
                    ['value' => '250mg', 'value_ar' => '250 ملغ'],
                    ['value' => '500mg', 'value_ar' => '500 ملغ'],
                    ['value' => '1000mg', 'value_ar' => '1000 ملغ'],
                ],
            ],
            [
                'name' => 'Form',
                'name_ar' => 'الشكل',
                'values' => [
                    ['value' => 'Tablet', 'value_ar' => 'قرص'],
                    ['value' => 'Capsule', 'value_ar' => 'كبسولة'],
                    ['value' => 'Liquid', 'value_ar' => 'سائل'],
                ],
            ],
            [
                'name' => 'Skin Type',
                'name_ar' => 'نوع البشرة',
                'values' => [
                    ['value' => 'Oily', 'value_ar' => 'دهنية'],
                    ['value' => 'Dry', 'value_ar' => 'جافة'],
                    ['value' => 'Combination', 'value_ar' => 'مختلطة'],
                ],
            ],
            [
                'name' => 'Gender',
                'name_ar' => 'الجنس',
                'values' => [
                    ['value' => 'Male', 'value_ar' => 'ذكر'],
                    ['value' => 'Female', 'value_ar' => 'أنثى'],
                    ['value' => 'Unisex', 'value_ar' => 'للجنسين'],
                ],
            ],
            [
                'name' => 'Age Group',
                'name_ar' => 'الفئة العمرية',
                'values' => [
                    ['value' => 'Adult', 'value_ar' => 'بالغ'],
                    ['value' => 'Teen', 'value_ar' => 'مراهق'],
                    ['value' => 'Child', 'value_ar' => 'طفل'],
                ],
            ],
            [
                'name' => 'Packaging Size',
                'name_ar' => 'حجم العبوة',
                'values' => [
                    ['value' => '100ml', 'value_ar' => '100 مل'],
                    ['value' => '250ml', 'value_ar' => '250 مل'],
                    ['value' => '500ml', 'value_ar' => '500 مل'],
                ],
            ],
        ];

        foreach ($attributes as $attributeData) {
            $attribute = ProductAttribute::create([
                'name' => $attributeData['name'],
                'name_ar' => $attributeData['name_ar'],
            ]);

            foreach ($attributeData['values'] as $valueData) {
                $attribute->values()->create([
                    'value' => $valueData['value'],
                    'value_ar' => $valueData['value_ar'],
                ]);
            }
        }
    }
}
