<?php

namespace Database\Seeders;

use App\Models\Banner;
use App\Models\BannerItem;
use App\Models\User;
use Illuminate\Database\Seeder;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user or create a default user


        // Create main banner
        $banner = Banner::create([
            'user_id' => 1,
            'title' => 'Main Homepage Banner',
            'type' => 'main-homepage-banner',
            'description' => 'Main banner displayed on the homepage',
            'is_active' => true,
        ]);

        // Media paths provided
        $mediaPaths = [
            'uploads/bb6817b2-4732-4755-936f-76342fe1f6e1.png',
            'uploads/d3e82924-9a8e-4b7e-87d9-c29ed91943fb.png',
        ];

        // Create banner items with the provided media paths
        foreach ($mediaPaths as $index => $mediaPath) {
            BannerItem::create([
                'banner_id' => $banner->id,
                'user_id' => 1,
                'title_en' => 'Banner Item ' . ($index + 1),
                'title_ar' => 'عنصر البانر ' . ($index + 1),
                'media_path' => $mediaPath,
                'link_url' => '#',
                'target' => '_self',
                'alt_text' => 'Banner image ' . ($index + 1),
                'position' => $index + 1,
                'is_active' => true,
            ]);
        }

        // Create a secondary banner with additional items
        $secondaryBanner = Banner::create([
            'user_id' => 1,
            'title' => 'Promotional Banner',
            'type' => 'promotional-banner',
            'description' => 'Secondary promotional banner',
            'is_active' => true,
        ]);

        // Create additional banner items alternating between the two media paths
        for ($i = 0; $i < 8; $i++) {
            $mediaPath = $mediaPaths[$i % 2]; // Alternate between the two paths

            BannerItem::create([
                'banner_id' => $secondaryBanner->id,
                'user_id' => 1,
                'title_en' => 'Promo Item ' . ($i + 1),
                'title_ar' => 'عنصر ترويجي ' . ($i + 1),
                'media_path' => $mediaPath,
                'link_url' => '/products',
                'target' => '_self',
                'alt_text' => 'Promotional banner image ' . ($i + 1),
                'position' => $i + 1,
                'is_active' => true,
            ]);
        }
    }
}
