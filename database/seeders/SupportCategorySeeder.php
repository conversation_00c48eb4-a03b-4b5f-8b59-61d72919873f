<?php

namespace Database\Seeders;

use App\Models\SupportCategory;
use App\Models\User;
use Illuminate\Database\Seeder;

class SupportCategorySeeder extends Seeder
{
    public function run(): void
    {
        // Get or create an admin user for the categories
        $adminUser = null;

        // Try to get an admin user if the role exists
        try {
            $adminUser = User::role('admin')->first();
        } catch (\Exception $e) {
            // Role doesn't exist, continue without role check
        }

        // If no admin user exists, create a default system user
        if (!$adminUser) {
            $adminUser = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'System Admin',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );
        }

        $categories = [
            // General Platform Categories
            [
                'name_en' => 'Account & Profile',
                'name_ar' => 'الحساب والملف الشخصي',
                'status' => 'active'
            ],
            [
                'name_en' => 'Login & Authentication',
                'name_ar' => 'تسجيل الدخول والمصادقة',
                'status' => 'active'
            ],
            [
                'name_en' => 'Payment & Billing',
                'name_ar' => 'الدفع والفواتير',
                'status' => 'active'
            ],
            [
                'name_en' => 'Technical Issues',
                'name_ar' => 'المشاكل التقنية',
                'status' => 'active'
            ],

            // Order & Transaction Categories
            [
                'name_en' => 'Order Management',
                'name_ar' => 'إدارة الطلبات',
                'status' => 'active'
            ],
            [
                'name_en' => 'Refunds & Returns',
                'name_ar' => 'المبالغ المستردة والإرجاع',
                'status' => 'active'
            ],
            [
                'name_en' => 'Shipping & Delivery',
                'name_ar' => 'الشحن والتوصيل',
                'status' => 'active'
            ],
            [
                'name_en' => 'Order Cancellation',
                'name_ar' => 'إلغاء الطلب',
                'status' => 'active'
            ],

            // Product & Vendor Categories
            [
                'name_en' => 'Product Quality',
                'name_ar' => 'جودة المنتج',
                'status' => 'active'
            ],
            [
                'name_en' => 'Product Information',
                'name_ar' => 'معلومات المنتج',
                'status' => 'active'
            ],
            [
                'name_en' => 'Vendor Communication',
                'name_ar' => 'التواصل مع البائع',
                'status' => 'active'
            ],
            [
                'name_en' => 'Product Availability',
                'name_ar' => 'توفر المنتج',
                'status' => 'active'
            ],

            // Marketplace Specific Categories
            [
                'name_en' => 'Vendor Onboarding',
                'name_ar' => 'تسجيل البائع',
                'status' => 'active'
            ],
            [
                'name_en' => 'Commission & Fees',
                'name_ar' => 'العمولة والرسوم',
                'status' => 'active'
            ],
            [
                'name_en' => 'Marketplace Policies',
                'name_ar' => 'سياسات السوق',
                'status' => 'active'
            ],
            [
                'name_en' => 'Vendor Performance',
                'name_ar' => 'أداء البائع',
                'status' => 'active'
            ],

            // Customer Service Categories
            [
                'name_en' => 'General Inquiry',
                'name_ar' => 'استفسار عام',
                'status' => 'active'
            ],
            [
                'name_en' => 'Complaint Resolution',
                'name_ar' => 'حل الشكاوى',
                'status' => 'active'
            ],
            [
                'name_en' => 'Feature Request',
                'name_ar' => 'طلب ميزة',
                'status' => 'active'
            ],
            [
                'name_en' => 'Feedback & Suggestions',
                'name_ar' => 'التعليقات والاقتراحات',
                'status' => 'active'
            ],

            // Third-Party Logistics (TPL) Categories
            [
                'name_en' => 'Logistics & Warehousing',
                'name_ar' => 'اللوجستيات والتخزين',
                'status' => 'active'
            ],
            [
                'name_en' => 'Delivery Tracking',
                'name_ar' => 'تتبع التوصيل',
                'status' => 'active'
            ],
            [
                'name_en' => 'Package Handling',
                'name_ar' => 'التعامل مع الطرود',
                'status' => 'active'
            ],
            [
                'name_en' => 'Delivery Schedule',
                'name_ar' => 'جدول التوصيل',
                'status' => 'active'
            ],

            // Security & Compliance Categories
            [
                'name_en' => 'Security Concerns',
                'name_ar' => 'مخاوف الأمان',
                'status' => 'active'
            ],
            [
                'name_en' => 'Privacy Issues',
                'name_ar' => 'قضايا الخصوصية',
                'status' => 'active'
            ],
            [
                'name_en' => 'Compliance & Legal',
                'name_ar' => 'الامتثال والقانونية',
                'status' => 'active'
            ],
            [
                'name_en' => 'Fraud Prevention',
                'name_ar' => 'منع الاحتيال',
                'status' => 'active'
            ],

            // Mobile App Specific Categories
            [
                'name_en' => 'Mobile App Issues',
                'name_ar' => 'مشاكل التطبيق المحمول',
                'status' => 'active'
            ],
            [
                'name_en' => 'App Performance',
                'name_ar' => 'أداء التطبيق',
                'status' => 'active'
            ],
            [
                'name_en' => 'Push Notifications',
                'name_ar' => 'الإشعارات الفورية',
                'status' => 'active'
            ],

            // Business & Partnership Categories
            [
                'name_en' => 'Business Partnership',
                'name_ar' => 'الشراكة التجارية',
                'status' => 'active'
            ],
            [
                'name_en' => 'Bulk Orders',
                'name_ar' => 'الطلبات المجمعة',
                'status' => 'active'
            ],
            [
                'name_en' => 'Corporate Accounts',
                'name_ar' => 'الحسابات المؤسسية',
                'status' => 'active'
            ],

            // Regional & Localization Categories
            [
                'name_en' => 'Language Support',
                'name_ar' => 'دعم اللغة',
                'status' => 'active'
            ],
            [
                'name_en' => 'Regional Services',
                'name_ar' => 'الخدمات الإقليمية',
                'status' => 'active'
            ],
            [
                'name_en' => 'Currency & Exchange',
                'name_ar' => 'العملة والصرف',
                'status' => 'active'
            ],

            // Emergency & Urgent Categories
            [
                'name_en' => 'Urgent Issues',
                'name_ar' => 'القضايا العاجلة',
                'status' => 'active'
            ],
            [
                'name_en' => 'Emergency Support',
                'name_ar' => 'الدعم الطارئ',
                'status' => 'active'
            ],

            // Inactive/Legacy Categories (for testing)
            [
                'name_en' => 'Legacy Category',
                'name_ar' => 'فئة قديمة',
                'status' => 'inactive'
            ],
            [
                'name_en' => 'Deprecated Feature',
                'name_ar' => 'ميزة مهجورة',
                'status' => 'inactive'
            ]
        ];

        foreach ($categories as $category) {
            SupportCategory::create([
                'user_id' => $adminUser->id,
                'name_en' => $category['name_en'],
                'name_ar' => $category['name_ar'],
                'status' => $category['status']
            ]);
        }
    }
}
