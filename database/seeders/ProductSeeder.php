<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\ProductClass;
use App\Models\Brand;
use App\Models\Dropdown;
use App\Models\DropdownOption;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, ensure we have the required data
        $this->ensureRequiredData();
        
        // Create realistic UAE marketplace products
        $this->createHealthSupplementProducts();
        $this->createBeautyProducts();
        $this->createFoodProducts();
        $this->createSportsNutritionProducts();
        $this->createWeightManagementProducts();
    }

    private function ensureRequiredData(): void
    {
        // Create vendors if they don't exist
        if (Vendor::count() === 0) {
            $this->createVendors();
        } else {
            // Ensure specific vendors exist
            $this->ensureSpecificVendorsExist();
        }
    }

    /**
     * Get dropdown option ID by dropdown slug and option value with error handling
     */
    private function getDropdownOptionId(string $dropdownSlug, string $optionValue): ?int
    {
        $dropdown = Dropdown::where('slug', $dropdownSlug)->first();
        if (!$dropdown) {
            // Use echo instead of $this->command->warn() for broader compatibility
            if (app()->runningInConsole()) {
                echo "Warning: Dropdown with slug '{$dropdownSlug}' not found. Please ensure DropdownSeeder has been run.\n";
            }
            return null;
        }

        $option = DropdownOption::where('dropdown_id', $dropdown->id)
            ->where('value_en', $optionValue)
            ->first();

        if (!$option) {
            if (app()->runningInConsole()) {
                echo "Warning: Dropdown option '{$optionValue}' not found in dropdown '{$dropdownSlug}'. Please check DropdownSeeder data.\n";
            }
            return null;
        }

        return $option->id;
    }

    /**
     * Get fallback dropdown option ID for critical fields
     */
    private function getFallbackDropdownOptionId(string $dropdownSlug): ?int
    {
        $dropdown = Dropdown::where('slug', $dropdownSlug)->first();
        if (!$dropdown) {
            return null;
        }

        // Get the first available option as fallback
        $option = DropdownOption::where('dropdown_id', $dropdown->id)->first();
        return $option ? $option->id : null;
    }

    /**
     * Get appropriate user group IDs for all product categories
     */
    private function getAllUserGroups(): array
    {
        static $cachedUserGroups = null;

        if ($cachedUserGroups === null) {
            $cachedUserGroups = [
                // Core user groups
                'kids' => $this->getDropdownOptionId('user-group', 'Kids'),
                'teens' => $this->getDropdownOptionId('user-group', 'Teens'),
                'men' => $this->getDropdownOptionId('user-group', 'Men'),
                'women' => $this->getDropdownOptionId('user-group', 'Women'),
                'unisex' => $this->getDropdownOptionId('user-group', 'UniSex'),
                'senior_adults' => $this->getDropdownOptionId('user-group', 'Senior Adults'),
                'menopausal' => $this->getDropdownOptionId('user-group', 'Menopausal'),
                'prenatal' => $this->getDropdownOptionId('user-group', 'Prenatal'),
                'fitness_freeks' => $this->getDropdownOptionId('user-group', 'Fitness Freeks'),
                'weight_watchers' => $this->getDropdownOptionId('user-group', 'Weight Watchers'),
                'keto' => $this->getDropdownOptionId('user-group', 'Keto'),
            ];
        }

        return $cachedUserGroups;
    }

    /**
     * Get appropriate user group IDs for weight management products
     */
    private function getWeightManagementUserGroups(): array
    {
        $allGroups = $this->getAllUserGroups();
        return [
            'weight_watchers' => $allGroups['weight_watchers'],
            'fitness_freeks' => $allGroups['fitness_freeks'],
            'unisex' => $allGroups['unisex'],
        ];
    }

    /**
     * Get user group ID with fallback to UniSex
     */
    private function getUserGroupWithFallback(?int $preferredGroupId): int
    {
        $allGroups = $this->getAllUserGroups();

        if ($preferredGroupId && $preferredGroupId > 0) {
            return $preferredGroupId;
        }

        // Fallback to UniSex
        return $allGroups['unisex'] ?? 9; // Hard-coded fallback if dropdown lookup fails
    }

    /**
     * Generate unique model number for products
     */
    private function generateModelNumber(string $categoryCode): string
    {
        static $usedModelNumbers = [];

        $year = date('Y');
        $maxAttempts = 100;
        $attempt = 0;

        do {
            $randomDigits = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
            $modelNumber = "{$categoryCode}-{$randomDigits}-{$year}";
            $attempt++;
        } while (in_array($modelNumber, $usedModelNumbers) && $attempt < $maxAttempts);

        if ($attempt >= $maxAttempts) {
            // Fallback with timestamp if we can't generate unique number
            $modelNumber = "{$categoryCode}-" . time() . "-{$year}";
        }

        $usedModelNumbers[] = $modelNumber;
        return $modelNumber;
    }

    /**
     * Create default product media for a product
     */
    private function createProductMedia(int $productId): void
    {
        \App\Models\ProductMedia::create([
            'product_id' => $productId,
            'type' => 'image',
            'path' => 'uploads/fdbadcf8-1ea5-44b5-8a39-f71c0d19196a.png',
            'title' => 'Product Image',
            'alt_text' => 'Product Image',
            'position' => 1,
            'is_primary' => true,
        ]);
    }

    /**
     * Get ProductClass ID by category, subcategory, and class code
     */
    private function getProductClassId(int $categoryId, int $subCategoryId, string $classCode): ?int
    {
        $productClass = ProductClass::where('category_id', $categoryId)
            ->where('sub_category_id', $subCategoryId)
            ->where('code', $classCode)
            ->where('parent_id', null) // Only get parent classes, not subclasses
            ->first();

        return $productClass ? $productClass->id : null;
    }

    /**
     * Get ProductClass subclass ID by parent class ID and subclass name
     */
    private function getProductSubClassId(int $parentClassId, string $subClassName): ?int
    {
        $subClass = ProductClass::where('parent_id', $parentClassId)
            ->where('name_en', $subClassName)
            ->first();

        return $subClass ? $subClass->id : null;
    }

    /**
     * Get default dropdown option IDs for fields that use dropdown references
     */
    private function getDefaultDropdownOptions(): array
    {
        // Cache dropdown option IDs to avoid repeated database queries
        static $cachedOptions = null;

        if ($cachedOptions === null) {
            $cachedOptions = [
                'storage_conditions' => [
                    'Store in cool, dry place' => $this->getDropdownOptionId('storage-conditions', 'Store in Cool, Dry Place'),
                    'Refrigerate after opening' => $this->getDropdownOptionId('storage-conditions', 'Refrigerate After Opening'),
                    'Store at room temperature' => $this->getDropdownOptionId('storage-conditions', 'Store at Room Temperature'),
                    'Store in cool place, avoid direct sunlight' => $this->getDropdownOptionId('storage-conditions', 'Keep Away from Direct Sunlight'),
                    'Store in cool, dry place in airtight container' => $this->getDropdownOptionId('storage-conditions', 'Store in Cool, Dry Place'),
                    'Store in freezer' => $this->getDropdownOptionId('storage-conditions', 'Store in Freezer'),
                    'Store below 25°C' => $this->getDropdownOptionId('storage-conditions', 'Store Below 25°C'),
                    'Store in original container' => $this->getDropdownOptionId('storage-conditions', 'Store in Original Container'),
                ],
                'country_of_origin' => [
                    // Map commonly used country names to their exact DropdownSeeder values
                    'USA' => $this->getDropdownOptionId('country-of-origin', 'United States'),
                    'Norway' => $this->getDropdownOptionId('country-of-origin', 'Norway'),
                    'South Korea' => $this->getDropdownOptionId('country-of-origin', 'South Korea'),
                    'Australia' => $this->getDropdownOptionId('country-of-origin', 'Australia'),
                    'India' => $this->getDropdownOptionId('country-of-origin', 'India'),
                    'China' => $this->getDropdownOptionId('country-of-origin', 'China'),
                    'Germany' => $this->getDropdownOptionId('country-of-origin', 'Germany'),
                    'France' => $this->getDropdownOptionId('country-of-origin', 'France'),
                    'United Kingdom' => $this->getDropdownOptionId('country-of-origin', 'United Kingdom'),
                    'Italy' => $this->getDropdownOptionId('country-of-origin', 'Italy'),
                    'Switzerland' => $this->getDropdownOptionId('country-of-origin', 'Switzerland'),
                    'Japan' => $this->getDropdownOptionId('country-of-origin', 'Japan'),
                    'Canada' => $this->getDropdownOptionId('country-of-origin', 'Canada'),
                    'Netherlands' => $this->getDropdownOptionId('country-of-origin', 'Netherlands'),
                    'Belgium' => $this->getDropdownOptionId('country-of-origin', 'Belgium'),
                    'Denmark' => $this->getDropdownOptionId('country-of-origin', 'Denmark'),
                    'Sweden' => $this->getDropdownOptionId('country-of-origin', 'Sweden'),
                    'Turkey' => $this->getDropdownOptionId('country-of-origin', 'Turkey'),
                ],
                'is_returnable' => [
                    'default' => $this->getDropdownOptionId('is-returnable', 'Yes Within 7 Days'),
                    'non_returnable' => $this->getDropdownOptionId('is-returnable', 'Non Returnable'),
                    'conditional' => $this->getDropdownOptionId('is-returnable', 'Conditional'),
                ],
                'warranty' => [
                    'no_warranty' => $this->getDropdownOptionId('warranty', 'No Warranty'),
                    '6_months' => $this->getDropdownOptionId('warranty', '6 Months'),
                    '1_year' => $this->getDropdownOptionId('warranty', '1 Year'),
                    '2_years' => $this->getDropdownOptionId('warranty', '2 Years'),
                    'manufacturer' => $this->getDropdownOptionId('warranty', 'Manufacturer Warranty'),
                ],
                // New required dropdown options
                'net_weight_unit' => $this->getFallbackDropdownOptionId('net-weight-unit'),
                'formulation' => $this->getFallbackDropdownOptionId('formulation'),
                'flavour' => $this->getFallbackDropdownOptionId('flavour'),
            ];
        }

        return $cachedOptions;
    }

    /**
     * Generate realistic key ingredients for different product types
     */
    private function generateKeyIngredients(string $productType): string
    {
        $ingredients = [
            'vitamin' => [
                'Vitamin D3 (Cholecalciferol), Microcrystalline Cellulose, Magnesium Stearate, Silicon Dioxide',
                'Ascorbic Acid (Vitamin C), Citrus Bioflavonoids, Rose Hips Extract, Acerola Cherry Extract',
                'Mixed Tocopherols (Vitamin E), Sunflower Oil, Gelatin, Glycerin, Purified Water',
                'Thiamine Mononitrate (B1), Riboflavin (B2), Niacinamide (B3), Pyridoxine HCl (B6), Cyanocobalamin (B12)',
            ],
            'mineral' => [
                'Magnesium Glycinate, Vegetable Cellulose, Magnesium Stearate, Silicon Dioxide',
                'Calcium Carbonate, Vitamin D3, Magnesium Oxide, Zinc Gluconate',
                'Iron Bisglycinate, Vitamin C, Folic Acid, Vitamin B12',
                'Zinc Picolinate, Copper Gluconate, Manganese Sulfate, Chromium Picolinate',
            ],
            'herbal' => [
                'Turmeric Root Extract (95% Curcuminoids), Black Pepper Extract (Piperine), Vegetable Cellulose',
                'Ashwagandha Root Extract (5% Withanolides), Organic Ashwagandha Root Powder, Vegetable Capsule',
                'Ginkgo Biloba Leaf Extract (24% Flavone Glycosides), Microcrystalline Cellulose, Magnesium Stearate',
                'Milk Thistle Seed Extract (80% Silymarin), Dandelion Root Extract, Artichoke Leaf Extract',
            ],
            'omega' => [
                'Fish Oil Concentrate, EPA (Eicosapentaenoic Acid), DHA (Docosahexaenoic Acid), Vitamin E (Mixed Tocopherols)',
                'Algae Oil, DHA from Algae, EPA from Algae, Sunflower Oil, Natural Lemon Flavor',
                'Krill Oil, Phospholipids, Astaxanthin, EPA, DHA, Softgel Capsule (Gelatin, Glycerin)',
            ],
            'probiotic' => [
                'Lactobacillus acidophilus, Bifidobacterium lactis, Lactobacillus plantarum, Lactobacillus casei, Prebiotic Fiber (FOS)',
                'Multi-strain Probiotic Blend (50 Billion CFU), Lactobacillus rhamnosus, Bifidobacterium longum, Vegetable Capsule',
            ],
            'beauty' => [
                'Hyaluronic Acid, Sodium Hyaluronate, Glycerin, Purified Water, Phenoxyethanol',
                'L-Ascorbic Acid (Vitamin C), Hyaluronic Acid, Vitamin E, Ferulic Acid, Purified Water',
                'Retinol, Squalane, Hyaluronic Acid, Vitamin E, Jojoba Oil, Rosehip Oil',
                'Niacinamide (10%), Zinc PCA, Hyaluronic Acid, Tasmanian Pepperberry Extract',
            ],
            'food' => [
                'Organic Quinoa Seeds, Natural Saponins Removed, No Additives or Preservatives',
                'Organic Coconut Oil, Cold-Pressed, Unrefined, No Chemicals or Additives',
                'Raw Manuka Honey, UMF 15+, Natural Enzymes, Methylglyoxal (MGO 514+)',
                'Himalayan Pink Salt, Natural Minerals, No Anti-Caking Agents, Unrefined',
            ],
        ];

        $typeKey = strtolower($productType);
        if (isset($ingredients[$typeKey])) {
            return $ingredients[$typeKey][array_rand($ingredients[$typeKey])];
        }

        // Default fallback
        return 'Natural ingredients, No artificial colors or flavors, Gluten-free, Non-GMO';
    }

    /**
     * Generate realistic usage instructions in English
     */
    private function generateUsageInstructionsEn(string $productType): string
    {
        $instructions = [
            'vitamin' => [
                'Take 1 capsule daily with food or as directed by your healthcare professional. Do not exceed recommended dosage.',
                'Adults: Take 1-2 tablets daily with meals. Children over 12: Take 1 tablet daily. Consult your doctor before use.',
                'Take 1 softgel daily with a meal containing fat for optimal absorption. Store in a cool, dry place.',
                'Dissolve 1 tablet in 200ml of water. Take once daily, preferably in the morning on an empty stomach.',
            ],
            'mineral' => [
                'Take 1-2 capsules daily with food to enhance absorption and minimize stomach upset. Best taken with vitamin D.',
                'Adults: Take 1 tablet twice daily with meals. Do not take with dairy products or calcium supplements.',
                'Take 1 capsule daily between meals for optimal absorption. May be taken with vitamin C to enhance uptake.',
            ],
            'herbal' => [
                'Take 1-2 capsules daily with water, preferably with meals. Use consistently for 4-6 weeks for best results.',
                'Adults: Take 1 capsule twice daily. Take with food if stomach sensitivity occurs. Discontinue if adverse reactions occur.',
                'Take 1-3 capsules daily as needed. Start with lowest dose and increase gradually. Not recommended during pregnancy.',
            ],
            'omega' => [
                'Take 1-2 softgels daily with meals. Refrigerate after opening to maintain freshness and potency.',
                'Adults: Take 1 softgel daily with food. Children: Consult pediatrician for appropriate dosage.',
                'Take 2 softgels daily with meals or as directed by healthcare provider. Do not exceed 3g daily without medical supervision.',
            ],
            'probiotic' => [
                'Take 1 capsule daily on an empty stomach, 30 minutes before meals. Refrigerate to maintain potency.',
                'Adults: Take 1-2 capsules daily. Take 2 hours before or after antibiotics. Keep refrigerated.',
                'Take 1 capsule daily with or without food. Best taken at the same time each day for consistency.',
            ],
            'beauty' => [
                'Apply 2-3 drops to clean, dry skin. Gently pat until absorbed. Use morning and evening. Follow with moisturizer.',
                'Apply thin layer to face and neck after cleansing. Use only at night. Start 2-3 times per week, increase gradually.',
                'Apply 3-5 drops to clean skin. Massage gently until absorbed. Use daily morning and evening.',
                'Apply small amount to affected areas twice daily. Avoid eye area. Use sunscreen during day when using this product.',
            ],
            'food' => [
                'Use as desired in cooking, baking, or as a natural sweetener. Store in cool, dry place away from direct sunlight.',
                'Rinse before cooking. Use 1 cup quinoa to 2 cups water. Bring to boil, reduce heat, simmer 15 minutes.',
                'Use for cooking, baking, or skincare. Melts at 76°F. Store at room temperature in original container.',
                'Use as table salt or in cooking. Start with small amounts as flavor is more intense than regular salt.',
            ],
        ];

        $typeKey = strtolower($productType);
        if (isset($instructions[$typeKey])) {
            return $instructions[$typeKey][array_rand($instructions[$typeKey])];
        }

        return 'Follow package directions or consult healthcare professional for proper usage. Keep out of reach of children.';
    }

    /**
     * Generate realistic usage instructions in Arabic
     */
    private function generateUsageInstructionsAr(string $productType): string
    {
        $instructions = [
            'vitamin' => [
                'تناول كبسولة واحدة يومياً مع الطعام أو حسب توجيهات أخصائي الرعاية الصحية. لا تتجاوز الجرعة الموصى بها.',
                'البالغون: تناول 1-2 قرص يومياً مع الوجبات. الأطفال فوق 12 سنة: تناول قرص واحد يومياً. استشر طبيبك قبل الاستخدام.',
                'تناول كبسولة جيلاتينية واحدة يومياً مع وجبة تحتوي على دهون للامتصاص الأمثل. احفظ في مكان بارد وجاف.',
                'اذب قرص واحد في 200 مل من الماء. تناول مرة واحدة يومياً، ويفضل في الصباح على معدة فارغة.',
            ],
            'mineral' => [
                'تناول 1-2 كبسولة يومياً مع الطعام لتعزيز الامتصاص وتقليل اضطراب المعدة. يفضل تناوله مع فيتامين د.',
                'البالغون: تناول قرص واحد مرتين يومياً مع الوجبات. لا تتناوله مع منتجات الألبان أو مكملات الكالسيوم.',
                'تناول كبسولة واحدة يومياً بين الوجبات للامتصاص الأمثل. يمكن تناوله مع فيتامين سي لتعزيز الامتصاص.',
            ],
            'herbal' => [
                'تناول 1-2 كبسولة يومياً مع الماء، ويفضل مع الوجبات. استخدم بانتظام لمدة 4-6 أسابيع للحصول على أفضل النتائج.',
                'البالغون: تناول كبسولة واحدة مرتين يومياً. تناول مع الطعام في حالة حساسية المعدة. توقف عن الاستخدام في حالة حدوث ردود فعل سلبية.',
                'تناول 1-3 كبسولات يومياً حسب الحاجة. ابدأ بأقل جرعة وزد تدريجياً. غير موصى به أثناء الحمل.',
            ],
            'omega' => [
                'تناول 1-2 كبسولة جيلاتينية يومياً مع الوجبات. احفظ في الثلاجة بعد الفتح للحفاظ على النضارة والفعالية.',
                'البالغون: تناول كبسولة جيلاتينية واحدة يومياً مع الطعام. الأطفال: استشر طبيب الأطفال للجرعة المناسبة.',
                'تناول كبسولتين جيلاتينيتين يومياً مع الوجبات أو حسب توجيهات مقدم الرعاية الصحية. لا تتجاوز 3 جرام يومياً بدون إشراف طبي.',
            ],
            'probiotic' => [
                'تناول كبسولة واحدة يومياً على معدة فارغة، قبل 30 دقيقة من الوجبات. احفظ في الثلاجة للحفاظ على الفعالية.',
                'البالغون: تناول 1-2 كبسولة يومياً. تناول قبل أو بعد المضادات الحيوية بساعتين. احفظ في الثلاجة.',
                'تناول كبسولة واحدة يومياً مع أو بدون طعام. يفضل تناوله في نفس الوقت يومياً للاستمرارية.',
            ],
            'beauty' => [
                'ضع 2-3 قطرات على بشرة نظيفة وجافة. اربت بلطف حتى الامتصاص. استخدم صباحاً ومساءً. اتبع بمرطب.',
                'ضع طبقة رقيقة على الوجه والرقبة بعد التنظيف. استخدم ليلاً فقط. ابدأ 2-3 مرات أسبوعياً، وزد تدريجياً.',
                'ضع 3-5 قطرات على بشرة نظيفة. دلك بلطف حتى الامتصاص. استخدم يومياً صباحاً ومساءً.',
                'ضع كمية صغيرة على المناطق المصابة مرتين يومياً. تجنب منطقة العين. استخدم واقي الشمس نهاراً عند استخدام هذا المنتج.',
            ],
            'food' => [
                'استخدم حسب الرغبة في الطبخ أو الخبز أو كمحلي طبيعي. احفظ في مكان بارد وجاف بعيداً عن أشعة الشمس المباشرة.',
                'اشطف قبل الطبخ. استخدم كوب كينوا مع كوبين ماء. اتركه ينغلي، خفف النار، اتركه ينضج 15 دقيقة.',
                'استخدم للطبخ أو الخبز أو العناية بالبشرة. يذوب عند 24 درجة مئوية. احفظ في درجة حرارة الغرفة في العبوة الأصلية.',
                'استخدم كملح طعام أو في الطبخ. ابدأ بكميات صغيرة لأن النكهة أقوى من الملح العادي.',
            ],
        ];

        $typeKey = strtolower($productType);
        if (isset($instructions[$typeKey])) {
            return $instructions[$typeKey][array_rand($instructions[$typeKey])];
        }

        return 'اتبع التوجيهات الموجودة على العبوة أو استشر أخصائي الرعاية الصحية للاستخدام الصحيح. احفظ بعيداً عن متناول الأطفال.';
    }

    /**
     * Convert product data to use dropdown option IDs instead of string values
     */
    private function convertProductDataToDropdownIds(array $productData, array $dropdownOptions): array
    {
        // Convert storage_conditions if it exists and is a string
        if (isset($productData['storage_conditions']) && is_string($productData['storage_conditions'])) {
            $storageCondition = $productData['storage_conditions'];
            $productData['storage_conditions'] = $dropdownOptions['storage_conditions'][$storageCondition] ??
                $dropdownOptions['storage_conditions']['Store in cool, dry place'];

            // If still null, use fallback
            if ($productData['storage_conditions'] === null) {
                $productData['storage_conditions'] = $this->getFallbackDropdownOptionId('storage-conditions');
            }
        }

        // Convert country_of_origin if it exists and is a string
        if (isset($productData['country_of_origin']) && is_string($productData['country_of_origin'])) {
            $country = $productData['country_of_origin'];
            $productData['country_of_origin'] = $dropdownOptions['country_of_origin'][$country] ??
                $this->getDropdownOptionId('country-of-origin', 'United Arab Emirates');

            // If still null, use fallback
            if ($productData['country_of_origin'] === null) {
                $productData['country_of_origin'] = $this->getFallbackDropdownOptionId('country-of-origin');
            }
        }

        // Set default values for is_returnable if not set
        if (!isset($productData['is_returnable'])) {
            $productData['is_returnable'] = $dropdownOptions['is_returnable']['default'];

            // If null, use fallback
            if ($productData['is_returnable'] === null) {
                $productData['is_returnable'] = $this->getFallbackDropdownOptionId('is-returnable');
            }
        }

        // Set default values for warranty if not set
        if (!isset($productData['warranty'])) {
            $productData['warranty'] = $dropdownOptions['warranty']['no_warranty'];

            // If null, use fallback
            if ($productData['warranty'] === null) {
                $productData['warranty'] = $this->getFallbackDropdownOptionId('warranty');
            }
        }

        return $productData;
    }

    private function ensureSpecificVendorsExist(): void
    {
        $requiredVendorCodes = ['VND001', 'VND002', 'VND003'];

        foreach ($requiredVendorCodes as $code) {
            if (!Vendor::where('code', $code)->exists()) {
                $this->createVendors();
                break;
            }
        }
    }

    /**
     * Safely get required data with proper error handling
     */
    private function getRequiredData(string $vendorCode): array
    {
        $vendor = Vendor::where('code', $vendorCode)->first();
        $user = User::where('email', '<EMAIL>')->first();

        if (!$vendor) {
            throw new \Exception("Vendor with code '{$vendorCode}' not found. Please ensure vendors are properly seeded.");
        }

        if (!$user) {
            throw new \Exception("User with email '<EMAIL>' not found. Please ensure users are properly seeded.");
        }

        return [
            'vendor' => $vendor,
            'user' => $user
        ];
    }

    private function createVendors(): void
    {
         $vendors = [
        [
            'code' => 'VND001',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'HealthPlus UAE',
            'name_tl_ar' => 'هيلث بلس امارات',
            'vendor_display_name_en' => 'HealthPlus',
            'vendor_display_name_ar' => 'هيلث بلس',
            'business_type' => json_encode(['distributor']),
            'tl_license_issuing_authority' => 'Dubai Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(3),
            'tl_license_valid_till' => Carbon::now()->addYears(2),
            'tax_registration_number' => '100123456789003',
            'director_name' => 'Ahmed Al Mansouri',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971501234567',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
        [
            'code' => 'VND002',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'Beauty Emirates',
            'name_tl_ar' => 'بيوتي إمارات',
            'vendor_display_name_en' => 'Beauty Emirates',
            'vendor_display_name_ar' => 'بيوتي إمارات',
            'business_type' => json_encode(['retailer']),
            'tl_license_issuing_authority' => 'Abu Dhabi Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(2),
            'tl_license_valid_till' => Carbon::now()->addYears(3),
            'tax_registration_number' => '100123456789004',
            'director_name' => 'Fatima Al Zahra',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971507654321',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
        [
            'code' => 'VND003',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'Nutrition Hub',
            'name_tl_ar' => 'نيوتريشن هب',
            'vendor_display_name_en' => 'Nutrition Hub',
            'vendor_display_name_ar' => 'نيوتريشن هب',
            'business_type' => json_encode(['importer']),
            'tl_license_issuing_authority' => 'Sharjah Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(4),
            'tl_license_valid_till' => Carbon::now()->addYears(1),
            'tax_registration_number' => '100123456789005',
            'director_name' => 'Mohammed Al Rashid',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971509876543',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
    ];


        foreach ($vendors as $vendorData) {
            Vendor::create($vendorData);
        }
    }

    private function createHealthSupplementProducts(): void
    {
        $healthCategory = Category::where('code', 'H')->first();
        $vitaminSubcategory = Category::where('code', 'VS')->first();
        $herbsSubcategory = Category::where('code', 'HS')->first();

        if (!$healthCategory) {
            throw new \Exception("Health category with code 'H' not found. Please ensure categories are properly seeded.");
        }

        if (!$vitaminSubcategory) {
            throw new \Exception("Vitamin subcategory with code 'VS' not found. Please ensure categories are properly seeded.");
        }

        $requiredData = $this->getRequiredData('VND001');
        $vendor = $requiredData['vendor'];
        $user = $requiredData['user'];

        $brands = Brand::where('status', 'approved')->limit(3)->get();

        if ($brands->isEmpty()) {
            throw new \Exception("No approved brands found. Please ensure brands are properly seeded.");
        }

        // Get dropdown option mappings
        $dropdownOptions = $this->getDefaultDropdownOptions();

        // Get all user groups for assignment
        $allUserGroups = $this->getAllUserGroups();

        // Get product class IDs for health supplements
        $vitaminClassId = $this->getProductClassId($healthCategory->id, $vitaminSubcategory->id, 'VD');
        $multivitaminClassId = $this->getProductClassId($healthCategory->id, $vitaminSubcategory->id, 'MV');
        $mineralClassId = $this->getProductClassId($healthCategory->id, $vitaminSubcategory->id, 'MIN');
        $probioticClassId = $this->getProductClassId($healthCategory->id, $vitaminSubcategory->id, 'PRO');
        $herbsClassId = $this->getProductClassId($healthCategory->id, $herbsSubcategory->id, 'AYU');

        $healthProducts = [
            [
                'title_en' => 'Vitamin D3 5000 IU',
                'title_ar' => 'فيتامين د3 5000 وحدة دولية',
                'short_name' => 'Vitamin D3',
                'short_description_en' => 'High potency Vitamin D3 for bone health and immune support',
                'short_description_ar' => 'فيتامين د3 عالي الفعالية لصحة العظام ودعم المناعة',
                'description_en' => 'Premium Vitamin D3 supplement providing 5000 IU per capsule. Essential for calcium absorption, bone health, and immune system function. Made from high-quality cholecalciferol.',
                'description_ar' => 'مكمل فيتامين د3 الممتاز يوفر 5000 وحدة دولية لكل كبسولة. ضروري لامتصاص الكالسيوم وصحة العظام ووظيفة الجهاز المناعي.',
                'key_ingredients' => $this->generateKeyIngredients('vitamin'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('vitamin'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('vitamin'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $vitaminSubcategory->id,
                'class_id' => $vitaminClassId,
                'sub_class_id' => $vitaminClassId ? $this->getProductSubClassId($vitaminClassId, 'Vitamin D3') : null,
                'user_group_id' => $allUserGroups['unisex'], // General health supplement for all adults
                'regular_price' => 89.00,
                'offer_price' => 75.00,
                'net_weight' => 120,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 120,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegetarian' => true,
                // 'allergen_info' => 'Contains gelatin',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.5,
                'package_width' => 6.5,
                'package_height' => 12.0,
                'package_weight' => 0.15,
            ],
            [
                'title_en' => 'Omega-3 Fish Oil 1000mg',
                'title_ar' => 'زيت السمك أوميغا-3 1000 ملغ',
                'short_name' => 'Omega-3',
                'short_description_en' => 'Pure fish oil with EPA and DHA for heart and brain health',
                'short_description_ar' => 'زيت السمك النقي مع EPA و DHA لصحة القلب والدماغ',
                'description_en' => 'High-quality fish oil supplement providing 1000mg of omega-3 fatty acids per softgel. Rich in EPA and DHA to support cardiovascular health, brain function, and joint mobility.',
                'description_ar' => 'مكمل زيت السمك عالي الجودة يوفر 1000 ملغ من أحماض أوميغا-3 الدهنية لكل كبسولة جيلاتينية.',
                'key_ingredients' => $this->generateKeyIngredients('omega'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('omega'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('omega'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => Category::where('code', 'FO')->first()->id,
                'class_id' => $this->getProductClassId($healthCategory->id, Category::where('code', 'FO')->first()->id, 'OM3'),
                'sub_class_id' => null, // Will be set if subclass exists
                'user_group_id' => $allUserGroups['senior_adults'] ?? $allUserGroups['unisex'], // Heart health focus for seniors
                'regular_price' => 125.00,
                'offer_price' => 99.00,
                'net_weight' => 90,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 90,
                'country_of_origin' => 'Norway',
                'is_halal' => false,
                // 'allergen_info' => 'Contains fish',
                'storage_conditions' => 'Refrigerate after opening',
                'package_length' => 7.0,
                'package_width' => 7.0,
                'package_height' => 13.5,
                'package_weight' => 0.18,
            ],
            [
                'title_en' => 'Multivitamin Complex',
                'title_ar' => 'مجمع الفيتامينات المتعددة',
                'short_name' => 'Multivitamin',
                'short_description_en' => 'Complete daily multivitamin with essential nutrients',
                'short_description_ar' => 'فيتامينات متعددة يومية كاملة مع العناصر الغذائية الأساسية',
                'description_en' => 'Comprehensive multivitamin formula with 25+ vitamins and minerals to support overall health and wellness.',
                'description_ar' => 'تركيبة فيتامينات متعددة شاملة مع أكثر من 25 فيتامين ومعدن لدعم الصحة العامة.',
                'key_ingredients' => $this->generateKeyIngredients('vitamin'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('vitamin'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('vitamin'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $vitaminSubcategory->id,
                'class_id' => $multivitaminClassId,
                'sub_class_id' => $multivitaminClassId ? $this->getProductSubClassId($multivitaminClassId, 'Adult Multivitamin') : null,
                'user_group_id' => $allUserGroups['unisex'], // General multivitamin for all adults
                'regular_price' => 149.00,
                'offer_price' => 119.00,
                'net_weight' => 60,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 60,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.0,
                'package_weight' => 0.12,
            ],
            [
                'title_en' => 'Magnesium Glycinate 400mg',
                'title_ar' => 'مغنيسيوم جليسينات 400 ملغ',
                'short_name' => 'Magnesium',
                'short_description_en' => 'High absorption magnesium for muscle and nerve function',
                'short_description_ar' => 'مغنيسيوم عالي الامتصاص لوظائف العضلات والأعصاب',
                'description_en' => 'Chelated magnesium glycinate for superior absorption. Supports muscle relaxation, sleep quality, and nervous system health.',
                'description_ar' => 'مغنيسيوم جليسينات مخلب للامتصاص الفائق. يدعم استرخاء العضلات وجودة النوم وصحة الجهاز العصبي.',
                'key_ingredients' => $this->generateKeyIngredients('mineral'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('mineral'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('mineral'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $vitaminSubcategory->id,
                'class_id' => $mineralClassId,
                'sub_class_id' => $mineralClassId ? $this->getProductSubClassId($mineralClassId, 'Magnesium') : null,
                'user_group_id' => $allUserGroups['unisex'], // Sleep and muscle support for all adults
                'regular_price' => 95.00,
                'offer_price' => 79.00,
                'net_weight' => 90,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 90,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.5,
                'package_width' => 6.5,
                'package_height' => 12.5,
                'package_weight' => 0.16,
            ],
            [
                'title_en' => 'Probiotics 50 Billion CFU',
                'title_ar' => 'البروبيوتيك 50 مليار وحدة',
                'short_name' => 'Probiotics',
                'short_description_en' => 'Advanced probiotic formula for digestive health',
                'short_description_ar' => 'تركيبة بروبيوتيك متقدمة لصحة الجهاز الهضمي',
                'description_en' => 'Multi-strain probiotic supplement with 50 billion CFU per capsule. Supports digestive health and immune function.',
                'description_ar' => 'مكمل بروبيوتيك متعدد السلالات مع 50 مليار وحدة لكل كبسولة. يدعم صحة الجهاز الهضمي ووظيفة المناعة.',
                'key_ingredients' => $this->generateKeyIngredients('probiotic'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('probiotic'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('probiotic'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $vitaminSubcategory->id,
                'class_id' => $probioticClassId,
                'sub_class_id' => $probioticClassId ? $this->getProductSubClassId($probioticClassId, 'Multi-Strain Probiotics') : null,
                'user_group_id' => $allUserGroups['unisex'], // Digestive health for all adults
                'regular_price' => 179.00,
                'offer_price' => 149.00,
                'net_weight' => 30,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 30,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Refrigerate after opening',
                'package_length' => 5.5,
                'package_width' => 5.5,
                'package_height' => 10.0,
                'package_weight' => 0.08,
            ],
            [
                'title_en' => 'Turmeric Curcumin 1000mg',
                'title_ar' => 'كركم كركمين 1000 ملغ',
                'short_name' => 'Turmeric',
                'short_description_en' => 'Anti-inflammatory turmeric with black pepper extract',
                'short_description_ar' => 'كركم مضاد للالتهابات مع مستخلص الفلفل الأسود',
                'description_en' => 'High-potency turmeric curcumin with BioPerine for enhanced absorption. Supports joint health and reduces inflammation.',
                'description_ar' => 'كركم كركمين عالي الفعالية مع BioPerine لتعزيز الامتصاص. يدعم صحة المفاصل ويقلل الالتهاب.',
                'key_ingredients' => $this->generateKeyIngredients('herbal'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('herbal'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('herbal'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $herbsSubcategory->id,
                'class_id' => $herbsClassId,
                'sub_class_id' => $herbsClassId ? $this->getProductSubClassId($herbsClassId, 'Turmeric') : null,
                'user_group_id' => $allUserGroups['senior_adults'] ?? $allUserGroups['unisex'], // Joint health for seniors
                'regular_price' => 129.00,
                'offer_price' => 99.00,
                'net_weight' => 60,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 60,
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.5,
                'package_weight' => 0.14,
            ],
            [
                'title_en' => 'Ashwagandha Root Extract',
                'title_ar' => 'مستخلص جذر الأشواغاندا',
                'short_name' => 'Ashwagandha',
                'short_description_en' => 'Adaptogenic herb for stress relief and energy',
                'short_description_ar' => 'عشبة تكيفية لتخفيف التوتر والطاقة',
                'description_en' => 'Premium ashwagandha root extract standardized to 5% withanolides. Helps manage stress and supports energy levels.',
                'description_ar' => 'مستخلص جذر الأشواغاندا الممتاز المعياري إلى 5% ويثانوليدات. يساعد في إدارة التوتر ويدعم مستويات الطاقة.',
                'key_ingredients' => $this->generateKeyIngredients('herbal'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('herbal'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('herbal'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $herbsSubcategory->id,
                'class_id' => $herbsClassId,
                'sub_class_id' => $herbsClassId ? $this->getProductSubClassId($herbsClassId, 'Ashwagandha') : null,
                'user_group_id' => $allUserGroups['unisex'], // Stress management for all adults
                'regular_price' => 109.00,
                'offer_price' => 89.00,
                'net_weight' => 90,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 90,
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.0,
                'package_weight' => 0.13,
            ],
        ];

        foreach ($healthProducts as $index => $productData) {
            // Convert string values to dropdown option IDs
            $productData = $this->convertProductDataToDropdownIds($productData, $dropdownOptions);

            // Ensure user_group_id has a fallback if not set
            if (!isset($productData['user_group_id']) || $productData['user_group_id'] === null) {
                $productData['user_group_id'] = $this->getUserGroupWithFallback(null);
            }

            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'HP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '629' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['model_number'] = $this->generateModelNumber('HP');
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(30);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';
            $productData['supplement_image'] =  'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg';
            $product = Product::create($productData);

            // Create default product media
            $this->createProductMedia($product->id);
        }
    }

    private function createBeautyProducts(): void
    {
        $beautyCategory = Category::where('code', 'B')->first();
        $skinCareSubcategory = Category::where('code', 'SC')->first();

        if (!$beautyCategory) {
            throw new \Exception("Beauty category with code 'B' not found. Please ensure categories are properly seeded.");
        }

        if (!$skinCareSubcategory) {
            throw new \Exception("Skin care subcategory with code 'SC' not found. Please ensure categories are properly seeded.");
        }

        $requiredData = $this->getRequiredData('VND002');
        $vendor = $requiredData['vendor'];
        $user = $requiredData['user'];

        $brands = Brand::where('status', 'approved')->skip(1)->limit(2)->get();

        if ($brands->isEmpty()) {
            throw new \Exception("No approved brands found. Please ensure brands are properly seeded.");
        }

        // Get dropdown option mappings
        $dropdownOptions = $this->getDefaultDropdownOptions();

        // Get all user groups for assignment
        $allUserGroups = $this->getAllUserGroups();

        // Get product class IDs for beauty products
        $skinCareClassId = $this->getProductClassId($beautyCategory->id, $skinCareSubcategory->id, 'SC');
        $antiAgingClassId = $this->getProductClassId($beautyCategory->id, $skinCareSubcategory->id, 'AA');

        $beautyProducts = [
            [
                'title_en' => 'Hyaluronic Acid Serum',
                'title_ar' => 'سيروم حمض الهيالورونيك',
                'short_name' => 'HA Serum',
                'short_description_en' => 'Intensive hydrating serum with pure hyaluronic acid',
                'short_description_ar' => 'سيروم مرطب مكثف بحمض الهيالورونيك النقي',
                'description_en' => 'Advanced anti-aging serum with multiple molecular weights of hyaluronic acid. Provides deep hydration, plumps fine lines, and improves skin texture for a youthful glow.',
                'description_ar' => 'سيروم مضاد للشيخوخة متقدم بأوزان جزيئية متعددة من حمض الهيالورونيك.',
                'key_ingredients' => $this->generateKeyIngredients('beauty'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('beauty'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('beauty'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'class_id' => $antiAgingClassId,
                'sub_class_id' => $antiAgingClassId ? $this->getProductSubClassId($antiAgingClassId, 'Serums') : null,
                'user_group_id' => $allUserGroups['women'] ?? $allUserGroups['unisex'], // Anti-aging primarily for women
                'regular_price' => 199.00,
                'offer_price' => 159.00,
                'net_weight' => 30,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'South Korea',
                'is_vegan' => true,
                'is_vegetarian' => true,
                // 'allergen_info' => 'Fragrance-free, paraben-free',
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
                'package_length' => 4.0,
                'package_width' => 4.0,
                'package_height' => 10.5,
                'package_weight' => 0.08,
            ],
            [
                'title_en' => 'Vitamin C Brightening Serum',
                'title_ar' => 'سيروم فيتامين سي المشرق',
                'short_name' => 'Vitamin C Serum',
                'short_description_en' => 'Antioxidant serum for brighter, more even skin tone',
                'short_description_ar' => 'سيروم مضاد للأكسدة لبشرة أكثر إشراقاً وتوحيداً',
                'description_en' => '20% Vitamin C serum with hyaluronic acid and vitamin E. Brightens skin, reduces dark spots, and provides antioxidant protection.',
                'description_ar' => 'سيروم فيتامين سي 20% مع حمض الهيالورونيك وفيتامين إي. يشرق البشرة ويقلل البقع الداكنة ويوفر حماية مضادة للأكسدة.',
                'key_ingredients' => $this->generateKeyIngredients('beauty'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('beauty'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('beauty'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'class_id' => $antiAgingClassId,
                'sub_class_id' => $antiAgingClassId ? $this->getProductSubClassId($antiAgingClassId, 'Serums') : null,
                'user_group_id' => $allUserGroups['unisex'], // Brightening serum for all adults
                'regular_price' => 189.00,
                'offer_price' => 149.00,
                'net_weight' => 30,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'USA',
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
                'package_length' => 3.5,
                'package_width' => 3.5,
                'package_height' => 9.0,
                'package_weight' => 0.06,
            ],
            [
                'title_en' => 'Retinol Night Cream',
                'title_ar' => 'كريم الريتينول الليلي',
                'short_name' => 'Retinol Cream',
                'short_description_en' => 'Anti-aging night cream with pure retinol',
                'short_description_ar' => 'كريم ليلي مضاد للشيخوخة بالريتينول النقي',
                'description_en' => 'Advanced anti-aging night cream with 0.5% retinol. Reduces fine lines, improves skin texture, and promotes cell renewal.',
                'description_ar' => 'كريم ليلي متقدم مضاد للشيخوخة مع 0.5% ريتينول. يقلل الخطوط الدقيقة ويحسن ملمس البشرة ويعزز تجديد الخلايا.',
                'key_ingredients' => $this->generateKeyIngredients('beauty'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('beauty'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('beauty'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'class_id' => $antiAgingClassId,
                'sub_class_id' => $antiAgingClassId ? $this->getProductSubClassId($antiAgingClassId, 'Night Creams') : null,
                'user_group_id' => $allUserGroups['women'] ?? $allUserGroups['unisex'], // Anti-aging primarily for women
                'regular_price' => 229.00,
                'offer_price' => 189.00,
                'net_weight' => 50,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'France',
                'is_vegan' => false,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
                'package_length' => 5.0,
                'package_width' => 5.0,
                'package_height' => 6.0,
                'package_weight' => 0.12,
            ],
            [
                'title_en' => 'Niacinamide 10% + Zinc Serum',
                'title_ar' => 'سيروم نياسيناميد 10% + زنك',
                'short_name' => 'Niacinamide Serum',
                'short_description_en' => 'Pore-minimizing serum for oily and acne-prone skin',
                'short_description_ar' => 'سيروم تقليل المسام للبشرة الدهنية والمعرضة لحب الشباب',
                'description_en' => 'High-strength niacinamide serum with zinc for oil control, pore reduction, and blemish prevention.',
                'description_ar' => 'سيروم نياسيناميد عالي القوة مع الزنك للتحكم في الزيوت وتقليل المسام ومنع العيوب.',
                'key_ingredients' => $this->generateKeyIngredients('beauty'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('beauty'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('beauty'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'class_id' => $skinCareClassId,
                'sub_class_id' => $skinCareClassId ? $this->getProductSubClassId($skinCareClassId, 'Acne Treatment') : null,
                'user_group_id' => $allUserGroups['teens'] ?? $allUserGroups['unisex'], // Acne treatment for teens
                'regular_price' => 79.00,
                'offer_price' => 59.00,
                'net_weight' => 30,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'United Kingdom',
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store at room temperature',
                'package_length' => 3.0,
                'package_width' => 3.0,
                'package_height' => 8.5,
                'package_weight' => 0.05,
            ],
            [
                'title_en' => 'Argan Oil Hair Treatment',
                'title_ar' => 'علاج الشعر بزيت الأرغان',
                'short_name' => 'Argan Oil',
                'short_description_en' => 'Pure Moroccan argan oil for hair and skin',
                'short_description_ar' => 'زيت الأرغان المغربي النقي للشعر والبشرة',
                'description_en' => '100% pure, cold-pressed Moroccan argan oil. Nourishes hair, reduces frizz, and moisturizes skin naturally.',
                'description_ar' => 'زيت الأرغان المغربي النقي 100% المعصور على البارد. يغذي الشعر ويقلل التجعد ويرطب البشرة طبيعياً.',
                'key_ingredients' => $this->generateKeyIngredients('beauty'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('beauty'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('beauty'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'class_id' => $skinCareClassId,
                'sub_class_id' => $skinCareClassId ? $this->getProductSubClassId($skinCareClassId, 'Hair Care') : null,
                'user_group_id' => $allUserGroups['unisex'], // Hair care for all adults
                'regular_price' => 159.00,
                'offer_price' => 129.00,
                'net_weight' => 50,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'Turkey',
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 4.5,
                'package_width' => 4.5,
                'package_height' => 12.0,
                'package_weight' => 0.09,
            ],
        ];

        foreach ($beautyProducts as $index => $productData) {
            // Convert string values to dropdown option IDs
            $productData = $this->convertProductDataToDropdownIds($productData, $dropdownOptions);

            // Ensure user_group_id has a fallback if not set
            if (!isset($productData['user_group_id']) || $productData['user_group_id'] === null) {
                $productData['user_group_id'] = $this->getUserGroupWithFallback(null);
            }

            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'BE-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '880' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['model_number'] = $this->generateModelNumber('BT');
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(15);
            $productData['bbe_date'] = Carbon::now()->addYears(3);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';
            $productData['supplement_image'] =  'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg';
            $product = Product::create($productData);

            // Create default product media
            $this->createProductMedia($product->id);
        }
    }

    private function createFoodProducts(): void
    {
        $foodCategory = Category::where('code', 'F')->first();
        $healthFoodSubcategory = Category::where('code', 'HF')->first();

        if (!$foodCategory) {
            throw new \Exception("Food category with code 'F' not found. Please ensure categories are properly seeded.");
        }

        if (!$healthFoodSubcategory) {
            throw new \Exception("Health food subcategory with code 'HF' not found. Please ensure categories are properly seeded.");
        }

        $requiredData = $this->getRequiredData('VND003');
        $vendor = $requiredData['vendor'];
        $user = $requiredData['user'];

        $brands = Brand::where('status', 'approved')->limit(2)->get();

        if ($brands->isEmpty()) {
            throw new \Exception("No approved brands found. Please ensure brands are properly seeded.");
        }

        // Get dropdown option mappings
        $dropdownOptions = $this->getDefaultDropdownOptions();

        // Get all user groups for assignment
        $allUserGroups = $this->getAllUserGroups();

        // Get product class IDs for food products
        $organicFoodClassId = $this->getProductClassId($foodCategory->id, $healthFoodSubcategory->id, 'OF');
        $superFoodClassId = $this->getProductClassId($foodCategory->id, $healthFoodSubcategory->id, 'SF');

        $foodProducts = [
            [
                'title_en' => 'Organic Manuka Honey',
                'title_ar' => 'عسل مانوكا العضوي',
                'short_name' => 'Manuka Honey',
                'short_description_en' => 'Premium organic Manuka honey from New Zealand',
                'short_description_ar' => 'عسل مانوكا العضوي الممتاز من نيوزيلندا',
                'description_en' => 'Authentic Manuka honey with UMF 15+ rating. Raw, unprocessed honey with natural antibacterial properties. Perfect for immune support and digestive health.',
                'description_ar' => 'عسل مانوكا الأصلي بتقييم UMF 15+. عسل خام غير معالج بخصائص مضادة للبكتيريا طبيعية.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => Category::where('code', 'HS')->first()->id,
                'class_id' => $superFoodClassId,
                'sub_class_id' => $superFoodClassId ? $this->getProductSubClassId($superFoodClassId, 'Honey & Sweeteners') : null,
                'user_group_id' => $allUserGroups['unisex'], // Natural sweetener for all adults
                'regular_price' => 299.00,
                'offer_price' => 249.00,
                'net_weight' => 500,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'Australia',
                'is_halal' => true,
                'is_vegan' => false,
                'is_vegetarian' => true,
                // 'allergen_info' => 'Not suitable for children under 12 months',
                'storage_conditions' => 'Store at room temperature, avoid direct sunlight',
                'package_length' => 8.5,
                'package_width' => 8.5,
                'package_height' => 12.0,
                'package_weight' => 0.65,
            ],
            [
                'title_en' => 'Organic Quinoa',
                'title_ar' => 'الكينوا العضوية',
                'short_name' => 'Quinoa',
                'short_description_en' => 'Premium organic quinoa - complete protein superfood',
                'short_description_ar' => 'الكينوا العضوية الممتازة - سوبرفود بروتين كامل',
                'description_en' => 'Certified organic quinoa grain, a complete protein containing all nine essential amino acids. Gluten-free, high in fiber, and perfect for healthy meals.',
                'description_ar' => 'حبوب الكينوا العضوية المعتمدة، بروتين كامل يحتوي على جميع الأحماض الأمينية الأساسية التسعة.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'class_id' => $organicFoodClassId,
                'sub_class_id' => $organicFoodClassId ? $this->getProductSubClassId($organicFoodClassId, 'Grains & Cereals') : null,
                'user_group_id' => $allUserGroups['keto'] ?? $allUserGroups['unisex'], // Keto-friendly superfood
                'regular_price' => 45.00,
                'offer_price' => 39.00,
                'net_weight' => 1000,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'Canada',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                // 'allergen_info' => 'Gluten-free, may contain traces of nuts',
                'storage_conditions' => 'Store in cool, dry place in airtight container',
                'package_length' => 15.0,
                'package_width' => 10.0,
                'package_height' => 25.0,
                'package_weight' => 1.1,
            ],
            [
                'title_en' => 'Organic Coconut Oil',
                'title_ar' => 'زيت جوز الهند العضوي',
                'short_name' => 'Coconut Oil',
                'short_description_en' => 'Extra virgin organic coconut oil for cooking and beauty',
                'short_description_ar' => 'زيت جوز الهند العضوي البكر الممتاز للطبخ والجمال',
                'description_en' => 'Cold-pressed, unrefined organic coconut oil. Perfect for cooking, baking, and natural skincare routines.',
                'description_ar' => 'زيت جوز الهند العضوي المعصور على البارد وغير المكرر. مثالي للطبخ والخبز وروتين العناية الطبيعية بالبشرة.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'class_id' => $organicFoodClassId,
                'sub_class_id' => $organicFoodClassId ? $this->getProductSubClassId($organicFoodClassId, 'Oils & Vinegars') : null,
                'user_group_id' => $allUserGroups['keto'] ?? $allUserGroups['unisex'], // Keto-friendly cooking oil
                'regular_price' => 89.00,
                'offer_price' => 75.00,
                'net_weight' => 500,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store at room temperature',
                'package_length' => 8.0,
                'package_width' => 8.0,
                'package_height' => 10.0,
                'package_weight' => 0.55,
            ],
            [
                'title_en' => 'Himalayan Pink Salt',
                'title_ar' => 'ملح الهيمالايا الوردي',
                'short_name' => 'Pink Salt',
                'short_description_en' => 'Pure Himalayan pink salt with natural minerals',
                'short_description_ar' => 'ملح الهيمالايا الوردي النقي مع المعادن الطبيعية',
                'description_en' => 'Hand-mined Himalayan pink salt rich in natural minerals. Unprocessed and free from additives.',
                'description_ar' => 'ملح الهيمالايا الوردي المستخرج يدوياً والغني بالمعادن الطبيعية. غير معالج وخالي من المواد المضافة.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'class_id' => $organicFoodClassId,
                'sub_class_id' => $organicFoodClassId ? $this->getProductSubClassId($organicFoodClassId, 'Spices & Seasonings') : null,
                'user_group_id' => $allUserGroups['unisex'], // Natural seasoning for all adults
                'regular_price' => 35.00,
                'offer_price' => 29.00,
                'net_weight' => 1000,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 12.0,
                'package_width' => 8.0,
                'package_height' => 15.0,
                'package_weight' => 1.05,
            ],
            [
                'title_en' => 'Organic Chia Seeds',
                'title_ar' => 'بذور الشيا العضوية',
                'short_name' => 'Chia Seeds',
                'short_description_en' => 'Nutrient-dense organic chia seeds superfood',
                'short_description_ar' => 'بذور الشيا العضوية الغنية بالعناصر الغذائية',
                'description_en' => 'Premium organic chia seeds packed with omega-3, fiber, and protein. Perfect for smoothies, yogurt, and baking.',
                'description_ar' => 'بذور الشيا العضوية الممتازة المليئة بأوميغا-3 والألياف والبروتين. مثالية للعصائر واللبن والخبز.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'class_id' => $superFoodClassId,
                'sub_class_id' => $superFoodClassId ? $this->getProductSubClassId($superFoodClassId, 'Seeds & Nuts') : null,
                'user_group_id' => $allUserGroups['keto'] ?? $allUserGroups['unisex'], // Keto-friendly superfood
                'regular_price' => 65.00,
                'offer_price' => 55.00,
                'net_weight' => 500,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'Australia',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place in airtight container',
                'package_length' => 10.0,
                'package_width' => 7.0,
                'package_height' => 18.0,
                'package_weight' => 0.55,
            ],
            [
                'title_en' => 'Raw Almonds',
                'title_ar' => 'اللوز الخام',
                'short_name' => 'Almonds',
                'short_description_en' => 'Premium raw almonds - healthy snack and ingredient',
                'short_description_ar' => 'اللوز الخام الممتاز - وجبة خفيفة صحية ومكون',
                'description_en' => 'High-quality raw almonds, rich in healthy fats, protein, and vitamin E. Perfect for snacking or cooking.',
                'description_ar' => 'لوز خام عالي الجودة، غني بالدهون الصحية والبروتين وفيتامين إي. مثالي للوجبات الخفيفة أو الطبخ.',
                'key_ingredients' => $this->generateKeyIngredients('food'),
                'usage_instruction_en' => $this->generateUsageInstructionsEn('food'),
                'usage_instruction_ar' => $this->generateUsageInstructionsAr('food'),
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'class_id' => $superFoodClassId,
                'sub_class_id' => $superFoodClassId ? $this->getProductSubClassId($superFoodClassId, 'Seeds & Nuts') : null,
                'user_group_id' => $allUserGroups['keto'] ?? $allUserGroups['unisex'], // Keto-friendly nuts
                'regular_price' => 95.00,
                'offer_price' => 79.00,
                'net_weight' => 500,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 12.0,
                'package_width' => 8.0,
                'package_height' => 20.0,
                'package_weight' => 0.55,
            ],
        ];

        foreach ($foodProducts as $index => $productData) {
            // Convert string values to dropdown option IDs
            $productData = $this->convertProductDataToDropdownIds($productData, $dropdownOptions);

            // Ensure user_group_id has a fallback if not set
            if (!isset($productData['user_group_id']) || $productData['user_group_id'] === null) {
                $productData['user_group_id'] = $this->getUserGroupWithFallback(null);
            }

            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'FD-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '940' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['model_number'] = $this->generateModelNumber('FD');
            $productData['vat_tax'] = 'exempted'; // Food items are VAT exempt in UAE
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(20);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';
            $productData['supplement_image'] =  'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg';
            $product = Product::create($productData);

            // Create default product media
            $this->createProductMedia($product->id);
        }
    }

    private function createSportsNutritionProducts(): void
    {
        $sportsCategory = Category::where('code', 'S')->first();
        $proteinSubcategory = Category::where('code', 'PT')->first();

        if (!$sportsCategory) {
            throw new \Exception("Sports category with code 'S' not found. Please ensure categories are properly seeded.");
        }

        if (!$proteinSubcategory) {
            throw new \Exception("Protein subcategory with code 'PT' not found. Please ensure categories are properly seeded.");
        }

        $requiredData = $this->getRequiredData('VND003');
        $vendor = $requiredData['vendor'];
        $user = $requiredData['user'];

        $brands = Brand::where('status', 'approved')->skip(2)->limit(2)->get();

        if ($brands->isEmpty()) {
            throw new \Exception("No approved brands found. Please ensure brands are properly seeded.");
        }

        // Get dropdown option mappings
        $dropdownOptions = $this->getDefaultDropdownOptions();

        // Get all user groups for assignment
        $allUserGroups = $this->getAllUserGroups();

        // Get product class IDs for sports nutrition products
        $proteinClassId = $this->getProductClassId($sportsCategory->id, $proteinSubcategory->id, 'WP');
        $creatineClassId = $this->getProductClassId($sportsCategory->id, $proteinSubcategory->id, 'CR');
        $bcaaClassId = $this->getProductClassId($sportsCategory->id, $proteinSubcategory->id, 'BCAA');
        $preWorkoutClassId = $this->getProductClassId($sportsCategory->id, $proteinSubcategory->id, 'PWO');

        $sportsProducts = [
            [
                'title_en' => 'Whey Protein Isolate',
                'title_ar' => 'بروتين مصل اللبن المعزول',
                'short_name' => 'Whey Isolate',
                'short_description_en' => 'Premium whey protein isolate for muscle building',
                'short_description_ar' => 'بروتين مصل اللبن المعزول الممتاز لبناء العضلات',
                'description_en' => 'Ultra-pure whey protein isolate with 90% protein content. Fast-absorbing, low in lactose and fat. Perfect for post-workout recovery and muscle building.',
                'description_ar' => 'بروتين مصل اللبن المعزول فائق النقاء بمحتوى بروتين 90%. سريع الامتصاص، منخفض اللاكتوز والدهون.',
                'key_ingredients' => 'Whey Protein Isolate (90%), Natural Flavors, Lecithin, Stevia Extract, Digestive Enzymes',
                'usage_instruction_en' => 'Mix 1 scoop (30g) with 200ml water or milk. Take 1-2 servings daily, preferably post-workout.',
                'usage_instruction_ar' => 'اخلط 1 مغرفة (30 جرام) مع 200 مل من الماء أو الحليب. تناول 1-2 حصة يومياً، ويفضل بعد التمرين.',
                'supplement_image' => 'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'class_id' => $proteinClassId,
                'sub_class_id' => $proteinClassId ? $this->getProductSubClassId($proteinClassId, 'Whey Protein Isolate') : null,
                'user_group_id' => $allUserGroups['fitness_freeks'] ?? $allUserGroups['men'], // Fitness enthusiasts
                'regular_price' => 299.00,
                'offer_price' => 259.00,
                'net_weight' => 2000,
                'net_weight_unit_id' => $dropdownOptions['net_weight_unit'],
                'formulation_id' => $dropdownOptions['formulation'],
                'flavour_id' => $dropdownOptions['flavour'],
                'servings' => 66,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                // 'allergen_info' => 'Contains milk, may contain soy',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 20.0,
                'package_width' => 15.0,
                'package_height' => 25.0,
                'package_weight' => 2.2,
            ],
            [
                'title_en' => 'Creatine Monohydrate',
                'title_ar' => 'كرياتين مونوهيدرات',
                'short_name' => 'Creatine',
                'short_description_en' => 'Pure creatine monohydrate for strength and power',
                'short_description_ar' => 'كرياتين مونوهيدرات نقي للقوة والطاقة',
                'description_en' => 'Micronized creatine monohydrate powder. Increases muscle strength, power output, and exercise performance.',
                'description_ar' => 'مسحوق كرياتين مونوهيدرات مجهري. يزيد قوة العضلات وإنتاج الطاقة وأداء التمرين.',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'class_id' => $creatineClassId,
                'sub_class_id' => $creatineClassId ? $this->getProductSubClassId($creatineClassId, 'Creatine Monohydrate') : null,
                'user_group_id' => $allUserGroups['fitness_freeks'] ?? $allUserGroups['men'], // Fitness enthusiasts
                'regular_price' => 149.00,
                'offer_price' => 119.00,
                'net_weight' => 500,
                'net_weight_unit_id' => 4,
                'servings' => 100,
                'flavour_id' => 1,
                'country_of_origin' => 'Germany',
                'is_halal' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 12.0,
                'package_width' => 12.0,
                'package_height' => 15.0,
                'package_weight' => 0.55,
            ],
            [
                'title_en' => 'BCAA 2:1:1 Powder',
                'title_ar' => 'مسحوق BCAA 2:1:1',
                'short_name' => 'BCAA',
                'short_description_en' => 'Branched-chain amino acids for muscle recovery',
                'short_description_ar' => 'أحماض أمينية متفرعة السلسلة لاستعادة العضلات',
                'description_en' => 'Premium BCAA powder in optimal 2:1:1 ratio. Supports muscle recovery, reduces fatigue, and prevents muscle breakdown.',
                'description_ar' => 'مسحوق BCAA ممتاز بنسبة مثلى 2:1:1. يدعم استعادة العضلات ويقلل التعب ويمنع تكسير العضلات.',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'class_id' => $bcaaClassId,
                'sub_class_id' => $bcaaClassId ? $this->getProductSubClassId($bcaaClassId, 'BCAA Powder') : null,
                'user_group_id' => $allUserGroups['fitness_freeks'] ?? $allUserGroups['unisex'], // Fitness enthusiasts
                'regular_price' => 189.00,
                'offer_price' => 159.00,
                'net_weight' => 300,
                'net_weight_unit_id' => 4,
                'servings' => 30,
                'flavour_id' => 2,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 15.0,
                'package_width' => 10.0,
                'package_height' => 18.0,
                'package_weight' => 0.35,
            ],
            [
                'title_en' => 'Pre-Workout Energy Booster',
                'title_ar' => 'معزز الطاقة قبل التمرين',
                'short_name' => 'Pre-Workout',
                'short_description_en' => 'High-energy pre-workout formula with caffeine and beta-alanine',
                'short_description_ar' => 'تركيبة عالية الطاقة قبل التمرين مع الكافيين وبيتا ألانين',
                'description_en' => 'Advanced pre-workout formula with caffeine, beta-alanine, and citrulline malate. Boosts energy, focus, and endurance.',
                'description_ar' => 'تركيبة متقدمة قبل التمرين مع الكافيين وبيتا ألانين وسيترولين مالات. يعزز الطاقة والتركيز والتحمل.',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'class_id' => $preWorkoutClassId,
                'sub_class_id' => $preWorkoutClassId ? $this->getProductSubClassId($preWorkoutClassId, 'Energy Boosters') : null,
                'user_group_id' => $allUserGroups['fitness_freeks'] ?? $allUserGroups['men'], // Fitness enthusiasts
                'regular_price' => 199.00,
                'offer_price' => 169.00,
                'net_weight' => 300,
                'net_weight_unit_id' => 4,
                'servings' => 30,
                'flavour_id' => 3,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 12.0,
                'package_width' => 12.0,
                'package_height' => 16.0,
                'package_weight' => 0.35,
            ],
            [
                'title_en' => 'Mass Gainer Protein',
                'title_ar' => 'بروتين زيادة الكتلة',
                'short_name' => 'Mass Gainer',
                'short_description_en' => 'High-calorie protein powder for muscle mass gain',
                'short_description_ar' => 'مسحوق بروتين عالي السعرات لزيادة الكتلة العضلية',
                'description_en' => 'High-calorie mass gainer with whey protein, complex carbs, and healthy fats. Perfect for hard gainers and bulking phases.',
                'description_ar' => 'زيادة الكتلة عالية السعرات مع بروتين مصل اللبن والكربوهيدرات المعقدة والدهون الصحية.',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'class_id' => $proteinClassId,
                'sub_class_id' => $proteinClassId ? $this->getProductSubClassId($proteinClassId, 'Mass Gainers') : null,
                'user_group_id' => $allUserGroups['men'] ?? $allUserGroups['fitness_freeks'], // Mass gain primarily for men
                'regular_price' => 299.00,
                'offer_price' => 249.00,
                'net_weight' => 3000,
                'net_weight_unit_id' => 4,
                'servings' => 40,
                'flavour_id' => 1,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 25.0,
                'package_width' => 18.0,
                'package_height' => 30.0,
                'package_weight' => 3.2,
            ],
        ];

        foreach ($sportsProducts as $index => $productData) {
            // Convert string values to dropdown option IDs
            $productData = $this->convertProductDataToDropdownIds($productData, $dropdownOptions);

            // Ensure user_group_id has a fallback if not set
            if (!isset($productData['user_group_id']) || $productData['user_group_id'] === null) {
                $productData['user_group_id'] = $this->getUserGroupWithFallback(null);
            }

            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'SP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '756' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['model_number'] = $this->generateModelNumber('SP');
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(25);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';
            $productData['supplement_image'] =  'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg';
            $product = Product::create($productData);

            // Create default product media
            $this->createProductMedia($product->id);
        }
    }

    private function createWeightManagementProducts(): void
    {
        $weightCategory = Category::where('code', 'W')->first();
        $weightLossSubcategory = Category::where('code', 'WL')->first();

        if (!$weightCategory) {
            throw new \Exception("Weight category with code 'W' not found. Please ensure categories are properly seeded.");
        }

        if (!$weightLossSubcategory) {
            throw new \Exception("Weight loss subcategory with code 'WL' not found. Please ensure categories are properly seeded.");
        }

        $requiredData = $this->getRequiredData('VND001');
        $vendor = $requiredData['vendor'];
        $user = $requiredData['user'];

        // Get multiple brands for diversity instead of just one
        $brands = Brand::where('status', 'approved')->limit(4)->get();

        if ($brands->isEmpty()) {
            throw new \Exception("No approved brands found. Please ensure brands are properly seeded.");
        }

        // Get dropdown option mappings
        $dropdownOptions = $this->getDefaultDropdownOptions();

        // Get default user group IDs for weight management products
        $defaultUserGroups = $this->getWeightManagementUserGroups();

        // Get product class IDs for weight management products
        $weightLossClassId = $this->getProductClassId($weightCategory->id, $weightLossSubcategory->id, 'WL');
        $fatBurnerClassId = $this->getProductClassId($weightCategory->id, $weightLossSubcategory->id, 'FB');

        $weightProducts = [
            [
                'title_en' => 'Green Tea Extract',
                'title_ar' => 'مستخلص الشاي الأخضر',
                'short_name' => 'Green Tea',
                'short_description_en' => 'Natural green tea extract for weight management',
                'short_description_ar' => 'مستخلص الشاي الأخضر الطبيعي لإدارة الوزن',
                'description_en' => 'Standardized green tea extract with 50% EGCG. Supports metabolism, fat burning, and provides antioxidant benefits. Natural weight management support.',
                'description_ar' => 'مستخلص الشاي الأخضر المعياري مع 50% EGCG. يدعم الأيض وحرق الدهون ويوفر فوائد مضادات الأكسدة.',
                'category_id' => $weightCategory->id,
                'sub_category_id' => $weightLossSubcategory->id,
                'class_id' => $fatBurnerClassId,
                'sub_class_id' => $fatBurnerClassId ? $this->getProductSubClassId($fatBurnerClassId, 'Natural Fat Burners') : null,
                'user_group_id' => $defaultUserGroups['weight_watchers'] ?? $defaultUserGroups['unisex'] ?? null,
                'regular_price' => 149.00,
                'offer_price' => 119.00,
                'net_weight' => 60,
                'net_weight_unit_id' => 1,
                'servings' => 60,
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                // 'allergen_info' => 'Contains caffeine',
                'usage_instruction_en' => 'Take 1 capsule twice daily with meals',
                'usage_instruction_ar' => 'خذ كبسولة واحدة مرتين يوميًا مع الوجبات',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.0,
                'package_weight' => 0.12,
            ],
            [
                'title_en' => 'L-Carnitine Liquid',
                'title_ar' => 'إل-كارنيتين سائل',
                'short_name' => 'L-Carnitine',
                'short_description_en' => 'Liquid L-Carnitine for fat metabolism and energy',
                'short_description_ar' => 'إل-كارنيتين سائل لأيض الدهون والطاقة',
                'description_en' => 'High-potency liquid L-Carnitine supplement. Helps transport fatty acids for energy production and supports fat metabolism.',
                'description_ar' => 'مكمل إل-كارنيتين السائل عالي الفعالية. يساعد في نقل الأحماض الدهنية لإنتاج الطاقة ويدعم أيض الدهون.',
                'category_id' => $weightCategory->id,
                'sub_category_id' => $weightLossSubcategory->id,
                'class_id' => $weightLossClassId,
                'sub_class_id' => $weightLossClassId ? $this->getProductSubClassId($weightLossClassId, 'L-Carnitine') : null,
                'user_group_id' => $defaultUserGroups['fitness_freeks'] ?? $defaultUserGroups['unisex'] ?? null,
                'regular_price' => 159.00,
                'offer_price' => 129.00,
                'net_weight' => 500,
                'net_weight_unit_id' => 1,
                'servings' => 50,
                'country_of_origin' => 'Germany',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'usage_instruction_en' => 'Take 10ml before workout or meals',
                'usage_instruction_ar' => 'خذ 10 مل قبل التمرين أو الوجبات',
                'storage_conditions' => 'Refrigerate after opening',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 18.0,
                'package_weight' => 0.55,
            ],
            [
                'title_en' => 'Garcinia Cambogia Extract',
                'title_ar' => 'مستخلص الغارسينيا كامبوجيا',
                'short_name' => 'Garcinia',
                'short_description_en' => 'Natural appetite suppressant with HCA',
                'short_description_ar' => 'مثبط طبيعي للشهية مع HCA',
                'description_en' => 'Pure Garcinia Cambogia extract standardized to 60% HCA. Natural appetite suppressant and weight management support.',
                'description_ar' => 'مستخلص الغارسينيا كامبوجيا النقي المعياري إلى 60% HCA. مثبط طبيعي للشهية ودعم إدارة الوزن.',
                'category_id' => $weightCategory->id,
                'sub_category_id' => $weightLossSubcategory->id,
                'class_id' => $fatBurnerClassId,
                'sub_class_id' => $fatBurnerClassId ? $this->getProductSubClassId($fatBurnerClassId, 'Appetite Suppressants') : null,
                'user_group_id' => $defaultUserGroups['weight_watchers'] ?? $defaultUserGroups['unisex'] ?? null,
                'regular_price' => 119.00,
                'offer_price' => 95.00,
                'net_weight' => 90,
                'net_weight_unit_id' => 2,
                'servings' => 90,
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'usage_instruction_en' => 'Take 1 capsule 30 minutes before meals',
                'usage_instruction_ar' => 'خذ كبسولة واحدة قبل 30 دقيقة من الوجبات',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.5,
                'package_weight' => 0.14,
            ],
            [
                'title_en' => 'CLA Softgels',
                'title_ar' => 'كبسولات CLA الجيلاتينية',
                'short_name' => 'CLA',
                'short_description_en' => 'Conjugated Linoleic Acid for body composition',
                'short_description_ar' => 'حمض اللينوليك المترافق لتركيب الجسم',
                'description_en' => 'High-potency CLA softgels from safflower oil. Supports lean muscle mass and healthy body composition.',
                'description_ar' => 'كبسولات CLA عالية الفعالية من زيت القرطم. يدعم الكتلة العضلية الخالية من الدهون وتركيب الجسم الصحي.',
                'category_id' => $weightCategory->id,
                'sub_category_id' => $weightLossSubcategory->id,
                'class_id' => $weightLossClassId,
                'sub_class_id' => $weightLossClassId ? $this->getProductSubClassId($weightLossClassId, 'CLA') : null,
                'user_group_id' => $defaultUserGroups['fitness_freeks'] ?? $defaultUserGroups['unisex'] ?? null,
                'regular_price' => 139.00,
                'offer_price' => 115.00,
                'net_weight' => 120,
                'net_weight_unit_id' => 2,
                'servings' => 120,
                'country_of_origin' => 'USA',
                'is_halal' => false,
                'is_vegan' => false,
                'is_vegetarian' => true,
                'usage_instruction_en' => 'Take 2 capsules with meals, 3 times daily',
                'usage_instruction_ar' => 'خذ كبسولتين مع الوجبات، 3 مرات يوميًا',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 7.0,
                'package_width' => 7.0,
                'package_height' => 13.0,
                'package_weight' => 0.18,
            ],
        ];

        foreach ($weightProducts as $index => $productData) {
            // Convert string values to dropdown option IDs
            $productData = $this->convertProductDataToDropdownIds($productData, $dropdownOptions);

            // Ensure user_group_id has a fallback if not set
            if (!isset($productData['user_group_id']) || $productData['user_group_id'] === null) {
                $productData['user_group_id'] = $defaultUserGroups['weight_watchers'] ?? $defaultUserGroups['unisex'] ?? null;
            }

            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'WM-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '812' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['model_number'] = $this->generateModelNumber('WM');
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(35);
            $productData['bbe_date'] = Carbon::now()->addYears(3);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';
            $productData['supplement_image'] =  'uploads/358c5c13-57a8-4409-8426-0b3a6c84db32.jpeg';
            $product = Product::create($productData);

            // Create default product media
            $this->createProductMedia($product->id);
        }
    }
}
