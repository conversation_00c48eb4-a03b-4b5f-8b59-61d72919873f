<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use Illuminate\Database\Seeder;

class WarehouseSeeder extends Seeder
{
    public function run(): void
    {

        $majorWarehouses = [
            [
                'name_en' => 'Dubai South Logistics District - Main Hub',
                'name_ar'=> 'مخزن دبي الجنوبي - المركز الرئيسي',
                'code' => 'WH-DSL001',
                'address' => 'Dubai South Logistics District, Al Maktoum International Airport, Dubai, UAE, P.O. Box 390667',
                'location' => '24.8956,55.1614',
                'contact_person' => 'Ahmed Al Mansouri',
                'contact_number' => '+971-04-2345678',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => true,
                
            ],
            [
                'name_en' => 'Jebel Ali Free Zone - Distribution Center',
                'name_ar'=> 'مخزن جبل علي - مركز التوزيع',
                'code' => 'WH-JAFZ001',
                'address' => 'Jebel Ali Free Zone, Dubai, UAE, P.O. Box 17000',
                'location' => '25.0118,55.1370',
                'contact_person' => '<PERSON> bin <PERSON>',
                'contact_number' => '+971-04-8817777',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => true,
            ],
            [
                'name_en' => 'Abu Dhabi Industrial City - Fulfillment Center',
                'name_ar'=> 'مخزن أبو ظبي الصناعي - مركز التوزيع',
                'code' => 'WH-ADIC001',
                'address' => 'Abu Dhabi Industrial City, Musaffah, Abu Dhabi, UAE, P.O. Box 54477',
                'location' => '24.3567,54.5047',
                'contact_person' => 'Fatima Al Zahra',
                'contact_number' => '+971-02-5551234',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => true,
            ],
            [
                'name_en' => 'Sharjah Industrial Area - Cold Storage',
                'name_ar'=> 'مخزن شرقاء الصناعي - مركز التوزيع',
                'code' => 'WH-SIA001',
                'address' => 'Industrial Area 1, Sharjah, UAE, P.O. Box 1234',
                'location' => '25.3373,55.4239',
                'contact_person' => 'Omar Al Qasimi',
                'contact_number' => '+971-06-5339876',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
            ],
            [
                'name_en' => 'RAK Industrial Zone - Electronics Hub',
                'name_ar'=> 'مخزن رأس الخيمة الصناعي - مركز التوزيع',
                'code' => 'WH-RAK001',
                'address' => 'Ras Al Khaimah Industrial Zone, RAK, UAE, P.O. Box 10001',
                'location' => '25.7889,55.9643',
                'contact_person' => 'Khalid Al Qasimi',
                'contact_number' => '+971-07-2445566',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => false,
            ],
            [
                'name_en' => 'Ajman Free Zone - Textile Warehouse',
                'name_ar'=> 'مخزن أجمان الحرة - مركز التوزيع',
                'code' => 'WH-AFZ001',
                'address' => 'Ajman Free Zone, Ajman, UAE, P.O. Box 5555',
                'location' => '25.4052,55.5136',
                'contact_person' => 'Mariam Al Nuaimi',
                'contact_number' => '+971-06-7428888',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => false,
            ],
           
        ];

        foreach ($majorWarehouses as $warehouseData) {
            Warehouse::create($warehouseData);
        }

        $specializedWarehouses = [
            [
                'name_en' => 'Dubai Food Processing Zone - Halal Food Storage',
                'name_ar'=> 'مخزن دبي الغذائي - مركز التوزيع',
                'code' => 'WH-HALAL001',
                'address' => 'Dubai Food Processing Zone, Dubai, UAE',
                'location' => '25.0424,55.1727',
                'contact_person' => 'Ibrahim Al Hashimi',
                'contact_number' => '+971-04-2234567',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => false,
            ],
            [
                'name_en' => 'Dubai Flower Centre - Perishables Hub',
                'name_ar'=> 'مخزن دبي الزهور - مركز التوزيع',
                'code' => 'WH-FLOWER001',
                'address' => 'Dubai Flower Centre, Dubai, UAE',
                'location' => '25.2048,55.2708',
                'contact_person' => 'Noor Al Zaabi',
                'contact_number' => '+971-04-3456789',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_active' => true,
                'is_global' => false,
            ],
        ];

        foreach ($specializedWarehouses as $warehouseData) {
            Warehouse::create($warehouseData);
        }
    }
}
