<?php

namespace Database\Seeders;

use App\Models\OfferAndDeal;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class OfferAndDealSeeder extends Seeder
{
    public function run(): void
    {
        OfferAndDeal::truncate(); // optional: clears old data

        $offers = [
            [
                'title_en' => 'Summer Deal',
                'title_ar' => 'عرض الصيف',
                'description_en' => 'Get 50% off on all items!',
                'description_ar' => 'احصل على خصم 50% على جميع العناصر!',
                'image' => 'uploads/9d29ae33-a70b-4481-a39b-b582cfd24ecb.png',
                'link' => 'https://example.com/deal/summer',
                'type' => 'product',
                'regular_price' => 1000,
                'offer_price' => 500,
                'discount_percentage' => 50,
                'start_time' => Carbon::now()->subDays(1),
                'end_time' => Carbon::now()->addDays(5),
                'is_active' => true,
            ],
            [
                'title_en' => 'Flash Sale',
                'title_ar' => 'عرض فلاش',
                'description_en' => 'Limited time flash sale!',
                'description_ar' => 'عرض فلاش لفترة محدودة!',
                'image' => 'uploads/4876ec82-a12d-4b22-8f65-64ad8abf0df3.png',
                'link' => 'https://example.com/deal/flash',
                'type' => 'shipping',
                'regular_price' => 2000,
                'offer_price' => 1600,
                'discount_percentage' => 20,
                'start_time' => Carbon::now(),
                'end_time' => Carbon::now()->addHours(12),
                'is_active' => true,
            ],
            [
                'title_en' => 'Winter Sale',
                'title_ar' => 'عرض الشتاء',
                'description_en' => 'Get ready for the winter season with our exclusive deals!',
                'description_ar' => 'استعد لموسم الشتاء مع عروضنا الحصرية!',
                'image' => 'uploads/34d37824-2586-48d7-8365-c6b4dba978d0.png',
                'link' => 'https://example.com/deal/winter',
                'type' => 'product',
                'regular_price' => 1500,
                'offer_price' => 1200,
                'discount_percentage' => 20,
                'start_time' => Carbon::now(),
                'end_time' => Carbon::now()->addDays(10),
                'is_active' => true,
            ],
            [
                'title_en' => 'New Year Offer',
                'title_ar' => 'عرض رأس السنة',
                'description_en' => 'Celebrate the New Year with amazing discounts!',
                'description_ar' => 'احتفل برأس السنة مع خصومات مذهلة!',
                'image' => 'uploads/aff0492f-bedc-40c4-86c8-e95c0222ae27.png',
                'link' => 'https://example.com/deal/newyear',
                'type' => 'shipping',
                'regular_price' => 2500,
                'offer_price' => 2000,
                'discount_percentage' => 20,
                'start_time' => Carbon::now()->subDays(2),
                'end_time' => Carbon::now()->addDays(8),
                'is_active' => true,
            ],


        ];

        OfferAndDeal::insert($offers);
    }
}
