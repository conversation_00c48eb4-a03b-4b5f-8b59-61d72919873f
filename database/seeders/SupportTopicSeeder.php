<?php

namespace Database\Seeders;

use App\Models\SupportCategory;
use App\Models\SupportTopic;
use App\Models\User;
use Illuminate\Database\Seeder;

class SupportTopicSeeder extends Seeder
{
    public function run(): void
    {
        // Get or create an admin user for the topics
        $adminUser = null;

        // Try to get an admin user if the role exists
        try {
            $adminUser = User::role('admin')->first();
        } catch (\Exception $e) {
            // Role doesn't exist, continue without role check
        }

        // If no admin user exists, create a default system user
        if (!$adminUser) {
            $adminUser = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'System Admin',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );
        }

        // Define topics for each category
        $categoryTopics = [
            'Account & Profile' => [
                'Profile Information Update',
                'Password Reset',
                'Email Verification',
                'Account Deletion Request',
                'Profile Picture Issues',
                'Contact Information Change'
            ],
            
            'Login & Authentication' => [
                'Cannot Login',
                'Forgot Password',
                'Two-Factor Authentication',
                'Account Locked',
                'Social Media Login Issues',
                'Session Timeout Problems'
            ],
            
            'Payment & Billing' => [
                'Payment Failed',
                'Credit Card Issues',
                'Invoice Questions',
                'Billing Address Update',
                'Payment Method Change',
                'Transaction History',
                'Refund Status'
            ],
            
            'Technical Issues' => [
                'Website Not Loading',
                'Page Errors',
                'Browser Compatibility',
                'Search Function Problems',
                'Filter Not Working',
                'Slow Loading Times'
            ],
            
            'Order Management' => [
                'Order Status Inquiry',
                'Order Modification',
                'Order History',
                'Duplicate Orders',
                'Order Confirmation Issues',
                'Order Tracking Problems'
            ],
            
            'Refunds & Returns' => [
                'Return Request',
                'Refund Processing Time',
                'Return Policy Questions',
                'Damaged Item Return',
                'Wrong Item Received',
                'Refund Amount Dispute'
            ],
            
            'Shipping & Delivery' => [
                'Delivery Delay',
                'Wrong Delivery Address',
                'Package Not Delivered',
                'Delivery Time Slot',
                'Shipping Cost Questions',
                'Express Delivery Options'
            ],
            
            'Product Quality' => [
                'Defective Product',
                'Product Not as Described',
                'Missing Parts',
                'Quality Concerns',
                'Product Warranty',
                'Manufacturing Defects'
            ],
            
            'Product Information' => [
                'Product Specifications',
                'Product Availability',
                'Product Images',
                'Product Comparison',
                'Product Reviews',
                'Product Documentation'
            ],
            
            'Vendor Communication' => [
                'Vendor Response Time',
                'Vendor Contact Information',
                'Vendor Policies',
                'Vendor Rating Issues',
                'Vendor Dispute',
                'Vendor Feedback'
            ],
            
            'Vendor Onboarding' => [
                'Registration Process',
                'Document Verification',
                'Account Approval Status',
                'Seller Agreement',
                'Commission Structure',
                'Getting Started Guide'
            ],
            
            'Commission & Fees' => [
                'Commission Calculation',
                'Fee Structure',
                'Payment Schedule',
                'Commission Disputes',
                'Fee Deductions',
                'Payout Issues'
            ],
            
            'Marketplace Policies' => [
                'Terms of Service',
                'Privacy Policy',
                'Seller Guidelines',
                'Prohibited Items',
                'Content Guidelines',
                'Dispute Resolution'
            ],
            
            'General Inquiry' => [
                'How to Use Platform',
                'Feature Information',
                'Service Availability',
                'Contact Information',
                'Business Hours',
                'General Questions'
            ],
            
            'Complaint Resolution' => [
                'Service Complaint',
                'Product Complaint',
                'Vendor Complaint',
                'Platform Issues',
                'Customer Service',
                'Escalation Request'
            ],
            
            'Logistics & Warehousing' => [
                'Warehouse Location',
                'Storage Issues',
                'Inventory Management',
                'Packaging Problems',
                'Handling Instructions',
                'Storage Conditions'
            ],
            
            'Delivery Tracking' => [
                'Tracking Number Issues',
                'Real-time Updates',
                'Delivery Status',
                'GPS Tracking',
                'Delivery Notifications',
                'Tracking History'
            ],
            
            'Security Concerns' => [
                'Account Security',
                'Suspicious Activity',
                'Data Protection',
                'Unauthorized Access',
                'Security Breach',
                'Password Security'
            ],
            
            'Mobile App Issues' => [
                'App Crashes',
                'Login Problems',
                'Feature Not Working',
                'App Updates',
                'Performance Issues',
                'Compatibility Problems'
            ],
            
            'Business Partnership' => [
                'Partnership Opportunities',
                'Bulk Pricing',
                'Corporate Accounts',
                'API Integration',
                'White Label Solutions',
                'Custom Solutions'
            ],
            
            'Language Support' => [
                'Arabic Translation',
                'English Support',
                'Multi-language Issues',
                'Content Translation',
                'Language Preferences',
                'Regional Content'
            ],
            
            'Urgent Issues' => [
                'Critical System Error',
                'Payment Emergency',
                'Security Incident',
                'Data Loss',
                'Service Outage',
                'Emergency Contact'
            ]
        ];

        // Create topics for each category
        foreach ($categoryTopics as $categoryName => $topics) {
            $category = SupportCategory::where('name_en', $categoryName)->first();

            if ($category) {
                foreach ($topics as $topicName) {
                    SupportTopic::create([
                        'user_id' => $adminUser->id,
                        'category_id' => $category->id,
                        'name_en' => $topicName,
                        'name_ar' => $topicName, // Auto-populate with English for now
                        'status' => 'active'
                    ]);
                }
            }
        }

        // Create some inactive topics for testing
        $firstCategory = SupportCategory::first();
        if ($firstCategory) {
            SupportTopic::create([
                'user_id' => $adminUser->id,
                'category_id' => $firstCategory->id,
                'name_en' => 'Deprecated Topic',
                'name_ar' => 'موضوع مهجور',
                'status' => 'inactive'
            ]);
        }
    }
}
