<?php

namespace Database\Seeders;

use App\Models\Herb;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(RolePermissionSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(AuthClient::class);
        $this->call(DropdownSeeder::class);
        $this->call(BannerSeeder::class);

        // Category hierarchy must be seeded before ProductClass
        $this->call(CategorySeeder::class);
        $this->call(ProductClassSeeder::class);

        $this->call(AttributeSeeder::class);
    
        $this->call(BrandSeeder::class);
        $this->call(WarehouseSeeder::class);
        $this->call(VendorSeeder::class);
        $this->call(ProductSeeder::class);

        // Support System Seeders
        $this->call(SupportCategorySeeder::class);
        $this->call(SupportTopicSeeder::class);
        $this->call(SupportReasonSeeder::class);

        // Fulfilment Seeder
        $this->call(FulfilmentSeeder::class);

        // Banner Seeder
    
        $this->call(OfferAndDealSeeder::class);
    }
}
