# Multi Vendor E-Commerce API

A RESTful API built with Laravel 12 to power a multivendor e-commerce platform.

## Prerequisites

- PHP ≥ 8.2
- Composer
- PostgreSQL
- Redis (optional, for cache/queues)
- Node.js & NPM (optional, for frontend scaffolding)

## Installation



# 1. Clone the repository
```
<NAME_EMAIL>:bacbonit/uae-ecommerce-backend.git
cd uae-ecommerce-backend
```

# 2. Install PHP dependencies
```
composer install
```

# 3. Copy and configure environment file
```
cp .env.example .env
# └── Edit .env:
#     DB_CONNECTION=pgsql
#     DB_HOST=127.0.0.1
#     DB_PORT=5432
#     DB_DATABASE=your_database
#     DB_USERNAME=your_username
#     DB_PASSWORD=your_password
```

# 4. Generate application key
```
php artisan key:generate
```
# 5. Run database migrations & seeders
```
php artisan migrate --seed
```

# 6. (Optional) Install and build frontend assets
```
npm install
npm run dev
```

# 7. Serve the application
```
php artisan serve
```

API Documentation
Full API documentation is available via Swagger:
```
http://127.0.0.1:8000/swagger/documentation
```
